apiVersion: apps/v1
kind: Deployment
metadata:
  name: invoice-statement
  namespace: eo-sandbox
spec:
  replicas: 1
  selector:
    matchLabels:
      app: invoice-statement
  template:
    metadata:
      labels:
        app: invoice-statement
      annotations:
        gke-gcsfuse/volumes: "true" # required
    spec:
      serviceAccountName: nt-eo-gcp-cs
      nodeSelector:
        agentpool: sandbox
      containers:
        - name: invoice-statement
          image: northamerica-northeast2-docker.pkg.dev/nt-eo-gb/nt-eo-repo/eo-invoice-statement-service:latest
          imagePullPolicy: Always
          env:
            - name: NODE_ENV
              value: sandbox
          ports:
            - containerPort: 3030
          volumeMounts:
            - name: files
              mountPath: /home/<USER>/work/upload
              # subPath: bkp/upload
      volumes:
        - name: files
          persistentVolumeClaim:
            claimName: eo-files-sandbox-pvc-bkp

