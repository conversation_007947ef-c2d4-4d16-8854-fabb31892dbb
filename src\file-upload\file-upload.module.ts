import { Module } from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { FileUploadController } from './file-upload.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {FileUpload} from "./entities/file-upload.entity";
import { CsvModule } from 'nest-csv-parser'
import {OcrEsModule} from "../ocr-es/ocr-es.module";
import {EsModule} from "../es/es.module";
import {ConfigModule} from "@nestjs/config";
import {OcrInvoiceModule} from "../ocr-invoice/ocr-invoice.module";
import {OcrUtilsModule} from "../ocr-utils/ocr-utils.module";
import {Ap} from "../ap/entities/ap.entity";
import {HttpModule} from "@nestjs/axios";

@Module({
  imports: [
      TypeOrmModule.forFeature([FileUpload, Ap]),
      CsvModule,
      OcrEsModule,
      OcrInvoiceModule,
      OcrUtilsModule,
      HttpModule,
      ConfigModule,
      EsModule,
  ],
  controllers: [FileUploadController],
  providers: [FileUploadService],
  exports:[FileUploadService]
})
export class FileUploadModule {}
