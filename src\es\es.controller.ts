import {Body, Controller, Delete, Get, HttpException, Post, Query} from '@nestjs/common';
import {EsService} from './es.service';
import {Es} from "./entities/es.entity";
import {BaseController} from "../common/controller/base.controller";
import {ApiOperation, ApiResponse, ApiTags} from "@nestjs/swagger";
import {Page, Paginated} from "../common/utils/index.util";

@ApiTags('ES')
@Controller('es')
export class EsController extends BaseController<Es>{
  constructor(private readonly esService: EsService) {
    super(esService);
  }

  @Get()
  @ApiOperation({summary: 'Find all on paging'})
  @ApiResponse({ status: 200, description: 'Ok' })
  async findAll(@Query() params: Page): Promise<Es[] | Paginated<Es>>{
    return super.findAll(params);
  }

  @Post()
  @ApiOperation({summary: 'Save entity'})
  @ApiResponse({
    status: 201,
    description: 'The record has been successfully created.',
  })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async create(@Body() entity: Es): Promise<Es> {
    return super.create(entity);
  }

  @Delete(':id')
  @ApiOperation({summary: 'Delete by id'})
  @ApiResponse({ status: 200, description: 'Entity deleted successfully.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async delete(id: number): Promise<any> {
    const es = await this.findById(id);
    if(es.br_flag !== 0) {
      throw new HttpException('The es has been reconciled and cannot be deleted.', 500);
    }
    return super.delete(id);
  }
}
