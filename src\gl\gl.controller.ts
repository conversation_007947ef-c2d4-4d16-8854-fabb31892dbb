import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Logger,
  Query,
  UseInterceptors,
  UploadedFile
} from '@nestjs/common';
import { GlService } from './gl.service';
import { CreateGlDto } from './dto/create-gl.dto';
import { UpdateGlDto } from './dto/update-gl.dto';
import {ApiOperation, ApiTags} from "@nestjs/swagger";
import {Gl} from "./entities/gl.entity";
import {FileInterceptor} from "@nestjs/platform-express";
import { Express } from 'express';
import { createWriteStream } from 'fs';
import { join } from 'path';


@ApiTags('General Entry')
@Controller('gl')
export class GlController {
  constructor(private readonly glService: GlService) {}
  // set logger
  logger = new Logger(GlController.name);


  @Post()
  @ApiOperation({
    summary: 'create a new general entry',
  })
  create(@Body() createGlDto: CreateGlDto) {
    return this.glService.create(createGlDto);
  }

  @Get()
  @ApiOperation({
    summary: 'get GL list',
  })
  async findAll(@Query() queryData) {
    return this.glService.findAll(queryData);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'get general entry detail',
  })
  findOne(@Param('id') id: string) {
    return this.glService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'update general entry data',
  })
  update(@Param('id') id: string, @Body() updateGlDto: UpdateGlDto) {
    return this.glService.update(+id, updateGlDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'delete a general entry',
  })
  remove(@Param('id') id: string) {
    return this.glService.remove(+id);
  }
}
