import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class CompanyBankResponseDto {

    @ApiProperty({description: 'company code', example: '3000'})
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({description: 'bank account', example: '123-12312-********'})
    account_no: string;

    @ApiProperty({description: 'bank org id', example: '123-12312-312'})
    bank_org_id: string;

    @ApiProperty({description: 'bank payer name'})
    bank_payer_name: string;

    @ApiProperty({description: 'bank type', example: 'Cheque'})
    bank_type: string;

    @ApiProperty({description: 'bank account', example: '12312'})
    branch_no: string;

    @ApiProperty({description: 'bank code', example: 'TD'})
    code: string;

    @ApiProperty({description: 'currency', example: '1 - CNY; 2 - CAD; 3 - USD'})
    currency: string;

    @ApiProperty({description: 'gl account', example: '1002-3002-01'})
    gl_account: string;

    @ApiProperty({description: 'id', example: '111'})
    id: number;

    @ApiProperty({description: 'bank name', example: 'Bank of Montreal'})
    name: string

    @ApiProperty({description: 'bank processing center', example: '12312'})
    processing_center: string;

    @ApiProperty({description: 'swift'})
    swift: string;

    @ApiProperty({description: 'bank account', example: '0 initialization 、 1 success  、 2 failed'})
     plaid_status: number;

}
