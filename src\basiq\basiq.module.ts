import { Modu<PERSON> } from '@nestjs/common';
import { BasiqService } from './basiq.service';
import { BasiqController } from './basiq.controller';
import { EsModule } from 'src/es/es.module';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Basiq } from './entities/basiq.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Basiq]), EsModule, HttpModule, ConfigModule],
  controllers: [BasiqController],
  providers: [BasiqService]
})
export class BasiqModule {}
