import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {ReconcileSubmitDto} from "../dto/reconcile_submit.dto";
import {format, parseISO} from "date-fns";
import {Es} from "../../es/entities/es.entity";
import {EntityManager} from "typeorm";
import {EsReconcileService} from "../es_reconcile.service";
import {EsReconcileReverseService} from "../es_reconcile_reverse/es_reconcile_reverse.service";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {EnumUtil} from "../../common/enum/enum.util";

export class FTType extends BkTypeStrategyService {
    /**
     * 自动匹配，es对es， 同币种，不同银行账号，withdrawal找deposit相等的值， deposit找withdrawal相等的值，只返回1条
     * @param params
     * @param es
     * @param usedMap
     */
    public async find(params, es, usedMap): Promise<MatchDto[]> {
        params.bank_account = es.bank_account;

        const rest: MatchDto[] = (await this.findEsForBrMatch(params))
            .filter(item => (es.withdrawal !== 0 ? new Decimal(es.balance).comparedTo(item.deposit) === 0 : new Decimal(es.balance).comparedTo(item.withdrawal) === 0)
                && item.currency === es.currency
                && item.bank_account !== es.bank_account
                && !usedMap.has(item.id + 'es'))
            .map(item => this.convertEsToMatchDto(item))
            .slice(0, 1);
        if (rest.length > 0) rest.forEach(e => usedMap.set(e.id + 'es', e));
        return rest;
    }

    /**
     * 手动匹配，es对es，同币种，不同银行账号， withdrawal找deposit相等的值， deposit找withdrawal相等的值
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: Es): Promise<MatchDto[]> {
        return (await this.findEsForBrMatch(params))
            .filter(item => (es.withdrawal !== 0 ? new Decimal(es.balance).comparedTo(item.deposit) === 0 : new Decimal(es.balance).comparedTo(item.withdrawal) === 0)
                && item.currency === es.currency
                && item.bank_account !== es.bank_account
            )
            .map(item => this.convertEsToMatchDto(item))
    }

    /**
     * reverse ft type in history
     * @param esHistory
     * @param invoiceHistoryList
     * @param queryRunner
     */
    async reverse(esHistory: ReconciliationHistory, invoiceHistoryList: ReconciliationHistory[], queryRunner: QueryRunner) {
        let esNewBalance = new Decimal(esHistory.entity.balance);
        for (let invoiceHistory of invoiceHistoryList) {
            esNewBalance = esNewBalance.add(new Decimal(invoiceHistory.reconcile_amount).abs());
            await this.reverseEs(invoiceHistory, esHistory, queryRunner);
        }
        return esNewBalance;
    }

    public buildDataForEngine(submitDto: ReconcileSubmitDto, invoice_list_to_engine: any[], newEsBalance: Decimal): any {
        return {
            "creator": submitDto.creator,
            "company_code": submitDto.company_code,
            "posting_date": format(parseISO(submitDto.posting_date), 'yyyy-MM-dd'),
            "post_check": true,
            "currency": "CAD",
            "header_text": 'reconcile submit',
            "ref_document": submitDto.statement_id,
            "line_items": invoice_list_to_engine
        }
    }

    /**
     * update es
     * @param invoice
     * @param transactionalEntityManager
     * @param service
     */
    async processInvoices(invoice: ReconcileSubmitDto.Invoice, transactionalEntityManager: EntityManager, service: EsReconcileService): Promise<{ newBalance: Decimal; brFlag: number }> {
        // return service.updateInvoice(Es, transactionalEntityManager, invoice);
        const findOne = await service.findInvoice(Es, transactionalEntityManager, invoice);
        const newBalance = new Decimal(findOne.balance).sub(new Decimal(invoice.reconcile_amount));
        const brFlag = service.getBrFlag(newBalance, findOne);
        // update invoice
        await transactionalEntityManager.update(Es, {id: findOne.id}, {
            br_flag: brFlag,
            balance: newBalance.toNumber(),
            br_type: parseInt(BkTypeEnum.FT),
            report_type: EnumUtil.getEnumKey(BkTypeEnum.FT, BkTypeEnum)
        });
        return {newBalance, brFlag};
    }

    async buildInvoiceDataToEngin(es: Es, invoice_list_to_engine: any[], submitDto: ReconcileSubmitDto, invoice: ReconcileSubmitDto.Invoice) {
        invoice_list_to_engine.push({"gl_account": submitDto.gl_account, "amount_tc": es.balance, "dr_cr": es.withdrawal === 0 ? "dr" : "cr", "neg_posting": false});
        invoice_list_to_engine.push({"gl_account": invoice.gl_account, "amount_tc": invoice.balance, "dr_cr": es.withdrawal === 0 ? "cr" : "dr", "neg_posting": false});
    }

    async generateDataToEngine(submitDto: ReconcileSubmitDto, invoice_list_to_engine: any[], newEsBalance: Decimal, service: EsReconcileService) {
        const enginePostInvoiceUrl = service.configService.get('ENGINE_BASE_URL') + '/post-journal-entry';
        const data = this.buildDataForEngine(submitDto, invoice_list_to_engine, newEsBalance);
        return {url: enginePostInvoiceUrl, data}
    }

    public async sendReverseEngine(esHistory: ReconciliationHistory, queryRunner: QueryRunner, service: EsReconcileReverseService, creator: number) {
        await this.sendESReverseEngine(esHistory , queryRunner, service, creator)
    }

}