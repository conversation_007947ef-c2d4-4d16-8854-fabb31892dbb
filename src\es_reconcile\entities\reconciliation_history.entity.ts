import {BaseEntity} from "../../common/entity/base.entity";
import {Column, Entity, ObjectLiteral} from "typeorm";
import {Es} from "../../es/entities/es.entity";
import {Ap} from "../../ap/entities/ap.entity";
import {Ar} from "../../ar/entities/ar.entity";

@Entity("reconciliation_history_v2")
export class ReconciliationHistory extends BaseEntity {

    // @Column({name: 'company_id', type: 'int', comment: 'company id'})
    // company_id: number;

    @Column({name: 'company_code', type: 'varchar', comment: 'company code'})
    company_code: string;

    @Column({name: 'br_id', type: 'int', comment: 'statement/invoice id'})
    br_id: number;

    @Column({name: 'reconcile_amount', type: 'decimal', precision: 10, scale: 2, default: 0.00, comment: 'reconcile amount'})
    reconcile_amount: number;

    @Column({name: 'posting_date', type: 'varchar', length: 10, comment: 'posting date'})
    posting_date: string;

    @Column({name: 'transaction_id', type: 'varchar', length: 50, comment: 'reconcile transaction uuid'})
    transaction_id: string;

    @Column({name: 'br_type', nullable: true, type: 'tinyint', comment: '0 AR/RS; 1 AP/RP; 2 PR; 3 SR; 4 FT; 5 PY; 6 FX； 7 ES; 8 YED'})
    br_type: number;

    @Column({name: 'br_entity_type', nullable: true, type: 'tinyint', comment: '0 AR ; 1 AP; 7 ES'})
    br_entity_type: number;

    @Column({name: 'before_balance', type: 'decimal', precision: 10, scale: 2, default: 0.00, comment: 'balance before reconcile'})
    before_balance: number;

    @Column({name: 'after_balance', type: 'decimal', precision: 10, scale: 2, default: 0.00, comment: 'balance after reconcile'})
    after_balance: number;

    @Column({name: 'after_reverse_balance', type: 'decimal', precision: 10, scale: 2, default: 0.00, comment: 'balance after reverse'})
    after_reverse_balance: number;

    @Column({name: 'after_br_flag', type: 'int', comment: 'br_flag after reconcile: 0 not paid; 1 partial paid; 2 paid; 3 - reversed'})
    after_br_flag: number;

    @Column({name: 'target', type: 'int', comment: 'target: 0: reconcile,  1: reconciled'})
    target: number;

    @Column({name: 'reverse_number', type: 'int', default:0, comment: 'the number of reversed'})
    reverse_number: number;

    @Column({name: 'max_reverse_number', type: 'int', default:0, comment: 'the maximum number can be reversed'})
    max_reverse_number: number;

    @Column({name: 'document_no', type: 'varchar', length: 50, default: null, comment: 'document no return from engine for reconciliation'})
    document_no: string;

    @Column({name: 'reverse_document_no', type: 'varchar', length: 50, default: null, comment: 'document no return from engine for reverse'})
    reverse_document_no: string;

    @Column({name: 'send_sap_status', default: 0, comment: 'send sap status: 0 - not sent;1 - sending;2 - sent successfully;3 - sent fail'})
    send_sap_status: number;

    @Column({name: 'sap_document_no', type: 'varchar', length: 50, default: null, comment: 'sap document no return from engine for reconciliation'})
    sap_document_no: string;

    @Column({name: 'sap_reverse_document_no', type: 'varchar', length: 50, default: null, comment: 'sap document no return from engine for reverse'})
    sap_reverse_document_no: string;

    @Column({name: 'sap_message', type: 'varchar', length: 50, default: null, comment: 'sap message'})
    sap_message: string;

    @Column({name: 'reverse_time', type: 'datetime', comment: 'reverse time'})
    reverse_time: Date;

    @Column('json', { name: 'original_file_content', nullable: true, comment: 'original file content' })
    original_file_content: JSON;

    @Column({name: 'xml_status', nullable: true, comment: 'PENDING, SUCCESS, FAILURE'})
    xml_status: string;

    @Column({name: 'xml_message', nullable: true, comment: 'xml message'})
    xml_message: string;

    @Column({name: 'xml_uuid', nullable: true, comment: 'xml uuid'})
    xml_uuid: string;

    @Column('json',{name: 'edicom', nullable: true, comment: 'edicom'})
    edicom: JSON;

    @Column({name: 'reference', nullable: true, comment: 'reference'})
    reference: string;

    // @ManyToOne((type) => Es)
    entity: Ar | Ap | Es | ObjectLiteral;
}
