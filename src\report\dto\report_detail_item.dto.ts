import {ApiProperty} from "@nestjs/swagger";
import {BaseDTO} from "../../common/dto/base.dto";

export class ReportDetailItemDto extends BaseDTO {

    @ApiProperty({ description: 'engine doc no' })
    engine_doc_no: string;

    @ApiProperty({ description: 'invoice no' })
    invoice_no: string;

    @ApiProperty({description: 'posting date'})
    posting_date: Date;

    @ApiProperty({description: 'invoice currency'})
    invoice_currency: string;

    @ApiProperty({description: 'invoice reference'})
    invoice_reference: string;

    @ApiProperty({description: 'reconcile amount'})
    reconcile_amount: number;

    @ApiProperty({description: 'br type'})
    br_type: string;

    @ApiProperty({description: 'status: Invoice - not paid, Receive - ar paid, Payment - ap paid'})
    status: string;

    @ApiProperty({description: 'total amount'})
    total_amount: number;

    @ApiProperty({description: 'balance'})
    balance: number;

    @ApiProperty({description: 'to 0'})
    to_0: number;

    @ApiProperty({description: 'to 31'})
    to_31: number;

    @ApiProperty({description: 'to 61'})
    to_61: number;

    @ApiProperty({description: 'to 91'})
    to_91: number;

}