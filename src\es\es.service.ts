import {Injectable} from '@nestjs/common';
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {Es} from "./entities/es.entity";
import {BaseService} from "../common/service/base.service";

@Injectable()
export class EsService extends BaseService<Es>{
  constructor(@InjectRepository(Es) private readonly esRepository: Repository<Es>) {
    super(esRepository, 'es');
  }
}
