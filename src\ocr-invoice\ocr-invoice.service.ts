import {HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import { CreateOcrInvoiceDto } from './dto/create-ocr-invoice.dto';
import { UpdateOcrInvoiceDto } from './dto/update-ocr-invoice.dto';
import {BaseService} from "../common/service/base.service";
import {OcrInvoice} from "./entities/ocr-invoice.entity";
import {Paginated} from "../common/utils/index.util";
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {FileUploadService} from "../file-upload/file-upload.service";
import {OcrInvoiceItem} from "./entities/ocr-invoice-item.entity";
import {CreateApDto} from "../ap/dto/create-ap.dto";
import {Ap} from "../ap/entities/ap.entity";
import {generateLocalDateTimeByFormat} from "../common/utils/function.util";
import {ApItem} from "../ap/entities/ap.item.entity";
import {FileUpload} from "../file-upload/entities/file-upload.entity";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";

@Injectable()
export class OcrInvoiceService extends BaseService<OcrInvoice> {

    private readonly logger = new Logger(OcrInvoice.name);

    constructor(
        @InjectRepository(OcrInvoice) private readonly ocrInvoiceRepository: Repository<OcrInvoice>,
        @InjectRepository(OcrInvoiceItem) private readonly ocrInvoiceItemRepository: Repository<OcrInvoiceItem>,
        private dataSource: DataSource,
    ) {
        super(ocrInvoiceRepository, 'ocr-invoice');
    }

    async createOcrInvoice(createOcrInvoiceDto: CreateOcrInvoiceDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        let ocrInvoice: OcrInvoice = new OcrInvoice();
        ocrInvoice = Object.assign(ocrInvoice, createOcrInvoiceDto);

        try {
            // add ocr invoice data
            await queryRunner.manager.save<OcrInvoice>(ocrInvoice);
            for (let i in createOcrInvoiceDto.items) {
                let ocrInvoiceItem: OcrInvoiceItem = new OcrInvoiceItem();
                ocrInvoiceItem = Object.assign(ocrInvoiceItem, createOcrInvoiceDto.items[i]);
                ocrInvoiceItem.ocrInvoice = ocrInvoice;
                // add gl item data
                await queryRunner.manager.save<OcrInvoiceItem>(ocrInvoiceItem);
            }

            // commit transaction
            await queryRunner.commitTransaction();
            return ocrInvoice;
        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw new HttpException(error, error.status);
        } finally {
            await queryRunner.release();
        }
    }

    async createOcrInvoiceByUploadFile(createOcrInvoiceDto: CreateOcrInvoiceDto, queryRunner: QueryRunner) {
        if (!queryRunner) {
            throw new HttpException('QueryRunner instance cannot be null', 500);
        }

        let ocrInvoice: OcrInvoice = new OcrInvoice();
        ocrInvoice = Object.assign(ocrInvoice, createOcrInvoiceDto);

        try {
            // add ocr invoice data
            await queryRunner.manager.save<OcrInvoice>(ocrInvoice);
            for (let i in createOcrInvoiceDto.items) {
                let ocrInvoiceItem: OcrInvoiceItem = new OcrInvoiceItem();
                ocrInvoiceItem = Object.assign(ocrInvoiceItem, createOcrInvoiceDto.items[i]);
                ocrInvoiceItem.ocrInvoice = ocrInvoice;
                // add gl item data
                await queryRunner.manager.save<OcrInvoiceItem>(ocrInvoiceItem);
            }
            return ocrInvoice;
        } catch (error) {
            throw new HttpException(error, error.status);
        }
    }

  async findAllOcrInvoice(params: any) {
    try {
        const {where, order} = this.parseParams.parseQuery(params);
        if (params.page_index) {
            const {page_index, page_size = 10} = params;
            const [result, total] = await this.ocrInvoiceRepository.findAndCount({
                where: where,
                relations: {items: true},
                order: order,
                take: page_size,
                skip: (page_index - 1) * page_size
            });
            return new Paginated(total, page_size, page_index, result)
        }
        return await this.ocrInvoiceRepository.find({
            where: where,
            relations: {items: true},
            order: order
        });
    } catch (e) {
        throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  async findOne(id: number) {
    return await this.ocrInvoiceRepository.findOne({where: {id: id}, relations: {items: true} });
  }

  // update(id: number, updateOcrInvoiceDto: UpdateOcrInvoiceDto) {
  //   return `This action updates a #${id} ocrInvoice`;
  // }
  //
  // remove(id: number) {
  //   return `This action removes a #${id} ocrInvoice`;
  // }
}
