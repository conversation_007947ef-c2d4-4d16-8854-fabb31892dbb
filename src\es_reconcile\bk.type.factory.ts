import {RSType} from "./bk_type/rs.type";
import {RPType} from "./bk_type/rp.type";
import {PRType} from "./bk_type/pr.type";
import {SRType} from "./bk_type/sr.type";
import {PYType} from "./bk_type/py.type";
import {FTType} from "./bk_type/ft.type";
import {FXType} from "./bk_type/fx.type";
import {PPType} from "./bk_type/pp.type";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {DataSource} from "typeorm";
import {CPType} from "./bk_type/cp.type";
import {BCType} from "./bk_type/bc.type";
import {SPType} from "./bk_type/sp.type";
import {EEType} from "./bk_type/ee.type";
import {DRType} from "./bk_type/dr.type";
import {ReconcileSubmitDto} from "./dto/reconcile_submit.dto";
import {ReconcileSubmitEeDto} from "./dto/reconcile_submit_ee.dto";
import {EnumUtil} from "../common/enum/enum.util";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import {BkTypeStrategyService} from "./bk_type_strategy.service";

/**
 * Bk type factory class
 */
export class BkTypeFactory {

    dataSource: DataSource;
    httpService: HttpService;
    configService: ConfigService;
    brTypeStrategyMap: Map<string, BkTypeStrategyService>;

    constructor(dataSource, httpService, configService) {
        this.dataSource = dataSource;
        this.httpService = httpService;
        this.configService = configService;
        this.brTypeStrategyMap = this.getStrategyMap();
    }

    getStrategyMap() {
        // @ts-ignore
        return new Map([
            ['RS', new RSType(this.dataSource, this.httpService, this.configService)],
            ['RP', new RPType(this.dataSource, this.httpService, this.configService)],
            ['PR', new PRType(this.dataSource, this.httpService, this.configService)],
            ['SR', new SRType(this.dataSource, this.httpService, this.configService)],
            ['PY', new PYType(this.dataSource, this.httpService, this.configService)],
            ['FT', new FTType(this.dataSource, this.httpService, this.configService)],
            ['FX', new FXType(this.dataSource, this.httpService, this.configService)],
            ['CP', new CPType(this.dataSource, this.httpService, this.configService)],
            ['BC', new BCType(this.dataSource, this.httpService, this.configService)],
            ['SP', new SPType(this.dataSource, this.httpService, this.configService)],
            ['EE', new EEType(this.dataSource, this.httpService, this.configService)],
            ['PP', new PPType(this.dataSource, this.httpService, this.configService)],
            ['DR', new DRType(this.dataSource, this.httpService, this.configService)],
        ]);
    }

    getBkTypeStrategyByType(type) {
        return this.brTypeStrategyMap.get(type);
    }

    getBkTypeStrategy(type: string, document_no: string) {
        if (document_no) {
            return this.getBkTypeStrategyByType('EE');
        }
        return this.getBkTypeStrategyByType(EnumUtil.getEnumKey(type, BkTypeEnum));
    }

}