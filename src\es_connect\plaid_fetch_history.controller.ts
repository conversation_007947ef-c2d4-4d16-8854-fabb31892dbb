import {Controller, Get, Param, Query} from '@nestjs/common';
import {ApiOperation, ApiResponse, ApiTags} from "@nestjs/swagger";
import {BaseController} from "../common/controller/base.controller";
import {PlaidFetchHistory} from "./entities/plaid_fetch_history.entity";
import {PlaidFetchHistoryService} from "./plaid_fetch_history.service";
import {Page, Paginated} from "../common/utils/index.util";

@ApiTags('Plaid-Fetch-History')
@Controller('plaid-fetch-history')
export class PlaidFetchHistoryController extends BaseController<PlaidFetchHistory> {
    constructor(private readonly fetchHistoryService: PlaidFetchHistoryService) {
        super(fetchHistoryService);
    }


    @Get()
    @ApiOperation({summary: 'Find all with paging'})
    @ApiResponse({ status: 200, description: 'Ok' })
    async findAll(@Query() params: Page): Promise<PlaidFetchHistory[] | Paginated<PlaidFetchHistory>> {
        return super.findAll(params);
    }
}
