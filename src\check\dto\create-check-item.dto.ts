import {ApiProperty} from "@nestjs/swagger";
import {BaseDTO} from "../../common/dto/base.dto";
import {IsDateString, IsNotEmpty} from "class-validator";
import {Column} from "typeorm";
import {ArItem} from "../../ar/entities/ar.item.entity";

export class CreateCheckItemDto extends BaseDTO {
    @ApiProperty({ description: 'posting date'})
    @IsDateString()
    posting_date: Date;

    @ApiProperty({ description: 'reference no' })
    reference_no: string;

    @ApiProperty({ description: 'invoice id' })
    invoice_id: number;

    @ApiProperty({description: 'total fee'})
    total_fee: number;
}
