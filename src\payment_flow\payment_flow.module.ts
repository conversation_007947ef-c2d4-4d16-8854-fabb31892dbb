import { Module } from '@nestjs/common';
import { PaymentFlowService } from './payment_flow.service';
import { PaymentFlowController } from './payment_flow.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {CompanyFlow} from "../company_flow/entities/company_flow.entity";
import {ApprovalFlow} from "../invoice_flow/entities/invoice_flow.entity";
import {Ap} from "../ap/entities/ap.entity";
import {HttpModule} from "@nestjs/axios";
import {ConfigModule} from "@nestjs/config";
import {PaymentApprovalFlow} from "./entities/payment_flow.entity";

@Module({
  imports: [TypeOrmModule.forFeature([CompanyFlow, PaymentApprovalFlow, Ap]), HttpModule, ConfigModule, ],
  controllers: [PaymentFlowController],
  providers: [PaymentFlowService]
})
export class PaymentFlowModule {}
