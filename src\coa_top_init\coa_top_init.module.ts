import { Module } from '@nestjs/common';
import { CoaTopInitService } from './coa_top_init.service';
import { CoaTopInitController } from './coa_top_init.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {CoaTopInit} from "./entities/coa_top_init.entity";

@Module({
  imports: [TypeOrmModule.forFeature([CoaTopInit])],
  controllers: [CoaTopInitController],
  providers: [CoaTopInitService],
  exports:[CoaTopInitService]
})
export class CoaTopInitModule {}
