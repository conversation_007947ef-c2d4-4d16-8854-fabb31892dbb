import {HttpException, Injectable, Logger, UploadedFile} from '@nestjs/common';
import {OcrInvoice} from "../ocr-invoice/entities/ocr-invoice.entity";
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {OcrInvoiceItem} from "../ocr-invoice/entities/ocr-invoice-item.entity";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {FileUploadService} from "../file-upload/file-upload.service";
import {FileUpload} from "../file-upload/entities/file-upload.entity";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {Ap} from "../ap/entities/ap.entity";
import * as FormData from 'form-data'
import {Readable} from "stream";
import {createReadStream} from "fs";
import {join} from "path";
import {IsArray} from "class-validator";
import {LogService} from "../log/log.service";

@Injectable()
export class OcrUtilsService {

    private readonly logger = new Logger(OcrInvoice.name);
    private readonly ocrBaseUrl = this.configService.get('OCR_BASE_URL');
    private readonly ocrTokenUrl = this.configService.get('OCR_LOGIN_URL');
    private readonly ocrUserName = this.configService.get('OCR_USER_NAME');
    private readonly ocrPassword = this.configService.get('OCR_PASSWORD');
    private readonly ocrClientId = this.configService.get('OCR_CLIENT_ID');
    private readonly ocrClientSecret = this.configService.get('OCR_CLIENT_SECRET');
    private readonly ocrUploadUrl = this.configService.get('OCR_UPLOAD_URL');

    constructor(
        private dataSource: DataSource,
        private configService: ConfigService,
        private httpService: HttpService,
        private logService: LogService,
    ) {}

    async ocrLogin(): Promise<string> {
        this.logger.log('Abbyy OCR API: login begin.');
        try {
            // login input
            const loginInput = {
                username: this.ocrUserName,
                password: this.ocrPassword,
                grant_type: 'password',
                scope: 'openid permissions global.wildcard',
                client_id: this.ocrClientId,
                client_secret: this.ocrClientSecret
            }
            // headers
            const headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
            this.logger.log('[Ocr Login Input]', loginInput);
            const ocrLoginRes = await lastValueFrom(this.httpService.post(this.ocrTokenUrl, loginInput, { headers })
                .pipe(
                    tap(response => {
                        this.logger.log(JSON.stringify(response.data));
                        this.logService.sendLogDataToServer(this.ocrTokenUrl, JSON.stringify(loginInput), response.status.toString(), response.data);
                    }),
                    map((response) => {
                        return response.data;
                    }),
                    catchError(e => {
                        this.logger.log(e);
                        e.message = '[Ocr Login Error] ' + e.message;
                        throw new HttpException(e.response, 500);
                    })
                ));
            this.logger.log('[Ocr Login Response] ', ocrLoginRes);
            if (!ocrLoginRes || !ocrLoginRes.access_token) throw new HttpException(ocrLoginRes, 500);
            const tokenString = ocrLoginRes.access_token;
            this.logger.log('[Ocr Login Token] ', tokenString);
            return tokenString;
        }catch (e) {
            throw new HttpException(e, e.status);
        }
    }

    async ocrPostUpload(file: Express.Multer.File, token: string): Promise<string> {
        this.logger.log('Abbyy OCR API: upload begin.');
        try {
            // data input
            const formData = new FormData();
            formData.append('files', Readable.from(file.buffer), { filename: file.originalname });
            // headers
            const headers = {
                'Content-Type': 'multipart/form-data',
                'Authorization': 'Bearer ' + token
            }
            const ocrUploadRes = await lastValueFrom(this.httpService.post(this.ocrUploadUrl, formData, { headers })
                .pipe(
                    tap(response => {
                        this.logger.log(JSON.stringify(response.data));
                        this.logService.sendLogDataToServer(this.ocrUploadUrl, JSON.stringify(formData), response.status.toString(), response.data);
                    }),
                    map((response) => {
                        return response.data;
                    }),
                    catchError(e => {
                        this.logger.log(e);
                        e.message = '[Ocr upload Error] ' + e.message;
                        throw new HttpException(e.response.statusText, e.response.status);
                    })
                ));
            this.logger.log('[Ocr upload Response] ', ocrUploadRes);
            if (!ocrUploadRes) throw new HttpException('Ocr upload failed.', 500);
            return ocrUploadRes;
        }catch (e) {
            throw new HttpException(e, e.status);
        }

    }

    async ocrIdentify(transactionId: string, token: string): Promise<string>{
        this.logger.log('Abbyy OCR API: identify begin.');
        try {
            const ocrIdentifyUrl = join(this.ocrBaseUrl, '/transactions/', transactionId);
            this.logger.log('ocr identify API url', ocrIdentifyUrl);
            const ocrIdentifyRes = await lastValueFrom(this.httpService.get(ocrIdentifyUrl,
                { headers: {'Authorization': 'Bearer ' + token} })
                .pipe(
                    tap(response => {
                        this.logger.log(JSON.stringify(response.data));
                        this.logService.sendLogDataToServer(ocrIdentifyUrl, null, response.status.toString(), response.data);
                    }),
                    map((response) => {
                        return response.data;
                    }),
                    catchError(e => {
                        this.logger.log(e);
                        e.message = '[Ocr identify Error] ' + e.message;
                        throw new HttpException(e.response.statusText, e.response.status);
                    })
                ));
            this.logger.log('[Ocr identify Response] ', JSON.stringify(ocrIdentifyRes));
            if (!ocrIdentifyRes) throw new HttpException('Ocr identify failed.', 500);
            return this.parseFileId(ocrIdentifyRes);
        }catch (e) {
            throw new HttpException(e, e.status);
        }
    }

    async ocrGetDataByFileId(fileDto: FileUpload, fileId: string, token: string) {
        this.logger.log('Abbyy OCR API: get file data begin.');
        try {
            const ocrDataDownloadUrl = join(this.ocrBaseUrl, '/transactions/', fileDto.ocr_transaction_id, '/files/', fileId, '/download');
            this.logger.log('ocr get file data API url', ocrDataDownloadUrl);
            const ocrFileDataRes = await lastValueFrom(this.httpService.get(ocrDataDownloadUrl,
                { headers: {'Authorization': 'Bearer ' + token} })
                .pipe(
                    tap(response => {
                        this.logger.log(JSON.stringify(response.data));
                        this.logService.sendLogDataToServer(ocrDataDownloadUrl, null, response.status.toString(), response.data);
                    }),
                    map((response) => {
                        return response.data;
                    }),
                    catchError(e => {
                        this.logger.log(e);
                        e.message = '[Ocr get file data Error] ' + e.message;
                        throw new HttpException(e.response.statusText, e.response.status);
                    })
                ));
            this.logger.log('[Ocr get file data Response] ', JSON.stringify(ocrFileDataRes));
            if (!ocrFileDataRes) throw new HttpException('Ocr get file data failed.', 500);
            return this.parseFileData(ocrFileDataRes, fileDto);
        }catch (e) {
            throw new HttpException(e, e.status);
        }
    }

    async parseFileId(ocrIdentifyRes: any): Promise<string> {
        let filedId = null;
        try {
            if (ocrIdentifyRes.status.toUpperCase() == 'PROCESSING')
                throw new HttpException('OCR is processing, please wait for a few seconds', 500);
            if (!ocrIdentifyRes.documents || !IsArray(ocrIdentifyRes.documents))
                // throw new HttpException('parse error: response document from transaction is null.', 500);
                throw new HttpException('OCR is processing, please wait for a few seconds', 500);
            const documents: any[] = ocrIdentifyRes.documents;
            documents.forEach(document => {
                this.logger.log('document', JSON.stringify(document));
                if (!document.resultFiles || !IsArray(document.resultFiles))
                    throw new HttpException('parse error: response resultFiles from transaction is null.', 500);
                const resultFiles: any[] = document.resultFiles;
                for (let file of resultFiles) {
                    if (file.type == 'FieldsJson') filedId = file.fileId;
                }
            });
            if (!filedId) throw new HttpException('parse error: fileId from transaction is null.', 500);
            return filedId;
        }catch (e) {
            throw new HttpException(e, e.status);
        }
    }

    parseFileData(fileDataRes: any, fileDto: FileUpload): OcrInvoice {
        try {
            if (!fileDataRes.Fields)  throw new HttpException('parse error: response file data from file id is null.', 500);
            const ocrFile = fileDataRes.Fields;
            let invoiceDto = new OcrInvoice();
            let invoiceItemList: OcrInvoiceItem[] = [];
            let filePageIndex = 1;
            this.logger.log('ocrFile', ocrFile);
            //Invoice Date
            if (ocrFile['Invoice Date']) invoiceDto.invoice_create_date = ocrFile['Invoice Date'];
            //Invoice Number
            if (ocrFile['Invoice Number']) invoiceDto.invoice_no = ocrFile['Invoice Number'];
            //税前 Total Net Amount
            if (ocrFile['Total Net Amount']) invoiceDto.total_taxable = ocrFile['Total Net Amount'];
            if (ocrFile['Total Net Amount']) invoiceDto.amount = ocrFile['Total Net Amount']
            //总税
            if (ocrFile['Total Taxes']) invoiceDto.total_tax = ocrFile['Total Taxes'];
            //TVQ 魁省税  Tax 1_2
            if (ocrFile['Tax 1_2']) invoiceDto.qst = ocrFile['Tax 1_2'];
            //TPS 联邦  Tax 1_1
            if (ocrFile['Tax 1_1']) invoiceDto.gst = ocrFile['Tax 1_1'];
            //Total Fee
            if (ocrFile['Total']) invoiceDto.total_fee = ocrFile['Total'];
            // other info
            invoiceDto.file_id = fileDto.id;
            invoiceDto.file_page_index = filePageIndex;
            invoiceDto.company_id = fileDto.company_id;
            invoiceDto.company_code = fileDto.company_code;

            //Line Items
            invoiceDto.items = invoiceItemList;
            if (ocrFile['Line Item'] && IsArray(ocrFile['Line Item'])) {
                const ocrItemList: any[] = ocrFile['Line Item'];
                let itemNo = 1;
                for (let ocrItem of ocrItemList) {
                    let invoiceItem = new OcrInvoiceItem();
                    if (ocrItem['Description']) invoiceItem.description = ocrItem['Description'];
                    if (ocrItem['Quantity']) invoiceItem.qty = ocrItem['Quantity'];
                    if (ocrItem['Unit Price']) invoiceItem.unit_price = ocrItem['Unit Price'];
                    if (ocrItem['Total Price']) invoiceItem.total = ocrItem['Total Price'];
                    invoiceItem.item_no = itemNo++;
                    invoiceItemList.push(invoiceItem);
                }
            }
            return invoiceDto;
        }catch (e) {
            throw new HttpException(e, e.status);
        }
    }
}
