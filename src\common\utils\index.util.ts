import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

/**
 * 获取分页信息
 * @param total
 * @param page_size
 * @param page_index
 * @returns
 */
export const getPagination = (
    total: number,
    page_size: number,
    page_index: number,
) => {
    const pageTotal = Math.ceil(total / page_size);
    return {
        total: Number(total),
        page_index: Number(page_index),
        page_size: Number(page_size),
        page_total: Number(pageTotal),
    };
};

export class Page{
    @ApiProperty({type: 'number', description: 'page index', example: 1})
    page_index?: number;

    @ApiProperty({type: 'number', description: 'page size', example: 10})
    page_size?: number;

    constructor(page_size: number, page_index: number) {
        this.page_index = Number(page_index);
        this.page_size = Number(page_size);
    }
}

export class Paginated<T> extends Page{
    total: number;
    page_total: number;
    data: T[];

    constructor(total: number, page_size: number, page_index: number, data: T[]) {
        super(page_size, page_index);
        this.total = Number(total);
        this.page_total = Number(Math.ceil(total / page_size));
        this.data = data;
    }
}

export class ErrorMessage<T> {
    statusCode: number;
    message: string;
    data: T;

    constructor(statusCode: number, message: string, data: T) {
        this.statusCode = statusCode;
        this.message = message;
        this.data = data;
    }
}


