import {Column, Entity, OneTo<PERSON>any} from "typeorm";
import {BaseEntity} from "../../common/entity/base.entity";
import {ApiProperty} from "@nestjs/swagger";

@Entity()
export class Task extends BaseEntity{
    @Column({name: 'name', comment: 'name'})
    name: string;

    @Column({name: 'company_code', comment: 'company code'})
    company_code: string;

    @Column({name: 'company_name', comment: 'company name'})
    company_name: string;

    @Column({name: 'period_start', nullable: true, type: 'date', comment: 'period start date'})
    period_start: Date;

    @Column({name: 'period_end', nullable: true, type: 'date', comment: 'period end date'})
    period_end: Date;

    @Column({name: 'due_date', nullable: true, type: 'date', comment: 'due date'})
    due_date: Date;

    @Column({name: 'estimated_hour', type: 'numeric', nullable: true, precision: 10, scale: 1, comment: 'estimated hour'})
    estimated_hour: number;

    @Column({name: 'actual_hour', type: 'numeric', nullable: true, precision: 10, scale: 1, comment: 'actual hour'})
    actual_hour: number;

    @Column({name: 'status', comment: 'status: todo, doing, done'})
    status: string;

    @Column({name: 'assign_to', nullable: true, comment: 'assign to'})
    assign_to: string;

    @Column({name: 'email', nullable: true, comment: 'email'})
    email: string;

    @Column({name: 'priority', type: 'numeric', nullable: true, precision: 10, scale: 0, comment: 'priority'})
    priority: number;

    @Column({name: 'tag', nullable: true, comment: 'tag'})
    tag: string;

    @Column({name: 'del_flag', default: '0', comment: 'del_flag'})
    del_flag: string;

}

