import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {Decimal} from "decimal.js";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {ReconcileSubmitDto} from "../dto/reconcile_submit.dto";
import {EsReconcileService} from "../es_reconcile.service";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";

/**
 * cash pay
 */
export class CPType extends BkTypeStrategyService {

    /**
     * cp 对账不发送引擎。 创建cp票时，直接发送引擎，并返回对账的document_no
     * @param submitDto
     * @param uuid
     * @param service
     */
    async submit(submitDto: ReconcileSubmitDto, uuid, service: EsReconcileService) {
        const historyList: ReconciliationHistory[] = [];
        await this.dataSource.transaction(async (transactionalEntityManager) => {
            // use to keep es balance after reconcile
            let newEsBalance: Decimal = new Decimal(submitDto.balance);
            for (let invoice of submitDto.invoice_list) {
                newEsBalance = new Decimal(0);
                const invoiceHis = await this.processInvoices(invoice, transactionalEntityManager, service);
                // The invoice in each reconciliation can only be reversed once
                historyList.push(service.buildHistory(submitDto, invoiceHis.newBalance, invoiceHis.brFlag, uuid, invoice.reconcile_amount, invoice, invoice.invoice_id, invoice.br_entity_type, 1, 1))
            }

            // The number es can be reversed is equal to the number of invoices in this reconciliation
            await service.saveHistories(historyList, transactionalEntityManager);
            // send to engine
        });
        return historyList;
    }

    /**
     * CP reverse , directly send to engine
     * @param invoiceHistory
     * @param creator
     * @param queryRunner
     * @param service
     */
    public async reverseForInvoice(invoiceHistory, creator: number, queryRunner: QueryRunner, service) {
        const enginePostInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/reset-clearing-doc';
        const response = await service.sendToEngine(
            {
                document_no: invoiceHistory.document_no,
                // posting_date: `${new Date().getFullYear()}-${(new Date().getMonth() + 1).toString().padStart(2, '0')}-${new Date().getDate().toString().padStart(2, '0')}`
                posting_date: invoiceHistory.posting_date
            },
            queryRunner, invoiceHistory.transaction_id, enginePostInvoiceUrl);
        return response.data.data.document_no;
    }
}