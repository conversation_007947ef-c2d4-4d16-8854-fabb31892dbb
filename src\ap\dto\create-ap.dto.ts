import {ApiProperty, ApiPropertyOptional} from "@nestjs/swagger";
import {ApItem} from "../../ap/entities/ap.item.entity";
import {IsDateString, IsEmail, IsEnum, IsJSON, IsNotEmpty} from "class-validator";
import {PayMethodEnum} from "../../common/enum/pay.method.enum";

export class CreateApDto {
    @ApiProperty({ description: 'companyId', example: 'companyId' })
    @IsNotEmpty()
    company_id: number;

    @ApiProperty({ description: 'companyCode' })
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({description: 'issuer id'})
    issuer_id: string;

    @ApiProperty({description: 'issuer name'})
    issuer_name: string;

    @ApiProperty({description: 'issuer address'})
    issuer_address: string;

    @ApiProperty({description: 'issuer tel'})
    issuer_tel: string;

    @ApiProperty({description: 'issuer email'})
    issuer_email: string;

    @ApiProperty({description: 'reference no'})
    reference_no: string;

    @ApiProperty({description: 'invoice currency: 1 - CNY; 2 - CAD; 3 - USD'})
    invoice_currency: string;

    @ApiProperty({description: 'pay method: 1 - NOT PAID; 2 - CASH PAID; 3 - FUNDING TRANSFER; 4 - INTERCOM'})
    @IsEnum(PayMethodEnum)
    pay_method: string;

    @ApiProperty({description: 'invoice create date'})
    @IsDateString()
    invoice_create_date: Date;

    @ApiProperty({description: 'invoice due date'})
    @IsDateString()
    invoice_due_date: Date;

    @ApiProperty({description: 'posting date'})
    @IsDateString()
    posting_date: Date;

    @ApiProperty({description: 'net amount'})
    net_amount: number;

    @ApiProperty({description: 'gst'})
    gst: number;

    @ApiProperty({description: 'pst'})
    pst: number;

    @ApiProperty({description: 'qst'})
    qst: number;

    @ApiProperty({description: 'total tax'})
    total_tax: number;

    @ApiProperty({description: 'total fee'})
    total_fee: number;

    @ApiProperty({description: 'total fee local'})
    total_fee_local: number;

    @ApiProperty({description: 'exchange rate: other currency -> local'})
    exchange_rate: number;

    @ApiProperty({description: 'invoice comments'})
    invoice_comments: string;

    @ApiProperty({description: 'file id'})
    file_id: number;

    @ApiProperty({description: 'file page index'})
    file_page_index: number;

    @ApiProperty({description: 'file url'})
    file_url: string;

    @ApiProperty({description: 'send sap status: 0 - not sent;1 - sending;2 - sent successfully;3 - sent fail'})
    send_sap_status: number;

    @ApiProperty({description: 'RS（regular sales）0, RP（regular purchase）1, PR（purchase refund）2, SR（sales refund）3, FT（funding trans）4, PY（payroll）5, FX(es of different currency) 6, ES(e-statement) 7'})
    br_type: string;

    @ApiProperty({description: 'creator'})
    creator: string;

    @ApiProperty({description: 'creator_name'})
    creator_name: string;

    @ApiProperty({description: 'po'})
    po: string;

    @ApiProperty({description: 'payment terms day 1'})
    payment_terms_day_1: string;

    @ApiProperty({description: 'payment terms day 2'})
    payment_terms_day_2: string;

    @ApiProperty({description: 'payment terms day 3'})
    payment_terms_day_3: string;

    @ApiProperty({description: 'payment terms discount 1'})
    payment_terms_discount_1: string;

    @ApiProperty({description: 'payment terms discount 2'})
    payment_terms_discount_2: string;

    @ApiProperty({description: 'payment terms discount 3'})
    payment_terms_discount_3: string;

    @ApiProperty({description: 'tax_content'})
    tax_content: JSON;

    @ApiProperty({ description: 'items' })
    items: [ApItem];

    @ApiProperty({ description: 'extension items, payroll invoice only' })
    extension_items: JSON;

    @ApiProperty({description: 'mx_discount'})
    mx_discount: number;

    @ApiProperty({description: 'mx_isr'})
    mx_isr: number;

    @ApiProperty({description: 'mx_iva'})
    mx_iva: number;
}
