import {HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import {Paginated} from "../common/utils/index.util";
import {InjectRepository} from "@nestjs/typeorm";
import {Ap} from "../ap/entities/ap.entity";
import {DataSource, Repository} from "typeorm";
import {ApItem} from "../ap/entities/ap.item.entity";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {FileUploadService} from "../file-upload/file-upload.service";
import {EsReconcileService} from "../es_reconcile/es_reconcile.service";
import {EsReconcileReverseService} from "../es_reconcile/es_reconcile_reverse/es_reconcile_reverse.service";
import {LogService} from "../log/log.service";
import {BaseService} from "../common/service/base.service";
import {Task} from "./entities/task.entity";

@Injectable()
export class TaskService extends BaseService<Task> {

  private readonly logger = new Logger(TaskService.name);

  constructor(
      @InjectRepository(Task) private readonly taskRepository: Repository<Task>,
      private dataSource: DataSource,
      private configService: ConfigService,
      private httpService: HttpService,
      private logService: LogService,
  ) {
    super(taskRepository, 'task');
  }

  async createTask(createTaskDto: CreateTaskDto) {
    try {
      return await this.taskRepository.save(createTaskDto);
    }catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }

  }

  async findAllTask(params: any) {
    try {
      const {where, order} = this.parseParams.parseQuery(params);
      if (params.page_index) {
        const {page_index, page_size = 10} = params;
        const [result, total] = await this.taskRepository.findAndCount({
          where: where,
          order: order,
          take: page_size,
          skip: (page_index - 1) * page_size
        });
        return new Paginated(total, page_size, page_index, result)
      }
      return await this.taskRepository.find({
        where: where,
        order: order
      });
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  async findOne(id: number) {
    return await this.taskRepository.findOne({where: {id: id}});
  }

  remove(id: number) {
    return `This action removes a #${id} task`;
  }
}
