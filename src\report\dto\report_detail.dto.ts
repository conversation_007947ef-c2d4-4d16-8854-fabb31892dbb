import {ApiProperty} from "@nestjs/swagger";
import {BaseDTO} from "../../common/dto/base.dto";
import {ReportDetailItemDto} from "./report_detail_item.dto";
import {ReportDetailSummaryDto} from "./report_detail_summary.dto";

export class ReportDetailDto extends BaseDTO {

    @ApiProperty({ description: 'bp no' })
    bp_no: string;

    @ApiProperty({ description: 'bp name' })
    bp_name: string;

    @ApiProperty({description: 'sub total'})
    summary: ReportDetailSummaryDto[];

    @ApiProperty({description: 'sub total to 0'})
    detail: ReportDetailItemDto[];
}