import {Controller, Get, Post, Body, Patch, Param, Delete, Query} from '@nestjs/common';
import { OcrInvoiceService } from './ocr-invoice.service';
import { CreateOcrInvoiceDto } from './dto/create-ocr-invoice.dto';
import { UpdateOcrInvoiceDto } from './dto/update-ocr-invoice.dto';
import {ApiTags} from "@nestjs/swagger";
import {BaseController} from "../common/controller/base.controller";
import {OcrInvoice} from "./entities/ocr-invoice.entity";

@ApiTags('Ocr-Invoice')
@Controller('ocr-invoice')
export class OcrInvoiceController extends BaseController<OcrInvoice> {
  constructor(private readonly ocrInvoiceService: OcrInvoiceService) {
    super(ocrInvoiceService);
  }

  @Post()
  create(@Body() createOcrInvoiceDto: CreateOcrInvoiceDto) {
    return this.ocrInvoiceService.createOcrInvoice(createOcrInvoiceDto);
  }

  @Get()
  findAll(@Query() queryData: any) {
    return this.ocrInvoiceService.findAllOcrInvoice(queryData);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.ocrInvoiceService.findOne(+id);
  }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateOcrInvoiceDto: UpdateOcrInvoiceDto) {
  //   return this.ocrInvoiceService.update(+id, updateOcrInvoiceDto);
  // }
  //
  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.ocrInvoiceService.remove(+id);
  // }


}
