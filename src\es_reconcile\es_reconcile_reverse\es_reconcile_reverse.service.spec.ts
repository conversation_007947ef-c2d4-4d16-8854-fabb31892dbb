import {Test, TestingModule} from '@nestjs/testing';
import {EsReconcileReverseService} from './es_reconcile_reverse.service';
import {EsModule} from "../../es/es.module";
import {TestDatabaseModule} from "../../../test/dateModule.test";
import {DataSource} from "typeorm";
import {EsReconcileService} from "../es_reconcile.service";
import {EsService} from "../../es/es.service";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {ArModule} from "../../ar/ar.module";
import {ApModule} from "../../ap/ap.module";
import {ConfigModule} from "@nestjs/config";
import {Ar} from "../../ar/entities/ar.entity";
import {Es} from "../../es/entities/es.entity";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {v4 as uuidv4} from 'uuid';
import {Decimal} from "decimal.js";
import {HttpModule} from "@nestjs/axios";

describe('EsReconcileReverseService', () => {
    let service: EsReconcileReverseService;

    // beforeAll(async () => {
    //     const module: TestingModule = await Test.createTestingModule({
    //         imports: [TestDatabaseModule],
    //     }).compile();
    //     dataSource = module.get<DataSource>(DataSource);
    //
    //     const readSqlFile = (filepath: string): string[] => {
    //         return fs
    //             .readFileSync(path.join(__dirname, filepath))
    //             .toString()
    //             .replace(/\r?\n|\r/g, '')
    //             .split(';')
    //             .filter((query) => query?.length);
    //     };
    //
    //     const queries = readSqlFile('/init_data_for_reverse.sql');
    //
    //     for (let i = 0; i < queries.length; i++) {
    //         await dataSource.createQueryRunner().query(queries[i]);
    //     }
    // });
    //
    // beforeEach(async () => {
    //     const module: TestingModule = await Test.createTestingModule({
    //         imports: [TestDatabaseModule, EsModule],
    //         providers: [EsReconcileReverseService],
    //     }).compile();
    //
    //     service = module.get<EsReconcileReverseService>(EsReconcileReverseService);
    // });

    let esService: EsService;
    let queryRunner: QueryRunner;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            imports: [EsModule, ArModule, ApModule, TestDatabaseModule, ConfigModule, HttpModule],
            providers: [EsReconcileService],
        }).compile();

        service = module.get<EsReconcileReverseService>(EsReconcileReverseService);
        esService = module.get<EsService>(EsService);
        // dataSource = module.get<DataSource>(DataSource);

        queryRunner = module.get<DataSource>(DataSource).createQueryRunner();
        // await queryRunner.startTransaction();
    });

    afterEach(async () => {
        // await queryRunner.rollbackTransaction();
        await queryRunner.connection.destroy();
    })

    it('reverse_RS_3_ar_with_1_es_for_full_reconcile', async function () {

        const ar_sql_1 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel, company_email, company_gst_no, company_pst_no, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company, bill_to_street, bill_to_city, bill_to_province, bill_to_country, bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver, ship_to_company, ship_to_street, ship_to_city, ship_to_province, ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url, bank_id, bank_account, bank_name, create_time, creator, update_time, deleted_time, engine_document_id, company_logo, br_type) VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********', '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944', '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********, 'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '', '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, 65.03, 6697.75, null, 0.00, '', 2, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '', '2022-11-28 11:00:44.201802', '828', '2022-12-02 11:10:42', null, null, '/bookkeeping/logo/3000.jpg', 0);`;
        const ar_sql_2 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel, company_email, company_gst_no, company_pst_no, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company, bill_to_street, bill_to_city, bill_to_province, bill_to_country, bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver, ship_to_company, ship_to_street, ship_to_city, ship_to_province, ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url, bank_id, bank_account, bank_name, create_time, creator, update_time, deleted_time, engine_document_id, company_logo, br_type) VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********', '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944', '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********, 'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '', '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, 200.75, 6697.75, null, 0.00, '', 2, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '', '2022-11-28 11:00:44.201802', '828', '2022-12-02 11:10:42', null, null, '/bookkeeping/logo/3000.jpg', 0);`;
        const ar_sql_3 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel, company_email, company_gst_no, company_pst_no, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company, bill_to_street, bill_to_city, bill_to_province, bill_to_country, bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver, ship_to_company, ship_to_street, ship_to_city, ship_to_province, ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url, bank_id, bank_account, bank_name, create_time, creator, update_time, deleted_time, engine_document_id, company_logo, br_type) VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********', '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944', '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********, 'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '', '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, 99.95, 6697.75, null, 0.00, '', 2, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '', '2022-11-28 11:00:44.201802', '828', '2022-12-02 11:10:42', null, null, '/bookkeeping/logo/3000.jpg', 0);`;

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INTERAC ETRNSFR SENT GROCEGO DISTRIBUTION INC 2022329104377CCBD', null, null, 0.00, 365.73, 0.00, '123456', null, '1', 0, 2, 'RxQrdgQ50PuVZ4j5Jw7DS8AjKLXd3qf1OeOaO', 0, null, '2022-11-30 12:35:17.587796', '2022-12-02 11:10:42', null, 2, '2110')`;

        const ar1 = await queryRunner.query(ar_sql_1);
        const ar2 = await queryRunner.query(ar_sql_2);
        const ar3 = await queryRunner.query(ar_sql_3);

        const es = await queryRunner.query(es_sql_1);

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.412011', '201', '2022-12-02 11:10:42.412011', null, 65.03, '2022-11-11', '${uuid}', ${ar1.insertId}, 1, 0, 65.03, 0.00, 2, 0, 1, '3000');`;
        const history_sql_2 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.440130', '201', '2022-12-02 11:10:42.440130', null, 200.75, '2022-11-11', '${uuid}', ${ar2.insertId}, 1, 0, 200.75, 0.00, 2, 0, 1, '3000');`;
        const history_sql_3 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.476740', '201', '2022-12-02 11:10:42.476740', null, 99.95, '2022-11-11', '${uuid}', ${ar3.insertId}, 1, 0, 99.95, 0.00, 2, 0, 1, '3000');`;
        const history_sql_4 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.503365', '201', '2022-12-02 11:10:42.503365', null, 365.73, '2022-11-11', '${uuid}', ${es.insertId}, 1, 7, 365.73, 0.00, 2, 0, 3, '3000');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_2 = await queryRunner.query(history_sql_2);
        const history_3 = await queryRunner.query(history_sql_3);
        const history_4 = await queryRunner.query(history_sql_4);
        try {

            await service.reverseEs(es.insertId, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ar where id in (${ar1.insertId}, ${ar2.insertId}, ${ar3.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId})`);

            expect(arRes[0].balance).toBe("65.03");
            expect(arRes[0].br_flag).toBe(0);

            expect(arRes[1].balance).toBe("200.75");
            expect(arRes[1].br_flag).toBe(0);

            expect(arRes[2].balance).toBe("99.95");
            expect(arRes[2].br_flag).toBe(0);

            expect(esRes[0].balance).toBe("365.73");
            expect(esRes[0].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);
            expect(historiesRes[2].reverse_number).toBe(1);
            expect(historiesRes[3].reverse_number).toBe(3);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ar1.insertId}, ${ar2.insertId}, ${ar3.insertId})`);
            await queryRunner.query(`delete from es where id in (${es.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId})`);
        }
    }, 70000);

    it('reverse_RP_4_ap_with_1_es_for_1_ap_partial_reconcile', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'RONA', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 15.63, 0.78, 0.00, 1.56, 2.34, 500.75, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:05:58', '840', '2022-12-02 11:17:53', null, null, 1);`;
        const ap_sql_2 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', 30105051, 'LES EMBALLAGES MULTIDESIGN', null, null, null, '3.0102022062916567e24', '44751', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 554.08, 27.70, 0.00, 55.27, 82.97, 23.45, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:07:01', '840', '2022-12-02 11:17:53', null, null, 1);`;
        const ap_sql_3 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24', '20220424', '2', '1', '2022-06-29', '2022-06-29', '2022-04-24', 32.99, 0.00, 0.00, 0.00, 0.00, 233.03, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:09:32', '840', '2022-12-02 11:17:53', null, null, 1);`;
        //partial reconcile
        const ap_sql_4 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-25', 13.99, 0.00, 0.00, 0.00, 0.00, 1220.20, null, null, 953.00, null, 1, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:11:26', '840', '2022-12-02 11:17:53', null, null, 1);`;

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 0.00, '123456', null, '1', 1, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;

        const ap1 = await queryRunner.query(ap_sql_1);
        const ap2 = await queryRunner.query(ap_sql_2);
        const ap3 = await queryRunner.query(ap_sql_3);
        const ap4 = await queryRunner.query(ap_sql_4);

        const es = await queryRunner.query(es_sql_1);

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.645219', '201', '2022-12-02 11:17:53.645219', null, 500.75, '2022-11-11', '${uuid}', ${ap1.insertId}, 1, 1, 500.75, 0.00, 2, 0, 1, '');`;
        const history_sql_2 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.674523', '201', '2022-12-02 11:17:53.674523', null, 23.45, '2022-11-11', '${uuid}', ${ap2.insertId}, 1, 1, 23.45, 0.00, 2, 0, 1, '');`;
        const history_sql_3 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.701099', '201', '2022-12-02 11:17:53.701099', null, 233.03, '2022-11-11', '${uuid}', ${ap3.insertId}, 1, 1, 233.03, 0.00, 2, 0, 1, '');`;
        const history_sql_4 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.735527', '201', '2022-12-02 11:17:53.735527', null, 267.20, '2022-11-11', '${uuid}', ${ap4.insertId}, 1, 1, 1220.20, 953.00, 1, 0, 1, '');`;
        const history_sql_5 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 1024.43, '2022-11-11', '${uuid}', ${es.insertId}, 1, 7, 1024.43, 0.00, 2, 0, 4, '');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_2 = await queryRunner.query(history_sql_2);
        const history_3 = await queryRunner.query(history_sql_3);
        const history_4 = await queryRunner.query(history_sql_4);
        const history_5 = await queryRunner.query(history_sql_5);
        try {

            await service.reverseEs(es.insertId, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ap where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId}, ${history_5.insertId})`);

            expect(arRes[0].balance).toBe("500.75");
            expect(arRes[0].br_flag).toBe(0);

            expect(arRes[1].balance).toBe("23.45");
            expect(arRes[1].br_flag).toBe(0);

            expect(arRes[2].balance).toBe("233.03");
            expect(arRes[2].br_flag).toBe(0);

            expect(arRes[3].balance).toBe("1220.20");
            expect(arRes[3].br_flag).toBe(0);

            expect(esRes[0].balance).toBe("1024.43");
            expect(esRes[0].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);
            expect(historiesRes[2].reverse_number).toBe(1);
            expect(historiesRes[3].reverse_number).toBe(1);
            expect(historiesRes[4].reverse_number).toBe(4);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            await queryRunner.query(`delete from es where id in (${es.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId}, ${history_5.insertId})`);
        }
    }, 70000);

    it('reverse_RP_1_ap_with_1_es_for_both_partial_reconcile', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name,
                                                    issuer_address, issuer_tel, issuer_email, invoice_no, reference_no,
                                                    invoice_currency, pay_method, invoice_create_date, invoice_due_date,
                                                    posting_date, net_amount, gst, pst, qst, total_tax, total_fee,
                                                    total_fee_cad, exchange_rate, balance, invoice_comments, br_flag,
                                                    send_engine_status, file_id, file_page_index, file_url, po,
                                                    payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                                    payment_terms_discount_1, payment_terms_discount_2,
                                                    payment_terms_discount_3, create_time, creator, update_time,
                                                    deleted_time, engine_document_id, br_type)
                          VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24',
                                  '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-25', 13.99, 0.00, 0.00,
                                  0.00, 0.00, 1220.20, null, null, 400.00, null, 1, 1, null, null, null, null, null,
                                  null, null, null, null, null, '2022-06-29 16:11:26', '840', '2022-12-02 16:03:48',
                                  null, null, 1);`;

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal,
                                                    deposit, balance, bank_account, statement_period, statement_type,
                                                    br_type, br_flag, plaid_transaction_id, match_flag, creator,
                                                    create_time, update_time, deleted_time, currency,
                                                    company_code)
                          VALUES (51, '2022-11-18', 'INOSSEM CANADA CAP/CDE', null, null, 2989.37, 0.00, 2436.37,
                                  '123456', null, '1', 1, 1, 'rDAqdaAjyKTjOJ8NR5Aah9EEAMPVDJUrMdMPX', 0, null,
                                  '2022-11-30 12:35:17.397377', '2022-12-02 16:03:48', null, 2, '2110');`;

        const ap1 = await queryRunner.query(ap_sql_1);

        const es = await queryRunner.query(es_sql_1);

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time,
                                                                      reconcile_amount, posting_date,
                                                                      transaction_id, br_id, br_type, br_entity_type, before_balance,
                                                                      after_balance, after_br_flag, reverse_number,
                                                                      max_reverse_number, company_code)
                               VALUES ('2022-12-02 16:03:48.857306', '201', '2022-12-02 16:03:48.857306', null,
                                       553.00, '2022-11-11', '${uuid}', ${ap1.insertId}, 1, 1, 953.00,
                                       400.00, 1, 0, 1, '');`;
        const history_sql_2 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time,
                                                                      reconcile_amount, posting_date,
                                                                      transaction_id, br_id, br_type, br_entity_type, before_balance,
                                                                      after_balance, after_br_flag, reverse_number,
                                                                      max_reverse_number, company_code)
                               VALUES ('2022-12-02 16:03:48.895615', '201', '2022-12-02 16:03:48.895615', null,
                                       553.00, '2022-11-11', '${uuid}', ${es.insertId}, 1, 7, 2989.37,
                                       2436.37, 1, 0, 1, '');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_2 = await queryRunner.query(history_sql_2);

        try {

            await service.reverseEs(es.insertId, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ap where id in (${ap1.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId})`);

            expect(arRes[0].balance).toBe("953.00");
            expect(arRes[0].br_flag).toBe(1);


            expect(esRes[0].balance).toBe("2989.37");
            expect(esRes[0].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ap1.insertId})`);
            await queryRunner.query(`delete from es where id in (${es.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId})`);
        }
    }, 70000);

    it('reverse_invoice_of_1_ap_with_1_es', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'RONA', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 15.63, 0.78, 0.00, 1.56, 2.34, 500.75, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:05:58', '840', '2022-12-02 11:17:53', null, null, 1);`;
        const ap_sql_2 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', 30105051, 'LES EMBALLAGES MULTIDESIGN', null, null, null, '3.0102022062916567e24', '44751', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 554.08, 27.70, 0.00, 55.27, 82.97, 23.45, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:07:01', '840', '2022-12-02 11:17:53', null, null, 1);`;
        const ap_sql_3 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24', '20220424', '2', '1', '2022-06-29', '2022-06-29', '2022-04-24', 32.99, 0.00, 0.00, 0.00, 0.00, 233.03, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:09:32', '840', '2022-12-02 11:17:53', null, null, 1);`;
        //partial reconcile
        const ap_sql_4 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-25', 13.99, 0.00, 0.00, 0.00, 0.00, 1220.20, null, null, 953.00, null, 1, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:11:26', '840', '2022-12-02 11:17:53', null, null, 1);`;

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 0.00, '123456', null, '1', 1, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;

        const ap1 = await queryRunner.query(ap_sql_1);
        const ap2 = await queryRunner.query(ap_sql_2);
        const ap3 = await queryRunner.query(ap_sql_3);
        const ap4 = await queryRunner.query(ap_sql_4);

        const es = await queryRunner.query(es_sql_1);

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.645219', '201', '2022-12-02 11:17:53.645219', null, 500.75, '2022-11-11', '${uuid}', ${ap1.insertId}, 1, 1, 500.75, 0.00, 2, 0, 1, '');`;
        const history_sql_2 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.674523', '201', '2022-12-02 11:17:53.674523', null, 23.45, '2022-11-11', '${uuid}', ${ap2.insertId}, 1, 1, 23.45, 0.00, 2, 0, 1, '');`;
        const history_sql_3 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.701099', '201', '2022-12-02 11:17:53.701099', null, 233.03, '2022-11-11', '${uuid}', ${ap3.insertId}, 1, 1, 233.03, 0.00, 2, 0, 1, '');`;
        const history_sql_4 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.735527', '201', '2022-12-02 11:17:53.735527', null, 267.20, '2022-11-11', '${uuid}', ${ap4.insertId}, 1, 1, 1220.20, 953.00, 1, 0, 1, '');`;
        const history_sql_5 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 1024.43, '2022-11-11', '${uuid}', ${es.insertId}, 1, 7, 1024.43, 0.00, 2, 0, 4, '');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_2 = await queryRunner.query(history_sql_2);
        const history_3 = await queryRunner.query(history_sql_3);
        const history_4 = await queryRunner.query(history_sql_4);
        const history_5 = await queryRunner.query(history_sql_5);
        try {

            await service.reverseInvoice(ap1.insertId, 1, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ap where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId}, ${history_5.insertId})`);

            expect(arRes[0].balance).toBe("500.75");
            expect(arRes[0].br_flag).toBe(0);

            expect(arRes[1].balance).toBe("23.45");
            expect(arRes[1].br_flag).toBe(0);

            expect(arRes[2].balance).toBe("233.03");
            expect(arRes[2].br_flag).toBe(0);

            expect(arRes[3].balance).toBe("1220.20");
            expect(arRes[3].br_flag).toBe(0);

            expect(esRes[0].balance).toBe("1024.43");
            expect(esRes[0].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);
            expect(historiesRes[2].reverse_number).toBe(1);
            expect(historiesRes[3].reverse_number).toBe(1);
            expect(historiesRes[4].reverse_number).toBe(4);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            await queryRunner.query(`delete from es where id in (${es.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId}, ${history_5.insertId})`);
        }
    }, 70000);

    it('reverse_invoice_of_1_ap_with_2_es', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'RONA', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 15.63, 0.78, 0.00, 1.56, 2.34, 500.75, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:05:58', '840', '2022-12-02 11:17:53', null, null, 1);`;
        const ap_sql_2 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', 30105051, 'LES EMBALLAGES MULTIDESIGN', null, null, null, '3.0102022062916567e24', '44751', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 554.08, 27.70, 0.00, 55.27, 82.97, 23.45, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:07:01', '840', '2022-12-02 11:17:53', null, null, 1);`;
        const ap_sql_3 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24', '20220424', '2', '1', '2022-06-29', '2022-06-29', '2022-04-24', 32.99, 0.00, 0.00, 0.00, 0.00, 233.03, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:09:32', '840', '2022-12-02 11:17:53', null, null, 1);`;
        // same ap with 2 es
        const ap_sql_4 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type)VALUES (201, '3010', ********, 'AMAZON', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-25', 13.99, 0.00, 0.00, 0.00, 0.00, 1220.20, null, null, 400.00, null, 1, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:11:26', '840', '2022-12-02 16:03:48', null, null, 1);`;

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 0.00, '123456', null, '1', 1, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;
        const es_sql_2 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-18', 'INOSSEM CANADA CAP/CDE', null, null, 2989.37, 0.00, 2436.37, '123456', null, '1', 1, 1, 'rDAqdaAjyKTjOJ8NR5Aah9EEAMPVDJUrMdMPX', 0, null, '2022-11-30 12:35:17.397377', '2022-12-02 16:03:48', null, 2, '2110');`;

        const ap1 = await queryRunner.query(ap_sql_1);
        const ap2 = await queryRunner.query(ap_sql_2);
        const ap3 = await queryRunner.query(ap_sql_3);
        const ap4 = await queryRunner.query(ap_sql_4);

        const es1 = await queryRunner.query(es_sql_1);
        const es2 = await queryRunner.query(es_sql_2);

        const uuid1 = uuidv4();
        const uuid2 = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.645219', '201', '2022-12-02 11:17:53.645219', null, 500.75, '2022-11-11', '${uuid1}', ${ap1.insertId}, 1, 1, 500.75, 0.00, 2, 0, 1, '');`;
        const history_sql_2 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.674523', '201', '2022-12-02 11:17:53.674523', null, 23.45, '2022-11-11', '${uuid1}', ${ap2.insertId}, 1,  1,23.45, 0.00, 2, 0, 1, '');`;
        const history_sql_3 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.701099', '201', '2022-12-02 11:17:53.701099', null, 233.03, '2022-11-11', '${uuid1}', ${ap3.insertId}, 1, 1, 233.03, 0.00, 2, 0, 1, '');`;
        const history_same_ap_sql_4 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.735527', '201', '2022-12-02 11:17:53.735527', null, 267.20, '2022-11-11', '${uuid1}', ${ap4.insertId}, 1, 1, 1220.20, 953.00, 1, 0, 1, '');`;
        const history_es_sql_5 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 1024.43, '2022-11-11', '${uuid1}', ${es1.insertId}, 1, 7, 1024.43, 0.00, 2, 0, 4, '');`;

        const history__same_ap_sql_6 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time,
                                                                      reconcile_amount, posting_date, transaction_id,
                                                                      br_id, br_type, br_entity_type, before_balance, after_balance,
                                                                      after_br_flag, reverse_number, max_reverse_number,
                                                                      company_code)
                               VALUES ('2022-12-02 11:17:53.735527', '201', '2022-12-02 11:17:53.735527', null, 553.00,
                                       '2022-11-11', '${uuid2}', ${ap4.insertId}, 1, 1, 953.00, 400.00, 1, 0, 1, '3000');`;
        const history_es_sql_7 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time,
                                                                      reconcile_amount, posting_date, transaction_id,
                                                                      br_id, br_type, br_entity_type, before_balance, after_balance,
                                                                      after_br_flag, reverse_number, max_reverse_number,
                                                                      company_code)
                               VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 553.00,
                                       '2022-11-11', '${uuid2}', ${es2.insertId}, 1, 7, 2989.37, 2436.37, 1, 0, 1, '3000');`;



        const history_1 = await queryRunner.query(history_sql_1);
        const history_2 = await queryRunner.query(history_sql_2);
        const history_3 = await queryRunner.query(history_sql_3);
        const history_4 = await queryRunner.query(history_same_ap_sql_4);
        const history_5 = await queryRunner.query(history_es_sql_5);
        const history_6 = await queryRunner.query(history__same_ap_sql_6);
        const history_7 = await queryRunner.query(history_es_sql_7);

        try {

            await service.reverseInvoice(ap4.insertId, 1, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ap where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es1.insertId}, ${es2.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId}, ${history_5.insertId}, ${history_6.insertId}, ${history_7.insertId})`);

            expect(arRes[0].balance).toBe("500.75");
            expect(arRes[0].br_flag).toBe(0);

            expect(arRes[1].balance).toBe("23.45");
            expect(arRes[1].br_flag).toBe(0);

            expect(arRes[2].balance).toBe("233.03");
            expect(arRes[2].br_flag).toBe(0);

            expect(arRes[3].balance).toBe("1220.20");
            expect(arRes[3].br_flag).toBe(0);

            expect(esRes[0].balance).toBe("1024.43");
            expect(esRes[0].br_flag).toBe(0);

            expect(esRes[1].balance).toBe("2989.37");
            expect(esRes[1].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);
            expect(historiesRes[2].reverse_number).toBe(1);
            expect(historiesRes[3].reverse_number).toBe(1);
            expect(historiesRes[4].reverse_number).toBe(4);
            expect(historiesRes[5].reverse_number).toBe(1);
            expect(historiesRes[6].reverse_number).toBe(1);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            await queryRunner.query(`delete from es where id in (${es1.insertId}, ${es2.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId}, ${history_5.insertId}, ${history_6.insertId}, ${history_7.insertId})`);
        }
    }, 70000);

    it('reverse_SR_negative_3_ar_with_1_es_for_full_reconcile', async function () {

        const ar_sql_1 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel, company_email, company_gst_no, company_pst_no, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company, bill_to_street, bill_to_city, bill_to_province, bill_to_country, bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver, ship_to_company, ship_to_street, ship_to_city, ship_to_province, ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url, bank_id, bank_account, bank_name, create_time, creator, update_time, deleted_time, engine_document_id, company_logo, br_type) VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********', '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944', '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********, 'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '', '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, -65.03, 6697.75, null, 0.00, '', 2, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '', '2022-11-28 11:00:44.201802', '828', '2022-12-02 11:10:42', null, null, '/bookkeeping/logo/3000.jpg', 3);`;
        const ar_sql_2 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel, company_email, company_gst_no, company_pst_no, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company, bill_to_street, bill_to_city, bill_to_province, bill_to_country, bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver, ship_to_company, ship_to_street, ship_to_city, ship_to_province, ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url, bank_id, bank_account, bank_name, create_time, creator, update_time, deleted_time, engine_document_id, company_logo, br_type) VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********', '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944', '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********, 'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '', '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, -200.75, 6697.75, null, 0.00, '', 2, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '', '2022-11-28 11:00:44.201802', '828', '2022-12-02 11:10:42', null, null, '/bookkeeping/logo/3000.jpg', 3);`;
        const ar_sql_3 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel, company_email, company_gst_no, company_pst_no, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company, bill_to_street, bill_to_city, bill_to_province, bill_to_country, bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver, ship_to_company, ship_to_street, ship_to_city, ship_to_province, ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url, bank_id, bank_account, bank_name, create_time, creator, update_time, deleted_time, engine_document_id, company_logo, br_type) VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********', '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944', '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********, 'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '', '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, -99.95, 6697.75, null, 0.00, '', 2, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '', '2022-11-28 11:00:44.201802', '828', '2022-12-02 11:10:42', null, null, '/bookkeeping/logo/3000.jpg', 3);`;

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INTERAC ETRNSFR SENT GROCEGO DISTRIBUTION INC 2022329104377CCBD', null, null, 0.00, 365.73, 0.00, '123456', null, '1', 3, 2, 'RxQrdgQ50PuVZ4j5Jw7DS8AjKLXd3qf1OeOaO', 0, null, '2022-11-30 12:35:17.587796', '2022-12-02 11:10:42', null, 2, '2110')`;

        const ar1 = await queryRunner.query(ar_sql_1);
        const ar2 = await queryRunner.query(ar_sql_2);
        const ar3 = await queryRunner.query(ar_sql_3);

        const es = await queryRunner.query(es_sql_1);

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.412011', '201', '2022-12-02 11:10:42.412011', null, -65.03, '2022-11-11', '${uuid}', ${ar1.insertId}, 3, 0, -65.03, 0.00, 2, 0, 1, '3000');`;
        const history_sql_2 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.440130', '201', '2022-12-02 11:10:42.440130', null, -200.75, '2022-11-11', '${uuid}', ${ar2.insertId}, 3, 0, -200.75, 0.00, 2, 0, 1, '3000');`;
        const history_sql_3 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.476740', '201', '2022-12-02 11:10:42.476740', null, -99.95, '2022-11-11', '${uuid}', ${ar3.insertId}, 3, 0, -99.95, 0.00, 2, 0, 1, '3000');`;
        const history_sql_4 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:10:42.503365', '201', '2022-12-02 11:10:42.503365', null, 365.73, '2022-11-11', '${uuid}', ${es.insertId}, 3, 7, 365.73, 0.00, 2, 0, 3, '3000');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_2 = await queryRunner.query(history_sql_2);
        const history_3 = await queryRunner.query(history_sql_3);
        const history_4 = await queryRunner.query(history_sql_4);
        try {

            await service.reverseEs(es.insertId, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ar where id in (${ar1.insertId}, ${ar2.insertId}, ${ar3.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId})`);

            expect(arRes[0].balance).toBe("-65.03");
            expect(arRes[0].br_flag).toBe(0);

            expect(arRes[1].balance).toBe("-200.75");
            expect(arRes[1].br_flag).toBe(0);

            expect(arRes[2].balance).toBe("-99.95");
            expect(arRes[2].br_flag).toBe(0);

            expect(esRes[0].balance).toBe("365.73");
            expect(esRes[0].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);
            expect(historiesRes[2].reverse_number).toBe(1);
            expect(historiesRes[3].reverse_number).toBe(3);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ar1.insertId}, ${ar2.insertId}, ${ar3.insertId})`);
            await queryRunner.query(`delete from es where id in (${es.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_2.insertId}, ${history_3.insertId}, ${history_4.insertId})`);
        }
    }, 70000);

    it('reverse_PR_ap_with_1_es_for_1_ap_full_reconcile', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel, issuer_email, invoice_no, reference_no, invoice_currency, pay_method, invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance, invoice_comments, br_flag, send_engine_status, file_id, file_page_index, file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3, payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3, create_time, creator, update_time, deleted_time, engine_document_id, br_type) VALUES (201, '3010', ********, 'RONA', null, null, null, '3.0102022062916567e24', '********', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 15.63, 0.78, 0.00, 1.56, 2.34, -1024.43, null, null, 0.00, null, 2, 1, null, null, null, null, null, null, null, null, null, null, '2022-06-29 16:05:58', '840', '2022-12-02 11:17:53', null, null, 2);`;
        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 0.00, '123456', null, '1', 2, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;

        const ap1 = await queryRunner.query(ap_sql_1);
        const es = await queryRunner.query(es_sql_1);

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.645219', '201', '2022-12-02 11:17:53.645219', null, -1024.43, '2022-11-11', '${uuid}', ${ap1.insertId}, 2, 1, -1024.43, 0.00, 2, 0, 1, '');`;
        const history_sql_5 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 1024.43, '2022-11-11', '${uuid}', ${es.insertId}, 2, 7, 1024.43, 0.00, 2, 0, 4, '');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_5 = await queryRunner.query(history_sql_5);
        try {

            await service.reverseEs(es.insertId, 201);
            const arRes: Ar[] = await queryRunner.query(`select * from ap where id in (${ap1.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_5.insertId})`);

            expect(arRes[0].balance).toBe("-1024.43");
            expect(arRes[0].br_flag).toBe(0);

            expect(esRes[0].balance).toBe("1024.43");
            expect(esRes[0].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);

        }
        finally {
            await queryRunner.query(`delete from ar where id in (${ap1.insertId})`);
            await queryRunner.query(`delete from es where id in (${es.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_5.insertId})`);
        }
    }, 70000);

    it('reverse_FT_ap_with_1_es_for_1_es_full_reconcile', async function () {

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 0.00, 1024.43, 0.00, '123456', null, '1', 1, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;
        const es_sql_2 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 0.00, '123456', null, '1', 4, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;

        const es_1 = await queryRunner.query(es_sql_1);
        const es_2 = await queryRunner.query(es_sql_2); // 发起对账

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.645219', '201', '2022-12-02 11:17:53.645219', null, 1024.43, '2022-11-11', '${uuid}', ${es_1.insertId}, 1, 7, 1024.43, 0.00, 2, 0, 1, '');`;
        const history_sql_5 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 1024.43, '2022-11-11', '${uuid}', ${es_2.insertId}, 4, 7, 1024.43, 0.00, 2, 0, 1, '');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_5 = await queryRunner.query(history_sql_5);
        try {

            await service.reverseEs(es_2.insertId, 201);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es_1.insertId}, ${es_2.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_5.insertId})`);

            expect(esRes[0].balance).toBe("1024.43");
            expect(esRes[0].br_flag).toBe(0);

            expect(esRes[1].balance).toBe("1024.43");
            expect(esRes[1].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);

        }
        finally {
            await queryRunner.query(`delete from es where id in (${es_1.insertId}, ${es_2.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_5.insertId})`);
        }
    }, 70000);

    it('reverse_FX_ap_with_1_es_for_1_es_full_reconcile', async function () {

        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 0.00, 24.43, 0.00, '123456', null, '1', 1, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 2, '2110')`;
        const es_sql_2 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit, balance, bank_account, statement_period, statement_type, br_type, br_flag, plaid_transaction_id, match_flag, creator, create_time, update_time, deleted_time, currency, company_code)VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 0.00, '123456', null, '1', 6, 2, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null, '2022-11-30 12:35:17.613703', '2022-12-02 11:17:53', null, 3, '2110')`;

        const es_1 = await queryRunner.query(es_sql_1);
        const es_2 = await queryRunner.query(es_sql_2); // 发起对账

        const uuid = uuidv4();
        const history_sql_1 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.645219', '201', '2022-12-02 11:17:53.645219', null, 24.43, '2022-11-11', '${uuid}', ${es_1.insertId}, 1, 7, 24.43, 0.00, 2, 0, 1, '');`;
        const history_sql_5 = `INSERT INTO reconciliation_history_v2 (create_time, creator, update_time, deleted_time, reconcile_amount, posting_date, transaction_id, br_id, br_type, br_entity_type, before_balance, after_balance, after_br_flag, reverse_number, max_reverse_number, company_code) VALUES ('2022-12-02 11:17:53.764926', '201', '2022-12-02 11:17:53.764926', null, 1024.43, '2022-11-11', '${uuid}', ${es_2.insertId}, 6, 7, 1024.43, 0.00, 2, 0, 1, '');`;

        const history_1 = await queryRunner.query(history_sql_1);
        const history_5 = await queryRunner.query(history_sql_5);
        try {

            await service.reverseEs(es_2.insertId, 201);
            const esRes: Es[] = await queryRunner.query(`select * from es where id in (${es_1.insertId}, ${es_2.insertId})`);
            const historiesRes: ReconciliationHistory[] = await queryRunner.query(`select * from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_5.insertId})`);

            expect(esRes[0].balance).toBe("24.43");
            expect(esRes[0].br_flag).toBe(0);

            expect(esRes[1].balance).toBe("1024.43");
            expect(esRes[1].br_flag).toBe(0);

            expect(historiesRes[0].reverse_number).toBe(1);
            expect(historiesRes[1].reverse_number).toBe(1);

        }
        finally {
            await queryRunner.query(`delete from es where id in (${es_1.insertId}, ${es_2.insertId})`);
            await queryRunner.query(`delete from reconciliation_history_v2 where id in (${history_1.insertId}, ${history_5.insertId})`);
        }
    }, 70000);


    it('should return', function () {
        const res = service['getBrFlag'](null, new Decimal(100), 100);
        console.log(res);
    });
});




class OrderBuilder {
    public build() {
        this.handleError()
    }
    private handleError() {
        throw new Error('missing ... field in order')
    }
}

describe('Order Builder', () => {
    it('should test the handleError', () => {
        //mock private method
        const handleErrorSpy = jest.spyOn(OrderBuilder.prototype as any, 'handleError');
        const orderBuilder = new OrderBuilder()
        expect(() => orderBuilder.build()).toThrow('missing ... field in order');  // Success!
        expect(handleErrorSpy).toHaveBeenCalled();  // Success!
    });
});
