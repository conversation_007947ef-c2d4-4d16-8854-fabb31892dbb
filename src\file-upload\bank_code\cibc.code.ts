import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {BadRequestException, HttpException, HttpStatus} from "@nestjs/common";
import {BankCodeStrategyService} from "../bank_code_strategy.service";
import {CsvHeaderDataDetector} from "../dto/csv-header-data-detector";
import {FileUpload} from "../entities/file-upload.entity";
import {BankTypeEnum} from "../../common/enum/bank_type.enum";
import {EsCsvDesKeyForRefund, EsCsvDesKeyForTransfer} from "../../common/utils/const";
import {EnumUtil} from "../../common/enum/enum.util";

export class CIBCCode extends BankCodeStrategyService {
    /**
     * Canadian Imperial Bank of Commerce
     * parseCsvData()
     * 标准CSV文件格式，父类方法处理
     * @param file
     */


    /**
     * Canadian Imperial Bank of Commerce
     * detectCsvHeaderAndData()
     * 标准导入格式，父类方法处理
     * @param csvData
     * @param keywords
     */


    /**
     * Canadian Imperial Bank of Commerce
     * buildEsDtoList()
     * 通用数据格式，父类方法处理
     * @param csvHeaderData
     */
}