import {HttpException, Injectable, Logger} from '@nestjs/common';
import {BaseService} from "../common/service/base.service";
import {Check} from "./entities/check.entity";
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {CreateCheckDto} from "./dto/create-check.dto";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {Decimal} from "decimal.js";
import {CreateCheckItemDto} from "./dto/create-check-item.dto";
import {Ap} from "../ap/entities/ap.entity";

@Injectable()
export class CheckService extends BaseService<Check>  {

    private readonly logger = new Logger(CheckService.name);

    constructor(
        @InjectRepository(Check) private readonly checkRepository: Repository<Check>,
        @InjectRepository(Ap) private readonly apRepository: Repository<Ap>,
        private configService: ConfigService,
        private httpService: HttpService,
        private dataSource: DataSource,
    ) {
        super(checkRepository, 'check');
    }

    async createCheckBatch(createCheckDtoList: CreateCheckDto[]) {
        // init transaction instance
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        this.logger.log('createCheckDtoList', JSON.stringify(createCheckDtoList));
        let checkServiceRes;
        let checkUrlList = [];

        try {
            // valid array length
            if (createCheckDtoList.length === 0) throw new HttpException('input data cannot be empty.', 400);
            // valid line Item Total Amount
            for (let i = 0; i < createCheckDtoList.length; i++) {
                let createCheckDto = Object.assign(new CreateCheckDto(), createCheckDtoList[i]);
                if (!createCheckDto.company_id)
                    throw new HttpException('company id is required.', 400);
                if (!createCheckDto.company_code)
                    throw new HttpException('company code is required.', 400);
                if (!createCheckDto.company_name)
                    throw new HttpException('company name is required.', 400);
                if (!createCheckDto.issuer_id)
                    throw new HttpException('issuer id is required.', 400);
                if (!createCheckDto.issuer_name)
                    throw new HttpException('issuer name is required.', 400);
                if (!createCheckDto.check_date)
                    throw new HttpException('check date is required.', 400);
                if (!createCheckDto.check_total_fee)
                    throw new HttpException('check total fee is required.', 400);
                let lineItemTotalAmount = new Decimal(0);
                for (let item of createCheckDto.items) {
                    let createCheckItemDto = Object.assign(new CreateCheckItemDto(), item);
                    if (!createCheckItemDto.invoice_id)
                        throw new HttpException('invoice id is required.', 400);
                    lineItemTotalAmount = Decimal.add(lineItemTotalAmount, createCheckItemDto.total_fee);
                }
                if (!lineItemTotalAmount.equals(createCheckDto.check_total_fee))
                    throw new HttpException(`The check total amount (${createCheckDto.check_total_fee}) does not match the line item total (${lineItemTotalAmount}) for check[${i + 1}].`, 400);
            }

            // call check service
            checkServiceRes = await this.sendCheckDataToService(createCheckDtoList);
            this.logger.log('checkServiceRes', JSON.stringify(checkServiceRes));

            // create check data with transaction
            for (let i = 0; i < checkServiceRes.length; i++) {
                const checkStr = checkServiceRes[i];
                let invoiceIds = '';
                // update check_print_status in ap table
                for (let checkItem of checkStr.items) {
                    invoiceIds = invoiceIds.length == 0 ? `${checkItem.invoice_id}` : invoiceIds + `,${checkItem.invoice_id}`;
                    await queryRunner.manager.update<Ap>(Ap, checkItem.invoice_id, {check_print_status: 1});
                }
                let check: Check = Object.assign(new Check(), checkStr);
                check.invoice_ids = invoiceIds;
                checkUrlList.push(check.check_file_url);
                await queryRunner.manager.save(check);
            }
            // commit transaction
            await queryRunner.commitTransaction();
        } catch (error) {
            await queryRunner.rollbackTransaction();
            throw new HttpException(error, error.status);
        } finally {
            await queryRunner.release();
        }
        return checkUrlList;
    }

    async sendCheckDataToService(createCheckDtoList: CreateCheckDto[]): Promise<CreateCheckDto[]> {
        // send check data to eo-pdf-email-service
        const checkServiceUrl = this.configService.get('PDF_EMAIL_SERVICE_BASE_URL') + '/pdf-bk-cheque';
        this.logger.log('[CHECK][CHECK_SERVICE][url]', checkServiceUrl);
        this.logger.log('[CHECK][CHECK_SERVICE][input]', JSON.stringify(createCheckDtoList));
        const checkServiceRes = await lastValueFrom(this.httpService.post(checkServiceUrl, createCheckDtoList)
            .pipe(
                tap(response => this.logger.log(JSON.stringify(response.data))),
                map((response) => {
                    this.logger.log('[CHECK][CHECK_SERVICE][output]', response.data);
                    return response.data;
                }),
                catchError(e => {
                    this.logger.log('[CHECK][CHECK_SERVICE][Error]', e.response.data);
                    return `[CHECK][CHECK_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
                })
            ));
        this.logger.log('checkServiceRes', JSON.stringify(checkServiceRes));
        if (checkServiceRes && checkServiceRes.code != 200)
            throw new HttpException('[CHECK][CHECK_SERVICE][Error]' + checkServiceRes.message, checkServiceRes.code);
        if (!checkServiceRes || !checkServiceRes.data || checkServiceRes.data.length == 0)
            throw new HttpException('[CHECK][CHECK_SERVICE][Error]' + 'result is empty', 500);
        return checkServiceRes.data;
    }
}
