import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class CreateCompanyFlowDto {
    @ApiProperty({ description: 'companyCode' })
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({ description: 'type' })
    @IsNotEmpty()
    type: string;

    @ApiProperty({ description: 'level' })
    @IsNotEmpty()
    level: number;

    @ApiProperty({ description: 'email_list' })
    @IsNotEmpty()
    email_list: string;

    @ApiProperty({ description: 'low_code' })
    low_code: string;

}
