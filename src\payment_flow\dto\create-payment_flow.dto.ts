import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class CreatePaymentFlowDto {
    @ApiProperty({type: String, description: 'uuid', example: 'cc9e4798-6c09-4973-8ec3-b7978e142374'})
    @IsNotEmpty()
    uuid: string;

    @ApiProperty({ description: 'invoice_id' })
    @IsNotEmpty()
    invoice_id: number;

    @ApiProperty({ description: 'level' })
    @IsNotEmpty()
    level: number;

    @ApiProperty({ description: 'email_list' })
    @IsNotEmpty()
    email_list: string;

    @ApiProperty({ description: 'approver' })
    @IsNotEmpty()
    approver: string;

    @ApiProperty({ description: 'status' })
    @IsNotEmpty()
    status: number;
}

