import {Body, Controller, Logger, Post} from '@nestjs/common';
import { OcrUtilsService } from './ocr-utils.service';
import {OcrInvoice} from "../ocr-invoice/entities/ocr-invoice.entity";

@Controller('ocr-utils')
export class OcrUtilsController {
  private readonly logger = new Logger(OcrUtilsController.name);
  constructor(private readonly ocrUtilsService: OcrUtilsService) {}

  @Post('invoice')
  ocrInvoice(@Body() body: any) {

  }

  @Post('login')
  ocrLogin() {
    const token = this.ocrUtilsService.ocrLogin();
    this.logger.log('[Ocr Login Token] ', token);
  }



}
