import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";

export class RSType extends BkTypeStrategyService {
    /**
     * 自动对账， 查找正数AR的票
     * @param params
     * @param es
     * @param usedMap
     * @param threshold
     */
    public async find(params, es, usedMap, threshold): Promise<MatchDto[]> {
        const matches = (await this.findArs(params, es)).filter(ar => new Decimal(ar.balance).comparedTo(new Decimal(es.balance).add(es.charge_fee)) <= 0 && !usedMap.has(`${ar.id}_${ar.br_type}`));
        return params.ap_integration && params.ap_integration == 1 ? this.getReconciliationCombinationIntegration(matches, new Decimal(es.balance).add(es.charge_fee).toNumber(), true, threshold, usedMap) : this.getReconciliationCombination(matches, new Decimal(es.balance).add(es.charge_fee).toNumber(), true, threshold, usedMap);
    }

    /**
     * 手动对账
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return this.findArs(params, es);
    }

    private async findArs(params, es: Es): Promise<MatchDto[]> {
        if (params.ap_integration && params.ap_integration == 1) {
            // integration
            return (await this.findArForBrMatch(params, true, es.br_type))
                // .filter(ar => (new Decimal(ar.balance).comparedTo(es.balance) <= 0) && ar.invoice_currency === es.currency.toString())
                .filter(ar => (new Decimal(ar.balance).comparedTo(0) != 0) && ar.invoice_currency === es.currency)
                .map(ar => this.convertToMatchDto(ar));
        } else {
            // no integration
            return (await this.findArForBrMatch(params, true, es.br_type))
                .filter(ar => (new Decimal(ar.balance).comparedTo(0) > 0) && ar.invoice_currency === es.currency)
                .map(ar => this.convertToMatchDto(ar));
        }
    }

    public async getDrCr(submitDto) {
        if (BkTypeEnum.RS === submitDto.br_type.toString()){
            return  'dr'
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of RS type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }
}