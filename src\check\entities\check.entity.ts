import {Column, Entity, OneToMany} from "typeorm";
import {BaseEntity} from "../../common/entity/base.entity";

@Entity()
export class Check extends BaseEntity{
    @Column({name: 'company_id', comment: 'company id'})
    company_id: number;

    @Column({name: 'company_code', comment: 'company code'})
    company_code: string;

    @Column({name: 'company_name', comment: 'company name'})
    company_name: string;

    @Column({name: 'issuer_id', comment: 'issuer id'})
    issuer_id: number;

    @Column({name: 'issuer_name', comment: 'issuer name'})
    issuer_name: string;

    @Column({name: 'issuer_address', nullable: true, comment: 'issuer address'})
    issuer_address: string;

    @Column({name: 'street', nullable: true, comment: 'issuer address - street'})
    street: string;

    @Column({name: 'city', nullable: true, comment: 'issuer address - city'})
    city: string;

    @Column({name: 'province', nullable: true, comment: 'issuer address - province'})
    province: string;

    @Column({name: 'country', nullable: true, comment: 'issuer address - country'})
    country: string;

    @Column({name: 'post_code', nullable: true, comment: 'issuer address - post code'})
    post_code: string;

    @Column({name: 'total_fee', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total fee'})
    check_total_fee: number;

    @Column({name: 'invoice_ids', nullable: true, comment: 'invoice ids'})
    invoice_ids: string;

    @Column({name: 'check_date', nullable: true, type: 'date', comment: 'check date'})
    check_date: Date;

    @Column({name: 'check_file_url', nullable: true, comment: 'check file url'})
    check_file_url: string;

}
