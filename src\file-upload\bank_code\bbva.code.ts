import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {BadRequestException, HttpException, HttpStatus} from "@nestjs/common";
import {BankCodeStrategyService} from "../bank_code_strategy.service";
import {CsvHeaderDataDetector} from "../dto/csv-header-data-detector";
import {BankTypeEnum} from "../../common/enum/bank_type.enum";
import {EsCsvDesKeyForRefund} from "../../common/utils/const";
import {EnumUtil} from "../../common/enum/enum.util";
import {FileUpload} from "../entities/file-upload.entity";

export class BBVACode extends BankCodeStrategyService {
    private specialKeys = ['operation date', 'value date', 'code', 'observations', 'concept', 'transaction number', 'amount (cop)', 'balance (cop)'];
    /**
     * BBVA bank
     * parseCsvData()
     * 标准CSV文件格式，父类方法处理
     * @param file
     */


    /**
     * BBVA bank
     * detectCsvHeaderAndData()
     * 特殊数据格式，针对性处理
     * @param csvData
     * @param keywords
     */
    public detectCsvHeaderAndData(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
        if (keywords.length == 0) keywords = this.specialKeys;
        for (let i = 0; i < csvData.length; i++) {
            const row = csvData[i];
            if (row.length < minColumns) continue;

            const score = row.filter(cell =>
                keywords.some(keyword => cell.toLowerCase().includes(keyword)),
            ).length;

            if (score >= 6) {
                let csvHeaderDataRes = new CsvHeaderDataDetector();
                // 获取标题行
                csvHeaderDataRes.header = row;
                // 获取后续数据行
                csvHeaderDataRes.data = csvData.slice(i + 1);
                return csvHeaderDataRes;
            }
        }
        throw new Error('No CSV header or data found.');
    }


    /**
     * BBVA bank
     * buildEsDtoList()
     * 特殊数据格式，针对性处理
     * @param csvHeaderData
     */
    public buildEsDtoList(csvHeaderData: CsvHeaderDataDetector, fileUploadDto: FileUpload, bankType: String): Es[] {
        const headerLine = csvHeaderData.header;
        const dataLine = csvHeaderData.data;
        // this.logger.log('keys', headerLine);
        const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
        let createEsDtoList: Es[] = [];
        for (let i = 0; i < dataLine.length; i++) {
            const esItem = dataLine[i];
            // valid data line
            let dataContent = '';
            for (let t = 0; t < esItem.length; t++) {
                dataContent += esItem[t];
            }
            if (dataContent.trim() == '') continue;
            let createEsDto: Es = new Es();
            createEsDto.company_code = fileUploadDto.company_code;
            createEsDto.currency = fileUploadDto.file_currency;
            createEsDto.bank_account = fileUploadDto.bank_account;
            createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
            createEsDto.creator = fileUploadDto.creator;
            createEsDto.file_id = fileUploadDto.id;
            for (let j = 0; j < headerLine.length; j++) {
                const headerStr = headerLine[j];
                if (headerStr.trim() == '') continue;
                // date
                if (['operation date'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
                    let dateStrFromCsv: string = esItem[j].replace(/\t/g, '').trim();
                    const dateStrArray: string[] = dateStrFromCsv.substring(0, 10).split('-');
                    createEsDto.date = dateStrArray[2] + '-' + dateStrArray[1] + '-' + dateStrArray[0];
                }
                // description
                if (['observations'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
                    createEsDto.description = esItem[j].replace(/\t/g, '').trim();
                }
                // deposit - withdrawal
                if (['amount (cop)'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
                    const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
                    const num = parseFloat(numStr);
                    if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
                    createEsDto.deposit = num;
                    if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
                }
                // if (['abono'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
                //   const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
                //   const num = parseFloat(numStr);
                //   if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
                //   createEsDto.withdrawal = num;
                //   if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
                // }
                // reference
                if (['concept'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
                    createEsDto.reference = esItem[j].replace(/\t/g, '').trim();
                }
                // payer payee
                // if (['destinatario'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
                //   createEsDto.payer_payee = esItem[j].replace(/\t/g, '').trim();
                // }
            }
            try {
                // valid account type
                if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
                // valid account number
                if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
                // valid transaction date
                if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
                // valid description
                if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

                // handle negative value
                if (createEsDto.deposit < 0) {
                    createEsDto.withdrawal = Math.abs(createEsDto.deposit);
                    createEsDto.deposit = 0.00;
                }else if (createEsDto.withdrawal < 0) {
                    createEsDto.deposit = Math.abs(createEsDto.withdrawal);
                    createEsDto.withdrawal = 0.00;
                }

                // valid withdrawal and deposit
                if (createEsDto.deposit == null)
                    throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == null)
                    throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
                    continue;
                    // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
                if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
                    throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
                // set balance and br_type
                let isRefund, isTransfer = false;
                for (let key of EsCsvDesKeyForRefund) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isRefund = true;
                        break;
                    }
                }
                // for (let key of EsCsvDesKeyForTransfer) {
                //   if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                //     isTransfer = true;
                //     break;
                //   }
                // }
                // set br_type
                if (isTransfer) {
                    createEsDto.br_type = parseInt(BkTypeEnum.FT);
                }else if (isRefund) {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
                }else {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
                }
                createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
                // set balance
                createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

                createEsDtoList.push(createEsDto);
            } catch (e) {
                this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
                throw new HttpException(e.response, e.status);
            }
        }
        return createEsDtoList;
    }
}