import { Test, TestingModule } from '@nestjs/testing';
import { EsReconcileController } from './es_reconcile.controller';
import { EsReconcileService } from './es_reconcile.service';

describe('EsReconcileController', () => {
  let controller: EsReconcileController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EsReconcileController],
      providers: [EsReconcileService],
    }).compile();

    controller = module.get<EsReconcileController>(EsReconcileController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
