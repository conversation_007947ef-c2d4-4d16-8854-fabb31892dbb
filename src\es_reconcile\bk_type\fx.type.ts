import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {MatchDto} from "../dto/match.dto";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {EntityTarget} from "typeorm/common/EntityTarget";
import {EntityManager} from "typeorm";
import {ReconcileSubmitDto} from "../dto/reconcile_submit.dto";
import {Es} from "../../es/entities/es.entity";
import {Decimal} from "decimal.js";
import {HttpException, HttpStatus, Injectable} from "@nestjs/common";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {format, parseISO} from "date-fns";
import {EsReconcileService} from "../es_reconcile.service";
import {CurrencyEnum} from "../../common/enum/currency.enum";
import {EnumUtil} from "../../common/enum/enum.util";
import {HttpServiceUtil} from "../../common/utils/httpService.util";
import {EsReconcileReverseService} from "../es_reconcile_reverse/es_reconcile_reverse.service";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";

@Injectable()
export class FXType extends BkTypeStrategyService {

    /**
     * 手动匹配
     *   不同币种，用于CAD 和 USD 流水间对账。这种对账不自动匹配，只有手动匹配，手动对账时只列出公司名下币种不同的所有流水即可。
     *   对账的时候也不强求金额相等。对账动作也不传SAP。
     *
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return (await this.findEsForBrMatch(params))
            .filter(item => (es.withdrawal !== 0 ? item.deposit !== 0 : item.withdrawal !== 0)
                && item.currency !== es.currency
                && item.bank_account !== es.bank_account
                && (params.ap_integration && params.ap_integration == 1 ? item.br_type == 6 : true)
            )
            .map(item => this.convertEsToMatchDto(item))
    }


    async processInvoices(invoice: ReconcileSubmitDto.Invoice, transactionalEntityManager: EntityManager, service: EsReconcileService): Promise<{ newBalance: Decimal; brFlag: number }> {
        return this.updateInvoice(Es, transactionalEntityManager, invoice);
        // const findOne = await service.findInvoice(Es, transactionalEntityManager, invoice);
        // const newBalance = new Decimal(findOne.balance).sub(new Decimal(invoice.reconcile_amount));
        // const brFlag = service.getBrFlag(newBalance, findOne);
        // // update invoice
        // await transactionalEntityManager.update(Es, {id: findOne.id}, {
        //     br_flag: brFlag,
        //     balance: newBalance.toNumber(),
        //     br_type: parseInt(BkTypeEnum.FX),
        //     report_type: EnumUtil.getEnumKey(BkTypeEnum.FX, BkTypeEnum)
        // });
        // return {newBalance, brFlag};
    }

    /**
     * update Es for submit reconcile
     * @param entity
     * @param transactionalEntityManager
     * @param invoice
     */
    async updateInvoice(entity: EntityTarget<any>, transactionalEntityManager: EntityManager, invoice: ReconcileSubmitDto.Invoice) {
        const findOne: Es = await transactionalEntityManager.findOneBy(entity, {id: invoice.invoice_id})
        if (!findOne) {
            throw new HttpException(`[es_reconcile_submit] Es with ${invoice.invoice_id} can not find!`, HttpStatus.BAD_REQUEST);
        }
        // For FX type, reconciliation does not consider whether the amounts are equal
        const newBalance = new Decimal(0);
        const brFlag = 2;

        //update invoice
        await transactionalEntityManager.update(entity, {id: findOne.id}, {
            br_flag: brFlag,
            balance: newBalance.toString(),
            br_type: parseInt(BkTypeEnum.FX),
            report_type: EnumUtil.getEnumKey(BkTypeEnum.FX, BkTypeEnum)
        });
        return {newBalance, brFlag};
    }

    /**
     * reverse fx type
     * @param esHistory
     * @param invoiceHistoryList
     * @param queryRunner
     */
    async reverse(esHistory: ReconciliationHistory, invoiceHistoryList: ReconciliationHistory[], queryRunner: QueryRunner) {
        let esNewBalance = new Decimal(esHistory.entity.balance);
        for (let invoiceHistory of invoiceHistoryList) {
            esNewBalance = esNewBalance.add(new Decimal(esHistory.reconcile_amount).abs());
            await this.reverseEs(invoiceHistory, esHistory, queryRunner);
        }
        return esNewBalance;
    }

    public buildDataForEngine(submitDto: ReconcileSubmitDto, invoice_list_to_engine: any[], newEsBalance: Decimal): any {
        return {
            "creator": submitDto.creator,
            "company_code": submitDto.company_code,
            "posting_date": format(parseISO(submitDto.posting_date), 'yyyy-MM-dd'),
            "post_check": true,
            "header_text": 'reconcile submit',
            "currency": EnumUtil.getEnumKey(submitDto.currency.toString(), CurrencyEnum),
            "ref_document": submitDto.statement_id,
            "line_items": invoice_list_to_engine
        }
    }

    public calculateNewEsBalance(newEsBalance: Decimal, invoice: ReconcileSubmitDto.Invoice) {
        return new Decimal(0);
    }

    async buildInvoiceDataToEngin(es: Es, invoice_list_to_engine: any[], submitDto: ReconcileSubmitDto, invoice: ReconcileSubmitDto.Invoice) {
        const homeCurrency = await this.getHomeCurrency(submitDto);
        if(!EnumUtil.existsByKey(homeCurrency, CurrencyEnum))  throw new HttpException(`[es_reconcile_submit] Company home currency is invalid!`, HttpStatus.INTERNAL_SERVER_ERROR);

        if (es.currency === homeCurrency) {
            // es is home currency
            invoice_list_to_engine.push({
                "gl_account": submitDto.gl_account,
                "amount_tc": invoice.balance,
                "amount_lc": es.balance,
                "dr_cr": es.withdrawal === 0 ? "dr" : "cr",
                "neg_posting": false
            });
            invoice_list_to_engine.push({
                "gl_account": invoice.gl_account,
                "amount_tc": invoice.balance,
                "dr_cr": es.withdrawal === 0 ? "cr" : "dr",
                "neg_posting": false
            });
        } else {
            // invoice is home currency
            invoice_list_to_engine.push({
                "gl_account": submitDto.gl_account,
                "amount_tc": es.balance,
                "dr_cr": es.withdrawal === 0 ? "dr" : "cr",
                "neg_posting": false
            });
            invoice_list_to_engine.push({
                "gl_account": invoice.gl_account,
                "amount_tc": es.balance,
                "amount_lc": invoice.balance,
                "dr_cr": es.withdrawal === 0 ? "cr" : "dr",
                "neg_posting": false
            });
        }
    }

    private async getHomeCurrency(submitDto: ReconcileSubmitDto): Promise<string> {
        const payrollAuthenticationUrl = this.configService.get('PAYROLL_AUTHENTICATION_URL');
        const getCurrencyUrl = this.configService.get('PAYROLL_BASE_URL') + `/users/api/v1/company?code=${submitDto.company_code}`;
        const authenticationParams = {
            "account": this.configService.get('PAYROLL_USER'),
            "password": this.configService.get('PAYROLL_PASSWORD'),
            "strategy": "local"
        }
        const authentication = await new HttpServiceUtil(this.httpService).post(payrollAuthenticationUrl, authenticationParams, null, '[es_reconcile_submit] Get payroll access token error');
        const company = await new HttpServiceUtil(this.httpService).get(getCurrencyUrl, authentication.accessToken, '[es_reconcile_submit] Get company home currency error');
        if (company.data.length > 0 && company.data[0].currency) {
            return company.data[0].currency
        } else {
            throw new HttpException(`[es_reconcile_submit] No home currency!`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateDataToEngine(submitDto: ReconcileSubmitDto, invoice_list_to_engine: any[], newEsBalance: Decimal, service: EsReconcileService) {
        const enginePostInvoiceUrl = service.configService.get('ENGINE_BASE_URL') + '/post-journal-entry';
        const data = this.buildDataForEngine(submitDto, invoice_list_to_engine, newEsBalance);
        console.log(`[es_reconcile_submit] Url: ${enginePostInvoiceUrl}, Data: ${JSON.stringify(data)}`);
        return {url: enginePostInvoiceUrl, data}
        // await service.sendToEngine(data, transactionalEntityManager, uuid, enginePostInvoiceUrl);
    }

    public async sendReverseEngine(esHistory: ReconciliationHistory, queryRunner: QueryRunner, service: EsReconcileReverseService, creator: number) {
        await this.sendESReverseEngine(esHistory , queryRunner, service, creator)
    }

}