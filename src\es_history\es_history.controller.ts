import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { EsHistoryService } from './es_history.service';
import { CreateEsHistoryDto } from './dto/create-es_history.dto';
import { UpdateEsHistoryDto } from './dto/update-es_history.dto';
import {ApiTags} from "@nestjs/swagger";

@ApiTags("Es-History")
@Controller('es-history')
export class EsHistoryController {
  constructor(private readonly esHistoryService: EsHistoryService) {}

  @Post()
  create(@Body() createEsHistoryDto: CreateEsHistoryDto) {
    return this.esHistoryService.create(createEsHistoryDto);
  }

  @Get()
  findAll() {
    return this.esHistoryService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.esHistoryService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateEsHistoryDto: UpdateEsHistoryDto) {
    return this.esHistoryService.update(+id, updateEsHistoryDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.esHistoryService.remove(+id);
  }
}
