import {Body, HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import { BaseService } from 'src/common/service/base.service';
import { Basiq } from './entities/basiq.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { catchError, lastValueFrom, map, tap } from 'rxjs';
import {Es} from "../es/entities/es.entity";
import {EsService} from "../es/es.service";
import { format, parseISO } from 'date-fns';
import { SendBasiqAuthEmailDto } from './dto/SendBasiqAuthEmailDto.dto';
import {EsCsvDesKeyForRefund, EsCsvDesKeyForTransfer} from "../common/utils/const";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import { CreateBasiqDto } from './dto/create-basiq.dto';

@Injectable()
export class BasiqService extends BaseService<Basiq> {

  private readonly logger = new Logger(BasiqService.name);

  constructor(@InjectRepository(Basiq) private readonly repository: Repository<Basiq>,
              protected httpService: HttpService,
              protected configService: ConfigService,
              private esService: EsService) {
    super(repository, 'basiq');
  }

  /**
   * basiq步骤一
   * @param createBasiqDto
   */
  async create(createBasiqDto: CreateBasiqDto) {
    let company_email = createBasiqDto.company_email;
    delete createBasiqDto.company_email;
    let basiq = await super.create(createBasiqDto);
    // //获取basiq user id
    if (!basiq.basiq_user_id && company_email) {
      try {
        let server_token = await this.serverAuth();
        let response = await this.createUser(server_token, company_email);
        if (response && response.type == 'user') {
          basiq.basiq_user_id = response.id;
        }
        basiq = await super.update(basiq.id, basiq);
      }catch(e) {
        this.logger.log(`[warning][${createBasiqDto.company_code}] company_email[${company_email}] is not correct`);
      }
    }
    //更新
    return basiq;
  }

  // async preCreateUser(server_token, company_code) {
  //   const config = {
  //     method: 'get',
  //     // url: "http://users-service:3030/api/v1/company",
  //     url: this.configService.get('EO_APIM') + '/users/api/v1/company',
  //     headers: {
  //       'Authorization': `Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6ImFjY2VzcyJ9.***********************************************************************************************************************************************************************************************************************************************************************************************.pDgL4MkoN3YZrZxRwmg-UQ54gLJ6hiauO16-F2pVApIOXDuCs-lyO8bCJL2_GJjhRaYnM6J0lcbjEzOvtntq8YKoOyAYFsfMj5L0ndIql8MM8pWFQw5HCyXvU006xJl5YOiLKAaDpGTwgX9FIXMH8ei-b1HxnD5IAIx_8A6wQ_KQKdJfzFzUYBEtIZllKmnjTHW8LF6lIcLN_dV6OM06hS4fV5T6xo-iy42t8AINCsltzI6FBKzG9rH8j_9uLwCx6N9o1rYva-b8gQ4m5pp_l9_dLC7enQolkdFsvigJkj4RgNnadU1Vu-60fyeExJ8txfzlnxvhTa4xA83EgB1Reg`,
  //       'Accept': 'application/json',
  //       'Content-Type': 'application/json'
  //     },
  //     params: {
  //       'code': company_code
  //     }
  //   }
  //   let companyResponse = await this.request(config);
  //   if(companyResponse.length == 0) {
  //     throw new HttpException("Please provide an available company_code.", HttpStatus.BAD_REQUEST);
  //   }
  //   let company = companyResponse.data[0];
  //   return await this.createUser(server_token, company.email);
  // }

  /**
   * basiq accounts api
   * @param server_token server token from serverAuth()
   * @param user_id basiq user id from createUser(server_token)
   */
  async getBasiqAccount(server_token, user_id) {
    const config = {
      method: 'get',
      url: `https://au-api.basiq.io/users/${user_id}/accounts`,
      headers: {
        'Authorization': `Bearer ${server_token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    };

    return await this.request(config);
  }

  /**
   * basiq步骤二
   * @return server_token
   */
  async serverAuth() {
    return this.basiqAuth('SERVER_ACCESS', '');
  }

  /**
   * basiq步骤三
   * @param server_token
   * @param email String "xxxx@xxx"
   * @param mobile String "+1647xxxxxxx"
   * @return Object user in basiq end-point
   */
  async createUser(server_token, email) {
    const data = JSON.stringify({
      "email": email,
      // "mobile": mobile
    });

    const config = {
      method: 'post',
      url: 'https://au-api.basiq.io/users',
      headers: {
        'Authorization': `Bearer ${server_token}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      data: data
    };

    return await this.request(config);
  }

  /**
   * basiq步骤四
   */
  async clientAuth(userId) {
    return this.basiqAuth('CLIENT_ACCESS', userId);
  }

  /**
   * basiq步骤五
   */
  creatConnection(client_token) {
    return `https://consent.basiq.io/home?token=${client_token}`;
  }

  async sendAuthBankEmail(sendBasiqAuthEmailDto: SendBasiqAuthEmailDto) {
    const basiqArr = await this.findAll({"bank_id": sendBasiqAuthEmailDto.bank_id});
    if(basiqArr.length == 0) {
      throw new HttpException(`[basiq_send_email Error] bank_id is not exist`, HttpStatus.BAD_REQUEST); 
    }
    const basiq = basiqArr[0];
    // if(basiqArr.length === 0) {
    //   basiq = new Basiq();
    //   basiq.bank_id = SendBasiqAuthEmailDto.bank_id;
    //   basiq.bank_account = SendBasiqAuthEmailDto.bank_account;
    //   basiq.bank_name = SendBasiqAuthEmailDto.bank_name;
    //   basiq.currency = SendBasiqAuthEmailDto.currency;
    //   basiq.account_type = BankTypeEnum[SendBasiqAuthEmailDto.bank_type];
    //   basiq.company_code = SendBasiqAuthEmailDto.company_code;
    //   await this.create(basiq);
    // }else {
    //   basiq = basiqArr[0];
    //   if (basiq.bank_account !== SendBasiqAuthEmailDto.bank_account) {
    //     basiq.bank_account = SendBasiqAuthEmailDto.bank_account;
    //     basiq.bank_name = SendBasiqAuthEmailDto.bank_name;
    //     basiq.currency = SendBasiqAuthEmailDto.currency;
    //     basiq.account_type = BankTypeEnum[SendBasiqAuthEmailDto.bank_type];
    //     await this.update(basiq.id, basiq);
    //   }
    // }

    this.logger.log("[basiq_auth_email] Send basiq Bank auth email begin!");


    if(!basiq.basiq_user_id) {
      let server_token = await this.serverAuth();
      let response = await this.createUser(server_token, sendBasiqAuthEmailDto.company_email);
      if (response && response.type == 'user') {
        basiq.basiq_user_id = response.id;
      }
      await this.update(basiq.id, basiq);
    }
    

    try {
      var clientToken = await this.clientAuth(basiq.basiq_user_id);
      var authLink = this.creatConnection(clientToken);

      const data = {
        from: null,
        to: sendBasiqAuthEmailDto.company_email,
        subject: 'Please authenticate your bank account',
        data: {
            "customerName": sendBasiqAuthEmailDto.company_name,
            "bankAccount": basiq.bank_account,
            'authLink': authLink
        },
        template: 'BankBasiqEmail'
      };

      const emailServiceUrl = this.configService.get('PDF_EMAIL_SERVICE_BASE_URL') + '/email';
      this.logger.log(`[basiq_auth_email] Url: ${emailServiceUrl}, Data: ${JSON.stringify(data)}`);
      const emailServiceRes = await lastValueFrom(this.httpService.post(emailServiceUrl, data)
        .pipe(
            tap(response => this.logger.log(JSON.stringify(response.data))),
            map(async (response) => {
                if (response.data.code == 200) {
                    return "发送成功";
                }else {
                    throw new HttpException(`[basiq_auth_email] email service response error: ${JSON.stringify(response.data)}`, response.data.code);
                }
            }),
            catchError(e => {
                this.logger.log('[basiq_auth_email] email service response error:', e.response.data);
                throw new HttpException(`[basiq_auth_email Error] ${JSON.stringify(e.response.data)}`, e.response.status);
            })
        ));
      this.logger.log('[basiq_auth_email] email service response:', JSON.stringify(emailServiceRes));
      this.logger.log("[basiq_auth_email] Send plaid Bank auth email end!");
    } catch (e) {
      this.logger.error(`[basiq_auth_email] Send plaid auth email fail! ${JSON.stringify(e)}`);
      throw new HttpException(`[basiq_auth_email] Send plaid auth email fail! ${JSON.stringify(e)}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async auth(job_id) {
    const server_token = await this.serverAuth();

    var config = {
      method: 'get',
      url: `https://au-api.basiq.io/jobs/${job_id}`,
      headers: {
        'Authorization': `Bearer ${server_token}`,
        'Accept': 'application/json'
      },
    };
    let response = await this.request(config);
    let basiqUserId: String;
    let basiqStatus = 0;
    if(response.type == 'job' && response.steps) {
      for(const step of response.steps) {
        switch (step.title) {
          case 'verify-credentials':
            let str = String(step.result.url)
            let start = str.indexOf('/users/') + 7;
            let end = str.indexOf('/connections/');
            basiqUserId = str.slice(start, end);
            basiqStatus = step.status == 'success'? 1 : 0;
            break;
          case 'retrieve-accounts':
            break;
          case 'retrieve-transactions':
            break;
        }
      }
    }
    if(basiqUserId) {
      const basiqArr = await this.findAll({'basiq_user_id': basiqUserId});
      if(basiqArr.length == 0) {
        throw new HttpException('basiq_user_id in basiq is not exist', HttpStatus.BAD_REQUEST);
      }
      const basiq = basiqArr[0];
      basiq.basiq_status = basiqStatus;

      //获取银行账号对应的basiq account id
      let accounts = await this.getBasiqAccount(server_token, basiq.basiq_user_id);
      if (accounts && accounts.type == 'list') {
        for (const account of accounts.data) {
          if (account.accountNo == basiq.bank_account) {
            basiq.basiq_account_id = account.id;
            break;
          }
        }
      }

      return await this.update(basiq.id, basiq);
    }

    return null;
  }

  /**
   * basiq步骤六
   * @param server_token String
   * @param basiq Object
   */
  async fetch(server_token, basiq: Basiq) {
    var fetch_start_date = basiq.basiq_last_fetch_date ? basiq.basiq_last_fetch_date : basiq.basiq_init_fetch_date;

    //请求数据
    var config = {
      method: 'get',
      url: `https://au-api.basiq.io/users/${basiq.basiq_user_id}/transactions`,
      headers: {
        'Authorization': `Bearer ${server_token}`,
        'Accept': 'application/json'
      },
      params: {
        // 'limit': 10,
        'filter': `account.id.eq(${basiq.basiq_account_id}),transaction.postDate.gteq(${fetch_start_date},transaction.status.eq('posted'))`
      }
    };

    let transactions = await this.request(config);

    //处理数据，转换成es
    const esList = []
    for(let transaction of transactions.data) {
      esList.push(this.buildEsEntity(transaction, basiq));
    }
    //保存到Es
    await this.esService.batchCreate(esList);
    basiq.basiq_last_fetch_date = format(new Date(), 'yyyy-MM-dd');
    await this.update(basiq.id, basiq)

    //返回结果
    return esList;

  }

  /**
   * basiq authentication
   * @param scope
   * @param userId
   * @return access_token
   */
  private async basiqAuth(scope, userId) {
    var qs = require('qs');
    var data = qs.stringify({
      // 'scope': 'SERVER_ACCESS' 
      'scope': scope,
      'userId': userId
    })

    const api_key = this.configService.get('api_key');

    var config = {
      method: 'post',
      url: 'https://au-api.basiq.io/token',
      headers: { 
        'Authorization': `Basic ${api_key}`, 
        'Content-Type': 'application/x-www-form-urlencoded', 
        'basiq-version': '3.0'
      },
      data : data
    };

    let auth = await this.request(config);

    return auth.access_token;
  }

  async http(method, url, headers, params, data) {
    var config = {
      method: method,
      url: url,
      // baseURL: 'https://au-api.basiq.io',
      headers: headers,
      params: params,
      data: data
    };
    return this.request(config);
  }

  async request(config) {
    return await lastValueFrom(this.httpService.request(config)
    .pipe(
        tap(response => console.log(JSON.stringify(response.data))),
        map(async (response) => {
            if (response.status != 400 && response.data) {
                return response.data;
            }
            throw new HttpException(`${JSON.stringify(response.data)}`, response.status);
        }),
        catchError(e => {
          if(e.response?.data) {
            throw new HttpException(`${JSON.stringify(e.response.data)}`, e.response.status);
          }
          throw new HttpException(`${e}`, HttpStatus.BAD_REQUEST);
        })
    ));
  }

  /**
   * flinks返回的银行流水转换成数据库的Es对象
   * @param transaction
   * @param basiq
   * @returns
   */
  private buildEsEntity(transaction, basiq: Basiq) {
    let es = new Es();
    es.plaid_transaction_id = transaction.id;
    es.date = format(parseISO(transaction.postDate), 'yyyy-MM-dd');
    es.description = transaction.Description;
    es.bank_account = basiq.bank_account;
    es.company_code = basiq.company_code;
    es.statement_type = basiq.account_type.toString();
    es.currency = basiq.currency;

    // set br_type based on description
    let isRefund, isTransfer = false;
    for (let key of EsCsvDesKeyForRefund) {
        if (es.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
          isRefund = true;
          break;
        }
    }
    for (let key of EsCsvDesKeyForTransfer) {
        if (es.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
          isTransfer = true;
          break;
        }
    }

    //format amount.
    if(transaction.direction == 'credit') {
      es.deposit = transaction.amount;
      es.withdrawal = 0;
      es.balance = es.deposit;
      if(transaction.class == 'transfer') {
        es.br_type = parseInt(BkTypeEnum.FT)
      }else {
        es.br_type = parseInt(isTransfer ? BkTypeEnum.FT : (isRefund ? BkTypeEnum.PR : BkTypeEnum.RS));
      }
    }else {
      es.deposit = 0;
      es.withdrawal = Math.abs(transaction.amount);
      es.balance = es.withdrawal;
      if(transaction.class == 'transfer') {
        es.br_type = parseInt(BkTypeEnum.FT)
      }else {
        es.br_type = parseInt(isTransfer ? BkTypeEnum.FT : (isRefund ? BkTypeEnum.SR : BkTypeEnum.RP));
      }
    }

    return es;
  }
}
