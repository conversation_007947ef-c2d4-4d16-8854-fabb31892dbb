import {BadRequestException, HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import { CreateCompanyFlowDto } from './dto/create-company_flow.dto';
import { UpdateCompanyFlowDto } from './dto/update-company_flow.dto';
import {BaseService} from "../common/service/base.service";
import {CompanyFlow} from "./entities/company_flow.entity";
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {Ap} from "../ap/entities/ap.entity";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";
import {Paginated} from "../common/utils/index.util";
import {ApItem} from "../ap/entities/ap.item.entity";
import {BkFlowTypeEnum} from "../common/enum/bk.flow.type.enum";
import {BkFlowLevelEnum} from "../common/enum/bk.flow.level.enum";
import {CreateApDto} from "../ap/dto/create-ap.dto";
import {FileStatusEnum} from "../common/enum/file.status.enum";

@Injectable()
export class CompanyFlowService extends BaseService<CompanyFlow> {

  private readonly logger = new Logger(CompanyFlowService.name);

  constructor(
      @InjectRepository(CompanyFlow) private readonly companyFlowRepository: Repository<CompanyFlow>,
      @InjectRepository(Ap) private readonly apRepository: Repository<Ap>,
      private configService: ConfigService,
      private httpService: HttpService,
      private dataSource: DataSource,
  ) {
    super(companyFlowRepository, 'companyFlow');
  }

  async createCompanyFlow(createCompanyFlowDto: CreateCompanyFlowDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let flow: CompanyFlow = new CompanyFlow();
    flow = Object.assign(flow, createCompanyFlowDto);

    try {
      if (!flow.company_code) {
        throw new HttpException('Company code id is required.', 400);
      }
      if (!flow.type) {
        throw new HttpException('Company code id is required.', 400);
      }
      if (flow.type != BkFlowTypeEnum.INVOICE && flow.type != BkFlowTypeEnum.PAYMENT) {
        throw new HttpException('Type is not correct.', 400);
      }
      if (!flow.level) {
        throw new HttpException('Company code id is required.', 400);
      }
      if (flow.level != BkFlowLevelEnum.LEVEL_1 && flow.level != BkFlowLevelEnum.LEVEL_2 && flow.level != BkFlowLevelEnum.LEVEL_3
          && flow.level != BkFlowLevelEnum.LEVEL_4 && flow.level != BkFlowLevelEnum.LEVEL_5 && flow.level != BkFlowLevelEnum.LEVEL_6
          && flow.level != BkFlowLevelEnum.LEVEL_7 && flow.level != BkFlowLevelEnum.LEVEL_8
      ) {
        throw new HttpException('Level is not correct.', 400);
      }
      if (!flow.email_list) {
        throw new HttpException('Email list is required.', 400);
      }
      // add flow
      await queryRunner.manager.save<CompanyFlow>(flow);
      // commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
    return flow;
  }

  async createCompanyFlowBatch(param: any) {
    if (!param.companyCode) {
      throw new BadRequestException('the company code is required.');
    }
    if (!param.flowList)
      throw new BadRequestException('the company flow data is required.');
    const companyFlowList = [];

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // delete existed record
      await queryRunner.manager.update(CompanyFlow, {company_code: param.companyCode}, {deleted_time: new Date()});
      for (let i=0; i < param.flowList.length; i++) {
        let flow: CompanyFlow = new CompanyFlow();
        flow = Object.assign(flow, param.flowList[i]);
        if (!flow.company_code) {
          throw new HttpException('Company code id is required.', 400);
        }
        if (!flow.type) {
          throw new HttpException('Type id is required.', 400);
        }
        if (flow.type != BkFlowTypeEnum.INVOICE && flow.type != BkFlowTypeEnum.PAYMENT) {
          throw new HttpException('Type is not correct.', 400);
        }
        if (!flow.level) {
          throw new HttpException('Company code id is required.', 400);
        }
        if (flow.level != BkFlowLevelEnum.LEVEL_1 && flow.level != BkFlowLevelEnum.LEVEL_2 && flow.level != BkFlowLevelEnum.LEVEL_3
            && flow.level != BkFlowLevelEnum.LEVEL_4 && flow.level != BkFlowLevelEnum.LEVEL_5 && flow.level != BkFlowLevelEnum.LEVEL_6
            && flow.level != BkFlowLevelEnum.LEVEL_7 && flow.level != BkFlowLevelEnum.LEVEL_8
        ) {
          throw new HttpException('Level is not correct.', 400);
        }
        if (!flow.email_list) {
          throw new HttpException('Email list is required.', 400);
        }
        // save record
        const newFlow = await queryRunner.manager.save<CompanyFlow>(flow);
        companyFlowList.push(newFlow)
      }
      // commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
    return companyFlowList;
  }

  async findAllCompanyFlow(params?: any) {
    try {
      const {where, order} = this.parseParams.parseQuery(params);
      if (params.page_index) {
        const {page_index, page_size = 10} = params;
        const [result, total] = await this.companyFlowRepository.findAndCount({
          where: where,
          order: order,
          take: page_size,
          skip: (page_index - 1) * page_size
        });
        return new Paginated(total, page_size, page_index, result)
      }
      return await this.companyFlowRepository.find({
        where: where,
        order: order
      });
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  // findOne(id: number) {
  //   return `This action returns a #${id} companyFlow`;
  // }
  //
  updateCompanyFlow(id: number, updateCompanyFlowDto: UpdateCompanyFlowDto) {
    return `This action updates a #${id} companyFlow`;
  }

  remove(id: number) {
    return `This action removes a #${id} companyFlow`;
  }
}
