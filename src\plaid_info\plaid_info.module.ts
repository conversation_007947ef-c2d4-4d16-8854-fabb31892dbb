import { Module } from '@nestjs/common';
import { PlaidInfoService } from './plaid_info.service';
import { PlaidInfoController } from './plaid_info.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {PlaidInfo} from "./entities/plaid_info.entity";
import {HttpModule} from "@nestjs/axios";
import {ConfigModule} from "@nestjs/config";

@Module({
  imports: [TypeOrmModule.forFeature([PlaidInfo]), HttpModule, ConfigModule],
  controllers: [PlaidInfoController],
  providers: [PlaidInfoService],
  exports:[PlaidInfoService]
})
export class PlaidInfoModule {}
