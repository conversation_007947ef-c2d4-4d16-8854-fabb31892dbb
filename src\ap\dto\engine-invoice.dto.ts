import {EngineInvoiceItemDto} from "./engine-invoice-item.dto";

export class EngineInvoiceDto {

    // Company code
    company_code: string;

    // Cash paid flag
    cashPaidFlag: number;

    // Business partner code
    bp: string;

    // C - Customer; V - Vendor
    customer_vendor: string;

    // posting date
    posting_date: string;

    // currency: CAD; USD
    currency: string;

    // invoice comment
    header_text: string;

    // invoice no
    invoice_no: string;

    // due date
    due_date: string;

    // hst
    HST: number;

    // qst
    QST: number;

    // gst
    GST: number;

    // gst
    PST: number;

    // tax content
    tax: JSON;

    // creator
    creator: string;

    line_items: EngineInvoiceItemDto[];

    extension_items: JSON;
}
