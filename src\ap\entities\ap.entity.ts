import {BeforeInsert, Column, <PERSON><PERSON><PERSON>, OneTo<PERSON>any} from "typeorm";
import {ApItem} from "../../ap/entities/ap.item.entity";
import {BaseEntity} from "../../common/entity/base.entity";

@Entity()
export class Ap extends BaseEntity{
    @Column({name: 'company_id', comment: 'company id'})
    company_id: number;

    @Column({name: 'company_code', comment: 'company code'})
    company_code: string;

    @Column({name: 'issuer_id', nullable: true, comment: 'issuer id'})
    issuer_id: string;

    @Column({name: 'issuer_name', nullable: true, comment: 'issuer name'})
    issuer_name: string;

    @Column({name: 'issuer_address', nullable: true, comment: 'issuer address'})
    issuer_address: string;

    @Column({name: 'issuer_tel', nullable: true, comment: 'issuer tel'})
    issuer_tel: string;

    @Column({name: 'issuer_email', nullable: true, comment: 'issuer email'})
    issuer_email: string;

    @Column({name: 'invoice_no', nullable: true, comment: 'invoice no'})
    invoice_no: string;

    @Column({name: 'reference_no', nullable: true, comment: 'reference no'})
    reference_no: string;

    @Column({name: 'invoice_currency', comment: 'invoice currency: 1 - CNY; 2 - CAD; 3 - USD'})
    invoice_currency: string;

    @Column({name: 'pay_method', comment: 'pay method: 1 - NOT PAID; 2 - CASH PAID; 3 - FUNDING TRANSFER; 4 - INTERCOM'})
    pay_method: string;

    @Column({name: 'invoice_create_date', nullable: true, type: 'date', comment: 'invoice create date'})
    invoice_create_date: Date;

    @Column({name: 'invoice_due_date', nullable: true, type: 'date', comment: 'invoice due date'})
    invoice_due_date: Date;

    @Column({name: 'posting_date', type: 'date', comment: 'posting date'})
    posting_date: Date;

    @Column({name: 'net_amount', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'net amount'})
    net_amount: number;

    @Column({name: 'gst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'gst'})
    gst: number;

    @Column({name: 'pst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'pst'})
    pst: number;

    @Column({name: 'qst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'qst'})
    qst: number;

    @Column({name: 'total_tax', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total tax'})
    total_tax: number;

    @Column({name: 'total_fee', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total fee'})
    total_fee: number;

    @Column({name: 'total_fee_local', nullable: true, type: 'numeric', precision: 10, scale: 2, comment: 'total fee local'})
    total_fee_local: number;

    @Column({name: 'exchange_rate', nullable: true, type: 'numeric', precision: 10, scale: 5, default: 0.00, comment: 'exchange rate: other currency -> local'})
    exchange_rate: number;

    @Column({name: 'balance', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'balance'})
    balance: number;

    @Column({name: 'invoice_comments', nullable: true, comment: 'invoice comments'})
    invoice_comments: string;

    @Column({name: 'br_flag', default: 0, comment: 'br flag: 0 not paid; 1 partial paid; 2 paid; 3 - reversed'})
    br_flag: number;

    @Column({name: 'check_print_status', default: 0, comment: 'check print status: 0 - NOT PRINTED; 1 - PRINTED'})
    check_print_status: number;

    @Column({name: 'check_print_time', nullable: true, comment: 'check print time'})
    check_print_time: string;

    @Column({name: 'check_print_no', nullable: true, comment: 'check print no'})
    check_print_no: string;

    @Column({name: 'send_engine_status', default: 0, comment: 'send engine status: 0 - not send; 1 - sent success; 2 - sent fail'})
    send_engine_status: number;

    @Column({name: 'engine_document_id', nullable: true, comment: 'engine document id'})
    engine_document_id: string;

    @Column({name: 'cash_engine_payment_no', nullable: true, comment: 'cash engine payment no'})
    cash_engine_payment_no: string;

    @Column({name: 'engine_reverse_document_id', nullable: true, comment: 'engine reverse document id'})
    engine_reverse_document_id: string;

    @Column({name: 'send_sap_status', default: 0, comment: 'send sap status: 0 - not sent;1 - sending;2 - sent successfully;3 - sent fail'})
    send_sap_status: number;

    @Column({name: 'sap_document_id', nullable: true, comment: 'sap document id'})
    sap_document_id: string;

    @Column({name: 'sap_reverse_document_id', nullable: true, comment: 'sap reverse document id'})
    sap_reverse_document_id: string;

    @Column({name: 'sap_message', nullable: true, comment: 'sap message'})
    sap_message: string;

    @Column({name: 'file_id', nullable: true, comment: 'file id'})
    file_id: number;

    @Column({name: 'file_page_index', nullable: true, comment: 'file page index'})
    file_page_index: number;

    @Column({name: 'file_url', nullable: true, comment: 'file url'})
    file_url: string;

    @Column({name: 'xml_url', nullable: true, comment: 'xml url'})
    xml_url: string;

    @Column({name: 'purpose', nullable: true, comment: 'purpose: STANDARD, PROFORMA, CREDIT_MEMO'})
    purpose: string;

    @Column({name: 'br_type', default: '1', comment: 'RS（regular sales）0, RP（regular purchase）1, PR（purchase refund）2, SR（sales refund）3, FT（funding trans）4, PY（payroll）5, FX(es of different currency) 6, ES(e-statement) 7'})
    br_type: string;

    @Column({name: 'resource_key', nullable: true, comment: 'resource key with payroll and oms for reverse'})
    resource_key: string;

    @Column({name: 'po', nullable: true, comment: 'po'})
    po: string;

    @Column({name: 'payment_terms_day_1', nullable: true, comment: 'payment terms day 1'})
    payment_terms_day_1: string;

    @Column({name: 'payment_terms_day_2', nullable: true, comment: 'payment terms day 2'})
    payment_terms_day_2: string;

    @Column({name: 'payment_terms_day_3', nullable: true, comment: 'payment terms day 3'})
    payment_terms_day_3: string;

    @Column({name: 'payment_terms_discount_1', nullable: true, comment: 'payment terms discount 1'})
    payment_terms_discount_1: string;

    @Column({name: 'payment_terms_discount_2', nullable: true, comment: 'payment terms discount 2'})
    payment_terms_discount_2: string;

    @Column({name: 'payment_terms_discount_3', nullable: true, comment: 'payment terms discount 3'})
    payment_terms_discount_3: string;

    @Column('json', { name: 'tax_content', nullable: true, comment: 'tax content' })
    tax_content: JSON;

    @Column({name: 'creator_name', nullable: true, comment: 'creator name'})
    creator_name: string;

    @Column({name: 'payment_approval_fin', default: 0, comment: 'payment approval fin'})
    payment_approval_fin: number;

    @OneToMany(() => ApItem, apItem => apItem.ap)
    items: ApItem[]

    salary_details: any;  //get net_pay items from salary details

    @Column({name: 'flow_status', comment: 'flow_status: 00 - 无invoice审批无payment审批; 01 - 无invoice审批有payment审批; 10 - 有invoice审批无payment审批; 11 - 有invoice审批有payment审批'})
    flow_status: string;

    @Column({name: 'mx_discount', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'mx_discount'})
    mx_discount: number;

    @Column({name: 'mx_isr', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'mx_isr'})
    mx_isr: number;

    @Column({name: 'mx_iva', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'mx_iva'})
    mx_iva: number;

    @Column({name: 'col_ica', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'col_ica'})
    col_ica: number;
}
