import {Test, TestingModule} from '@nestjs/testing';
import {EsHistoryService} from './es_history.service';
import {getRepositoryToken} from "@nestjs/typeorm";
import {EsHistory} from "./entities/es_history.entity";

describe('EsHistoryService', () => {
    let service: EsHistoryService;

    const mockHistoryRepository = {
        create: jest.fn().mockImplementation((dto) => dto),
        save: jest.fn().mockImplementation((dot) => Promise.resolve({id: Date.now(), ...dot})),
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [EsHistoryService, {
                provide: getRepositoryToken(EsHistory),  // mock repository
                useValue: mockHistoryRepository
            }],
        }).compile();

        service = module.get<EsHistoryService>(EsHistoryService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should save EsHistory', async () => {
        expect(await service.create({name: 'Tom'})).toEqual({
            id: expect.any(Number),
            name: 'Tom'
        });
    });
});
