apiVersion: apps/v1
kind: Deployment
metadata:
  name: invoice-statement
  namespace: eo-sandbox
spec:
  replicas: 1
  selector:
    matchLabels:
      app: invoice-statement
  template:
    metadata:
      labels:
        app: invoice-statement
    spec:
      nodeSelector:
        agentpool: sandbox
      containers:
        - name: invoice-statement
          image: eorepo.azurecr.io/eo-invoice-statement-service:latest
          imagePullPolicy: Always
          env:
            - name: NODE_ENV
              value: sandbox
          ports:
            - containerPort: 3030
          volumeMounts:
            - name: files
              mountPath: /home/<USER>/work/upload
              subPath: bkp/upload
      volumes:
        - name: files
          persistentVolumeClaim:
            claimName: eo-files-sandbox-pvc

