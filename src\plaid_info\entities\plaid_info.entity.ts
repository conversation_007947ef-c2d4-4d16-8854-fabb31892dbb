import {BaseEntity} from "../../common/entity/base.entity";
import {Column, Entity} from "typeorm";
import {ApiProperty} from "@nestjs/swagger";

@Entity('plaid_info')
export class PlaidInfo extends BaseEntity{

    @ApiProperty({type: 'string', description: 'company code', example: '3000'})
    @Column({name: 'company_code', type: 'varchar', comment: 'company code'})
    company_code: string;

    @ApiProperty({type: 'number', description: 'bank id associate with payroll', example: '110'})
    @Column({name: 'bank_id', type: 'int', comment: 'bank id associate with payroll'})
    bank_id: number;

    @ApiProperty({type: 'string', description: 'bank account', example: '001-36721-1983873'})
    @Column({name: 'bank_account', nullable: true, type: 'varchar', length: 50, comment: 'bank account'})
    bank_account: string;

    @ApiProperty({type: 'string', description: 'bank short name', example: 'BMO'})
    @Column({name: 'bank_name', nullable: true, type: 'varchar', length: 50, comment: 'bank name'})
    bank_name: string;

    @ApiProperty({type: 'string', description: 'currency:  1 CNY; 2 CAD; 3 USD;', example: '2'})
    @Column({name: 'currency', nullable: true, type: 'varchar', length: 30, comment: 'currency:  1 CNY; 2 CAD; 3 USD;'})
    currency: string;

    @ApiProperty({type: 'string', description: 'account type: 1 cheque; 2 saving; 3 credit', example: '1'})
    @Column({name: 'account_type', nullable: true, type: 'tinyint', comment: 'account type: 1 cheque; 2 saving; 3 credit'})
    account_type: number;

    @ApiProperty({type: 'string', description: 'using to verify plaid token', example: '7434d49606b54abeb6c39dcc15299e49'})
    @Column({name: 'uuid', nullable: true, type: 'varchar', length: 200, comment: 'using to verify plaid token'})
    uuid: string;

    @ApiProperty({type: 'number', description: 'plaid return stats ： 0 initialization 、 1 success  、 2 failed', default: 0, example: 0})
    @Column({name: 'plaid_status', nullable: true, type: 'tinyint', default: 0, comment: 'plaid return stats ： 0 initialization 、 1 success  、 2 failed'})
    plaid_status: number;

    @ApiProperty({type: 'string', description: 'access token returned by plaid'})
    @Column({name: 'plaid_access_token', nullable: true, type: 'varchar', length: 200, comment: 'access token returned by plaid'})
    plaid_access_token: string;

    @ApiProperty({type: 'string', description: 'plaid token generate time'})
    @Column({name: 'plaid_access_time', nullable: true, type: 'datetime', comment: 'plaid token generate time'})
    plaid_access_time: string;

    @ApiProperty({type: 'string', description: 'plaid item'})
    @Column({name: 'plaid_item', nullable: true, type: 'varchar',length: 50, comment: 'plaid item'})
    plaid_item: string;

    @ApiProperty({type: 'string', description: 'plaid cursor'})
    @Column({name: 'plaid_cursor', nullable: true, type: 'varchar', length: 250, comment: 'plaid cursor'})
    plaid_cursor: string;

    @ApiProperty({type: 'string', description: 'plaid init fetch date'})
    @Column({name: 'plaid_init_fetch_date', nullable: true, type: 'date', comment: 'plaid init fetch date'})
    plaid_init_fetch_date: string;

    @ApiProperty({type: 'string', description: 'plaid init fetch datetime'})
    @Column({name: 'plaid_init_fetch_datetime', nullable: true, type: 'datetime', comment: 'plaid init fetch datetime'})
    plaid_init_fetch_datetime: string;

    @ApiProperty({type: 'string', description: 'bank unique id in plaid'})
    @Column({name: 'plaid_account_id', nullable: true, type: 'varchar', length: 50,  comment: 'bank unique id in plaid'})
    plaid_account_id: string;

    @ApiProperty({type: 'string', description: 'plaid transaction id'})
    @Column({name: 'plaid_transaction_id', nullable: true, type: 'varchar', length: 100,  comment: 'fetch bank es'})
    plaid_transaction_id: string;

    @ApiProperty({nullable: true, type: 'string', description: 'Sending mail status: 0 not sending/failed, 1 sending successfully'})
    @Column({name: 'email_status', nullable: true, type: 'tinyint', default: 0,  comment: 'Sending mail status: 0 not sending/failed, 1 sending successfully'})
    email_status: number;
}