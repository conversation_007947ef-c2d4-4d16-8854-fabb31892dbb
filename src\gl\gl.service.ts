import { Injectable } from '@nestjs/common';
import { CreateGlDto } from './dto/create-gl.dto';
import { UpdateGlDto } from './dto/update-gl.dto';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from "@nestjs/typeorm";
import { Gl } from './entities/gl.entity';
import { getPagination } from './../common/utils/index.util';
import {GlItem} from "./entities/gl.item.entity";
import {Ar} from "../ar/entities/ar.entity";

@Injectable()
export class GlService {

  constructor(
      @InjectRepository(Gl) private readonly glRepository: Repository<Gl>,
      @InjectRepository(GlItem) private readonly glItemRepository: Repository<GlItem>,
      private dataSource: DataSource,
  ) {}

  async create(createGlDto: CreateGlDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let gl: Gl = new Gl();
    gl = Object.assign(gl, createGlDto);
    try {
      // add gl data
      await queryRunner.manager.save<Gl>(gl);
      for (let i in createGlDto.items) {
        let glItem: GlItem = new GlItem();
        glItem = Object.assign(glItem, createGlDto.items[i]);
        glItem.gl = gl;
        // add gl item data
        await queryRunner.manager.save<GlItem>(glItem);
      }
      // commit transaction
      await queryRunner.commitTransaction();
      return createGlDto;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(params) {
    if (null == params.page) {
      // get gl list non paging
      const glListNonPaging = await this.glRepository
          .createQueryBuilder('gl')
          .leftJoinAndSelect('gl.items', 'glItem')
          // .where('gl.id=:gl_id', {'gl_id'})
          // .andWhere(queryParams)
          .orderBy({'gl.update_time': 'DESC'})
          .getMany();
      return glListNonPaging;
    }else {
      // get gl list on paging
      const {page, pageSize = 10} = params;
      const glListOnPaging = this.glRepository
          .createQueryBuilder('gl')
          .leftJoinAndSelect('gl.items', 'glItem')
          // .where({delFlag: 0})
          // .andWhere(queryParams)
          .orderBy({'gl.update_time': 'DESC'})
          .skip((page - 1) * pageSize)
          .take(pageSize)
          .getManyAndCount();
      const [list, total] = await glListOnPaging;
      const pagination = getPagination(total, pageSize, page);
      return {
        data: list,
        ...pagination,
      };
    }
  }

  async findOne(id: number) {
    return await this.glRepository.findOne({where: {id: id}, relations: {items: true} });
  }

  async update(id: number, updateGlDto: UpdateGlDto) {
    return await this.glRepository.update(id, updateGlDto);
  }

  async remove(id: number) {
    return await this.glRepository.softDelete(id);
  }
}
