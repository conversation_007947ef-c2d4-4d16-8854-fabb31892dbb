import {ApiProperty} from "@nestjs/swagger";
import {ArItem} from "../entities/ar.item.entity";
import {BaseDTO} from "../../common/dto/base.dto";
import {IsDateString, IsEmail, IsEnum, IsJSON, IsNotEmpty} from "class-validator";
import {PayMethodEnum} from "../../common/enum/pay.method.enum";

export class CreateArDto extends BaseDTO {
    @ApiProperty({ description: 'companyId', example: 'companyId' })
    @IsNotEmpty()
    company_id: number;

    @ApiProperty({ description: 'companyCode' })
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({description: 'company logo'})
    company_logo: string;

    @ApiProperty({description: 'company name'})
    @IsNotEmpty()
    company_name: string;

    @ApiProperty({description: 'company address'})
    company_address: string;

    @ApiProperty({description: 'company tel'})
    company_tel: string;

    @ApiProperty({description: 'company email'})
    @IsEmail()
    company_email: string;

    @ApiProperty({description: 'company gst no'})
    company_gst_no: string;

    @ApiProperty({description: 'company currency: CNY, CAD, USD'})
    company_currency: string;

    @ApiProperty({description: 'company pst no'})
    company_pst_no: string;

    @ApiProperty({description: 'reference no'})
    reference_no: string;

    @ApiProperty({description: 'invoice currency: 1 - CNY; 2 - CAD; 3 - USD'})
    invoice_currency: string;

    @ApiProperty({description: 'pay method: 1 - NOT PAID; 2 - CASH PAID; 3 - FUNDING TRANSFER; 4 - INTERCOM'})
    @IsEnum(PayMethodEnum)
    pay_method: string;

    @ApiProperty({description: 'invoice create date'})
    @IsDateString()
    invoice_create_date: Date;

    @ApiProperty({description: 'invoice due date'})
    @IsDateString()
    invoice_due_date: Date;

    @ApiProperty({description: 'posting date'})
    @IsDateString()
    posting_date: Date;

    @ApiProperty({description: 'bill to customer id'})
    @IsNotEmpty()
    bill_to_customer_id: string;

    @ApiProperty({description: 'bill to receiver'})
    bill_to_receiver: string;

    @ApiProperty({description: 'bill to company'})
    @IsNotEmpty()
    bill_to_company: string;

    @ApiProperty({description: 'bill to street'})
    bill_to_street: string;

    @ApiProperty({description: 'bill to city'})
    bill_to_city: string;

    @ApiProperty({description: 'bill to province'})
    bill_to_province: string;

    @ApiProperty({description: 'bill to country'})
    bill_to_country: string;

    @ApiProperty({description: 'bill to postal code'})
    bill_to_postal_code: string;

    @ApiProperty({description: 'bill to tel'})
    bill_to_tel: string;

    @ApiProperty({description: 'bill to email'})
    bill_to_email: string;

    @ApiProperty({description: 'ship to receiver'})
    ship_to_receiver: string;

    @ApiProperty({description: 'ship to company'})
    ship_to_company: string;

    @ApiProperty({description: 'ship to street'})
    ship_to_street: string;

    @ApiProperty({description: 'ship to city'})
    ship_to_city: string;

    @ApiProperty({description: 'ship to province'})
    ship_to_province: string;

    @ApiProperty({description: 'ship to country'})
    ship_to_country: string;

    @ApiProperty({description: 'ship to postal code'})
    ship_to_postal_code: string;

    @ApiProperty({description: 'ship to tel'})
    ship_to_tel: string;

    @ApiProperty({description: 'ship to email'})
    ship_to_email: string;

    @ApiProperty({description: 'net amount'})
    net_amount: number;

    @ApiProperty({description: 'gst'})
    gst: number;

    @ApiProperty({description: 'pst'})
    pst: number;

    @ApiProperty({description: 'qst'})
    qst: number;

    @ApiProperty({description: 'total tax'})
    total_tax: number;

    @ApiProperty({description: 'total fee'})
    total_fee: number;

    @ApiProperty({description: 'total fee local'})
    total_fee_local: number;

    @ApiProperty({description: 'exchange rate: other currency -> local'})
    exchange_rate: number;

    @ApiProperty({description: 'invoice comments'})
    invoice_comments: string;

    @ApiProperty({description: 'file id'})
    file_id: number;

    @ApiProperty({description: 'file page index'})
    file_page_index: number;

    @ApiProperty({description: 'file url'})
    file_url: string;

    @ApiProperty({description: 'RS（regular sales）0, RP（regular purchase）1, PR（purchase refund）2, SR（sales refund）3, FT（funding trans）4, PY（payroll）5, FX(es of different currency) 6, ES(e-statement) 7'})
    br_type: string;

    @ApiProperty({description: 'bank id'})
    bank_id: number;

    @ApiProperty({description: 'bank account'})
    bank_account: string;

    @ApiProperty({description: 'bank name'})
    bank_name: string;

    @ApiProperty({description: 'language: en, fr'})
    language: string;

    @ApiProperty({description: 'creator'})
    creator: string;

    @ApiProperty({description: 'creator_name'})
    creator_name: string;

    @ApiProperty({description: 'tax_content'})
    tax_content: JSON;

    @ApiProperty({description: 'original_file_content'})
    original_file_content: JSON;

    @ApiProperty({description: 'reverse_file_content'})
    reverse_file_content: JSON;

    @ApiProperty({description: 'sap_cancel_bill_doc'})
    sap_cancel_bill_doc: string;

    @ApiProperty({description: 'sap_original_bill_doc'})
    sap_original_bill_doc: string;

    @ApiProperty({description: 'sap_billing_type'})
    sap_billing_type: string;

    @ApiProperty({description: 'mx_discount'})
    mx_discount: number;

    @ApiProperty({description: 'mx_isr'})
    mx_isr: number;

    @ApiProperty({description: 'mx_iva'})
    mx_iva: number;

    @ApiProperty({description: 'col_ica'})
    col_ica: number;

    @ApiProperty({description: 'xml_status'})
    xml_status: string;

    @ApiProperty({description: 'xml_message'})
    xml_message: string;

    @ApiProperty({description: 'xml_uuid'})
    xml_uuid: string;

    @ApiProperty({ description: 'items' })
    items: [ArItem];
}
