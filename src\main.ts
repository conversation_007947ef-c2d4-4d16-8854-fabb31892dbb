import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import {ConfigService} from "@nestjs/config";
import {Logger, ValidationPipe} from "@nestjs/common";
import {createSwagger} from "../config/swagger.config";
import {HttpExceptionFilter} from "./filters/http-exception.filter";
import {WINSTON_MODULE_NEST_PROVIDER} from "nest-winston";
import {LogService} from "./log/log.service";
import * as express from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // set global prefix
  app.setGlobalPrefix('api/v1');
  app.use(express.json({ limit: '10mb' }));
  app.useGlobalPipes(new ValidationPipe());
  const logService = app.get(LogService);
  app.useGlobalFilters(new HttpExceptionFilter(logService));
  createSwagger(app);
  app.enableCors();
  app.useLogger(app.get(WINSTON_MODULE_NEST_PROVIDER));
  const configService = app.get<ConfigService>(ConfigService);
  await app.listen(`${configService.get('PORT')}`);

  Logger.log(`Application is running on: ${await app.getUrl()}`, 'main',);
}
bootstrap();
