import {Injectable, Logger} from '@nestjs/common';
import {lastValueFrom, tap} from "rxjs";
import {catchError, map} from "rxjs/operators";
import {LoggingInterceptor} from "../interceptor/logging.interceptor";
import {HttpService} from "@nestjs/axios";
import {ConfigService} from "@nestjs/config";

@Injectable()
export class LogService {
    private readonly logger = new Logger(LoggingInterceptor.name);

    constructor(private httpService: HttpService,
                private configService: ConfigService) {
    }

    generateLogData(url: string, params: string, status: string, response: string) {
        return {
            user_account: '',
            company_code: '',
            url: url,
            service: 'BKP-V2',
            status: status,
            del_flag: '0',
            level: '3',
            type: '2',
            data: {request: params, response: response},
            message: '',
            message_ext0: '',
            message_ext1: ''
        }
    }

    sendLogDataToServer(url: string, params: string, status: string, response: string) {
        const logInput = this.generateLogData(url, params, status, response);
        const logServiceUrl = this.configService.get('LOG_SERVICE_URL');
        this.logger.log('[LOG][url]', logServiceUrl);
        this.logger.log('[LOG][input]', logInput);
        lastValueFrom(this.httpService.post(logServiceUrl, logInput)
            .pipe(
                tap(response => this.logger.log(JSON.stringify(response.data))),
                map((response) => {
                    this.logger.log('log service response', response.data);
                    return response.data;
                }),
                catchError(e => {
                    e.response.message = '[LOG][Error] ' + JSON.stringify(e.response.data);
                    this.logger.error(e.response.message);
                    return e.response.message;
                })
            ));
    }
}
