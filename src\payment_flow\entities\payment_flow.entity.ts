import {BaseEntity} from "../../common/entity/base.entity";
import {Column, Entity} from "typeorm";

@Entity()
export class PaymentApprovalFlow extends BaseEntity{
    @Column({name: 'uuid', comment: 'uuid'})
    uuid: string;

    @Column({name: 'invoice_id', comment: 'invoice id'})
    invoice_id: number;

    @Column({name: 'level', comment: 'level'})
    level: number;

    @Column({name: 'email_list', comment: 'email list'})
    email_list: string;

    @Column({name: 'approver', comment: 'approver'})
    approver: string;

    @Column({name: 'status', comment: 'status'})
    status: number;

}
