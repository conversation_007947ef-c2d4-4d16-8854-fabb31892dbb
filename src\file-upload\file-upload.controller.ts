import {
  Controller,
  Post,
  Body,
  Param,
  Delete,
  UseInterceptors,
  UploadedFiles, Patch
} from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { CreateFileUploadDto } from './dto/create-file-upload.dto';
import {FileInterceptor, FilesInterceptor} from "@nestjs/platform-express";
import {Express} from "express";
import {ApiOperation, ApiTags} from "@nestjs/swagger";
import {BaseController} from "../common/controller/base.controller";
import {FileUpload} from "./entities/file-upload.entity";

@ApiTags('File-Upload')
@Controller('file')
export class FileUploadController extends BaseController<FileUpload> {
  constructor(private readonly fileUploadService: FileUploadService) {
    super(fileUploadService);
  }

  @Post()
  create(@Body() createFileUploadDto: CreateFileUploadDto) {
    return this.fileUploadService.create(createFileUploadDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.fileUploadService.remove(parseInt(id));
  }
  
  @UseInterceptors(FilesInterceptor('file'))
  @Post('upload')
  @ApiOperation({
    summary: 'upload AP/ES file',
  })
  uploadFile(
      @Body() jsonStr: String,
      @UploadedFiles() files: Express.Multer.File[]
  ) {
    console.log('files: ', files);
    console.log('body: ', jsonStr);
    return this.fileUploadService.upload(jsonStr, files);
  }

  @UseInterceptors(FilesInterceptor('file'))
  @Post('upload/xml')
  @ApiOperation({
    summary: 'upload AP/ES file',
  })
  uploadFileAndXml(
      @Body() jsonStr: String,
      @UploadedFiles() files: Express.Multer.File[]
  ) {
    console.log('files: ', files);
    console.log('body: ', jsonStr);
    return this.fileUploadService.uploadFileAndXml(jsonStr, files);
  }

  @UseInterceptors(FilesInterceptor('file'))
  @Post('upload/xml/new')
  @ApiOperation({
    summary: 'upload AP/ES file',
  })
  uploadFileAndXmlNew(
      @Body() jsonStr: String,
      @UploadedFiles() files: Express.Multer.File[]
  ) {
    console.log('files: ', files);
    console.log('body: ', jsonStr);
    return this.fileUploadService.uploadFileAndXmlNew(jsonStr, files);
  }

  @UseInterceptors(FilesInterceptor('file'))
  @Post(`invoice/:id/upload`)
  @ApiOperation({
    summary: 'upload AP file',
  })
  uploadFileWithInvoice(
      @Param('id') id: string,
      @Body() jsonStr: String,
      @UploadedFiles() files: Express.Multer.File[]
  ) {
    return this.fileUploadService.uploadWithInvoice(id, jsonStr, files);
  }

  @Post(`:id/ocr`)
  identifyInvoiceFileByFileId(@Param('id') id: string, @Body() param: any) {
    return this.fileUploadService.identifyInvoiceFileByFileId(id, param);
  }

  @Patch(`:id/comment`)
  updateComment(@Param('id') id: string, @Body() param: any) {
    return this.fileUploadService.updateComment(+id, param);
  }

  @Patch(`:id/xml/status`)
  updateXmlStatus(@Param('id') id: string, @Body() param: any) {
    return this.fileUploadService.updateXmlStatus(+id, param);
  }

  @Patch(`:id/ocr/status`)
  updateOcrStatus(@Param('id') id: string, @Body() param: any) {
    return this.fileUploadService.updateOcrStatus(+id, param);
  }

  @UseInterceptors(FilesInterceptor('file'))
  @Post('upload/es')
  @ApiOperation({
    summary: 'upload ES file only',
  })
  uploadEsFile(
      @Body() jsonStr: String,
      @UploadedFiles() files: Express.Multer.File[]
  ) {
    console.log('files: ', files);
    console.log('body: ', jsonStr);
    return this.fileUploadService.uploadEsFile(jsonStr, files);
  }

}
