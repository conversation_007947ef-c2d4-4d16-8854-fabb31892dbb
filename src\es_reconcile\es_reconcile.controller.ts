import {Body, Controller, Get, HttpException, HttpStatus, Logger, Param, Post, Query} from '@nestjs/common';
import {EsReconcileService} from './es_reconcile.service';
import {SelectEsReconcileDto} from "./dto/select_es_reconcile.dto";
import {ApiOperation, ApiResponse, ApiTags} from "@nestjs/swagger";
import {ResponseEsReconcileDto} from "./dto/response_es_reconcile.dto";
import {Paginated} from "../common/utils/index.util";
import {ReconcileSubmitDto} from "./dto/reconcile_submit.dto";
import {ReconciliationHistory} from "./entities/reconciliation_history.entity";
import {ManualEsReconcileDto} from "./dto/manual_es_reconcile.dto";
import {LogService} from "../log/log.service";
import {ReconcileSubmitSpDto} from "./dto/reconcile_submit_sp.dto";
import {ReconcileSubmitEeDto} from "./dto/reconcile_submit_ee.dto";

@ApiTags('Es-Reconcile')
@Controller('es-reconcile')
export class EsReconcileController {
    private readonly logger = new Logger(EsReconcileController.name);

    constructor(private readonly esReconcileService: EsReconcileService, public logService: LogService) {
    }


    @Get("/list")
    @ApiOperation({summary: 'Search e-statement list with automatic matching'})
    @ApiResponse({status: 200, description: 'Ok'})
    async findReconcileList(@Query() params: SelectEsReconcileDto): Promise<Paginated<ResponseEsReconcileDto>> {
        try {
            return this.esReconcileService.findEsReconcileList(params);
        } catch (e) {
            this.logger.log(`[es_reconcile_list] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_list] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }

    @Get("/single")
    @ApiOperation({summary: 'Search e-statement list with automatic matching'})
    @ApiResponse({status: 200, description: 'Ok'})
    async findReconcileById(@Query() params: any): Promise<ResponseEsReconcileDto> {
        try {
            return this.esReconcileService.findEsReconcileById(params);
        } catch (e) {
            this.logger.log(`[es_reconcile_list] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_list] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }

    @Post("/submit")
    @ApiOperation({summary: 'E-statement and invoice reconciliation and send information to the engine'})
    @ApiResponse({status: 201, description: 'Created'})
    async reconcileSubmit(@Body() submitDto: ReconcileSubmitDto): Promise<ReconciliationHistory[]> {
        try {
            if (submitDto.invoice_list.length === 0) {
                throw new HttpException('Invoice_list can not be empty', HttpStatus.BAD_REQUEST);
            }
            if (submitDto.charge_fee != null && submitDto.charge_fee != 0.00 && submitDto.charge_coa == null) {
                throw new HttpException('Charge Coa can not be empty', HttpStatus.BAD_REQUEST);
            }
            if (submitDto.charge_fee == null) submitDto.charge_fee = 0.00
            return await this.esReconcileService.reconcileSubmit(submitDto);
        } catch (e) {
            this.logger.log(`[es_reconcile_submit] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_submit] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }

    @Post("/submit/integration")
    @ApiOperation({summary: 'E-statement and invoice reconciliation and send information to the engine'})
    @ApiResponse({status: 201, description: 'Created'})
    async reconcileSubmitIntegration(@Body() submitDto: ReconcileSubmitDto): Promise<ReconciliationHistory[]> {
        try {
            if (submitDto.invoice_list.length === 0) {
                throw new HttpException('Invoice_list can not be empty', HttpStatus.BAD_REQUEST);
            }
            if (submitDto.charge_fee != null && submitDto.charge_fee != 0.00 && submitDto.charge_coa == null) {
                throw new HttpException('Charge Coa can not be empty', HttpStatus.BAD_REQUEST);
            }
            if (submitDto.charge_fee == null) submitDto.charge_fee = 0.00
            return await this.esReconcileService.reconcileSubmitIntegration(submitDto);
        } catch (e) {
            this.logger.log(`[es_reconcile_submit] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_submit] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }

    @Post("/submit/sp")
    @ApiOperation({summary: 'SP reconcile and send information to the engine'})
    @ApiResponse({status: 201, description: 'Created'})
    async reconcileSubmitSp(@Body() submitDto: ReconcileSubmitSpDto): Promise<ReconciliationHistory[]> {
        try {
            if (submitDto.invoice_list.length === 0) {
                throw new HttpException('Invoice_list can not be empty', HttpStatus.BAD_REQUEST);
            }
            await this.esReconcileService.validateSpParams(submitDto);
            return await this.esReconcileService.reconcileSubmit(submitDto);
        } catch (e) {
            this.logger.log(`[es_reconcile_submit_sp] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_submit_sp] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }

    @Post("/submit/ee")
    @ApiOperation({summary: 'EE reconcile GL'})
    @ApiResponse({status: 201, description: 'Created'})
    async reconcileSubmitEe(@Body() submitDto: ReconcileSubmitEeDto): Promise<ReconciliationHistory[]> {
        try {
            return await this.esReconcileService.reconcileSubmit(submitDto);
        } catch (e) {
            this.logger.log(`[es_reconcile_submit_ee] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_submit_ee] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }

    @Get("/manual")
    @ApiOperation({summary: 'Search e-statement with manual matching'})
    @ApiResponse({status: 200, description: 'Ok'})
    async findEsManualReconcile(@Query() params: ManualEsReconcileDto): Promise<ResponseEsReconcileDto> {
        try {
            return this.esReconcileService.findEsManualReconcile(params);
        } catch (e) {
            this.logger.log(`[es_reconcile_manual] ${e.message}, Error: ${JSON.stringify(e)}`);
            throw new HttpException(`[es_reconcile_manual] ${e.message}, Error: ${JSON.stringify(e)}`, e.status);
        }
    }
}
