import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";

export class PPType extends BkTypeStrategyService {

    /**
     * 自动对账，查找正数AP的票
     * @param params
     * @param es
     * @param usedMap
     * @param threshold
     */
    public async find(params, es, usedMap, threshold): Promise<MatchDto[]> {
        const aps = (await this.findAps(params, es))
            .filter(ap => (new Decimal(ap.balance).comparedTo(new Decimal(es.balance).sub(es.charge_fee)) <= 0) && !usedMap.has(`${ap.id}_${ap.br_type}`));
        console.log('Es id: '+ es.id);
        return this.getReconciliationCombination(aps, new Decimal(es.balance).sub(es.charge_fee).toNumber(), true, threshold, usedMap);
    }

    /**
     * 手动对账
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return this.findAps(params, es);
    }

    private async findAps(params, es: Es): Promise<MatchDto[]> {
        if (params.company_code != '8001' && params.company_code != '65QW' && params.company_code != '9999' && params.company_code != '38LR' && params.company_code != '8888') {
            return (await this.findApForBrMatch(params, true, es.br_type))
                // .filter(ar => (new Decimal(ar.balance).comparedTo(es.balance) <= 0) && ar.invoice_currency === es.currency.toString())
                .filter(ar => (new Decimal(ar.balance).comparedTo(0) > 0) && ar.invoice_currency === es.currency)
                .map(ar => this.convertToMatchDto(ar));
        } else {
            // integration
            return (await this.findApForBrMatch(params, true, es.br_type))
                // .filter(ar => (new Decimal(ar.balance).comparedTo(es.balance) <= 0) && ar.invoice_currency === es.currency.toString())
                .filter(ap => (new Decimal(ap.balance).comparedTo(0) != 0) && ap.invoice_currency === es.currency)
                .map(ap => this.convertToMatchDto(ap));
        }
    }

    public async getDrCr(submitDto) {

        if(BkTypeEnum.RP === submitDto.br_type.toString() || BkTypeEnum.PY === submitDto.br_type.toString()){
            return 'cr'
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of RP type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }
}