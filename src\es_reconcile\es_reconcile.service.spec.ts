import {Test, TestingModule} from '@nestjs/testing';
import {EsReconcileService} from './es_reconcile.service';
import {EsService} from "../es/es.service";
import {EsModule} from "../es/es.module";
import {TestDatabaseModule} from "../../test/dateModule.test";
import {SelectEsReconcileDto} from "./dto/select_es_reconcile.dto";
import {DataSource} from "typeorm";
import {ConfigModule} from "@nestjs/config";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {ReconcileSubmitDto} from "./dto/reconcile_submit.dto";
import {Ar} from "../ar/entities/ar.entity";
import {Es} from "../es/entities/es.entity";
import {HttpModule, HttpService} from "@nestjs/axios";
import {createMock} from "@golevelup/ts-jest";
import {ArService} from "../ar/ar.service";
import {ApService} from "../ap/ap.service";
import {WinstonModule} from "nest-winston";
import {LogModule} from "../log/log.module";

describe('EsReconcileService', () => {
    let service: EsReconcileService;
    let esService: EsService;
    let httpService: HttpService;
    // let dataSource: DataSource;
    let queryRunner: QueryRunner;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            imports: [EsModule, TestDatabaseModule, ConfigModule, HttpModule, WinstonModule, LogModule],
            providers: [EsReconcileService,
                // {
                //     provide: HttpService,
                //     useValue: {
                //         post: jest.fn().mockImplementation(() => of({data: {success: true}})),
                //     },
                //     // useValue: createMock<HttpService>()
                // },
                {
                    provide: ArService,
                    useValue: createMock<HttpService>()
                },
                {
                    provide: ApService,
                    useValue: createMock<HttpService>()
                },
            ],
        }).compile();

        service = module.get<EsReconcileService>(EsReconcileService);
        esService = module.get<EsService>(EsService);
        httpService = module.get<HttpService>(HttpService);
        // dataSource = module.get<DataSource>(DataSource);

        queryRunner = module.get<DataSource>(DataSource).createQueryRunner();
        // await queryRunner.startTransaction();
    });


    afterEach(async () => {
        // await queryRunner.rollbackTransaction();
        await queryRunner.connection.destroy();
    })

    // it('find_ar_list', async () => {
    //     const params = {
    //         "company_id": "202",
    //         "start_date": "2022-10-01",
    //         "end_date": "2022-10-30",
    //         "sort": {
    //             "date": "desc"
    //         }
    //     }
    //     const res = await service.findArForBrMatch(params, true);
    //     console.log(res)
    //     // expect(service).toBeDefined();
    // });


    it('find_Ar_Combinations', async () => {
        const params = {
            "company_id": "202",
            "start_date": "2022-10-01",
            "end_date": "2022-10-30",
            "sort": {
                "date": "desc"
            }
        }
        const es = {
            balance: 100
        }
        // @ts-ignore
        const res = await service.findArCombinations(params, es, new Map<string, number>());
        console.log(res)
        // expect(service).toBeDefined();
    });


    it('get_Reconciliation_Combination', async () => {
        const nums = [{id: 1, balance: 40.01}, {id: 2, balance: 59.98}]
        const es = {
            balance: 99.99
        }
        // @ts-ignore
        const res = service.getReconciliationCombination(nums, es.balance, true, 5, new Map<string, number>());
        console.log(res)
        expect(res.length).toBe(1);
    });


    it('get_Reconciliation_Combination_for_3_ars', async () => {
        const nums = [{id: 1, balance: 40.01}, {id: 2, balance: 33.98}, {id: 3, balance: 12.34}]
        const es = {
            balance: 86.33
        }
        // @ts-ignore
        const res = service.getReconciliationCombination(nums, es.balance, true, 5, new Map<string, number>());
        console.log(res)
        expect(res.length).toBe(1);
    });

    it('get_Reconciliation_Combination_for_3_ars_with_2_thread', async () => {
        const nums = [{id: 1, balance: 40.01}, {id: 2, balance: 33.98}, {id: 3, balance: 12.34}]
        const es = {
            balance: 86.33
        }
        // @ts-ignore
        const res = service.getReconciliationCombination(nums, es.balance, true, 2, new Map<string, number>());
        console.log(res)
        expect(res.length).toBe(0);
    });


    it('find_es_list', async () => {
        let params = new SelectEsReconcileDto(20, 1);
        params.company_code = "3000";
        params.start_date = "2022-10-01";
        params.end_date = "2022-10-30";
        const res = await service.findEsList(params);
        console.log(res)
        // expect(res.length).toBe(0);
    });


    it('find_EsReconcile_List', async () => {
        let params = new SelectEsReconcileDto(20, 1);
        params.company_code = "3000";
        params.start_date = "2022-10-01";
        params.end_date = "2022-10-30";
        const res = await service.findEsReconcileList(params);
        console.log(res)
        // expect(res.data.length).toBe(3);

        //134.45
    });

    it('submit_3_ar_with_1_es_for_full_reconcile', async function () {

        //initialize ar, es
        const ar_sql_1 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel,
                                          company_email, company_gst_no, company_pst_no, invoice_no, reference_no,
                                          invoice_currency, pay_method, invoice_create_date, invoice_due_date,
                                          posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company,
                                          bill_to_street, bill_to_city, bill_to_province, bill_to_country,
                                          bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver,
                                          ship_to_company, ship_to_street, ship_to_city, ship_to_province,
                                          ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount,
                                          gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url,
                                          bank_id, bank_account, bank_name, create_time, creator, update_time,
                                          deleted_time, engine_document_id, company_logo)
                          VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********',
                                  '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944',
                                  '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********,
                                  'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '',
                                  '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, 65.03, 6697.75, null, 65.03,
                                  '', 0, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '',
                                  '2022-11-28 11:00:44.201802', '828', '2022-12-07 12:10:11.407302', null, null,
                                  '/bookkeeping/logo/3000.jpg')`;
        const ar_sql_2 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel,
                                          company_email, company_gst_no, company_pst_no, invoice_no, reference_no,
                                          invoice_currency, pay_method, invoice_create_date, invoice_due_date,
                                          posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company,
                                          bill_to_street, bill_to_city, bill_to_province, bill_to_country,
                                          bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver,
                                          ship_to_company, ship_to_street, ship_to_city, ship_to_province,
                                          ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount,
                                          gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url,
                                          bank_id, bank_account, bank_name, create_time, creator, update_time,
                                          deleted_time, engine_document_id, company_logo)
                          VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********',
                                  '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944',
                                  '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********,
                                  'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '',
                                  '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, 200.75, 6697.75, null, 200.75,
                                  '', 0, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '',
                                  '2022-11-28 11:00:44.201802', '828', '2022-12-07 12:10:11.833224', null, null,
                                  '/bookkeeping/logo/3000.jpg')`;
        const ar_sql_3 = `INSERT INTO ar (company_id, company_code, company_name, company_address, company_tel,
                                          company_email, company_gst_no, company_pst_no, invoice_no, reference_no,
                                          invoice_currency, pay_method, invoice_create_date, invoice_due_date,
                                          posting_date, bill_to_customer_id, bill_to_receiver, bill_to_company,
                                          bill_to_street, bill_to_city, bill_to_province, bill_to_country,
                                          bill_to_postal_code, bill_to_tel, bill_to_email, ship_to_receiver,
                                          ship_to_company, ship_to_street, ship_to_city, ship_to_province,
                                          ship_to_country, ship_to_postal_code, ship_to_tel, ship_to_email, net_amount,
                                          gst, pst, qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, send_email_status, invoice_url,
                                          bank_id, bank_account, bank_name, create_time, creator, update_time,
                                          deleted_time, engine_document_id, company_logo)
                          VALUES (201, '3000', '9465-4662 Quebec Inc.', '1313 rue des seigneurs', '**********',
                                  '<EMAIL>', '********** RT0002', '122 7058541 TQ0001', '300020221128100042944',
                                  '*********', '2', '4', '2022-09-23', '2022-09-23', '2022-09-23', ********,
                                  'eweewe ew', '5599', '12', '', '', '', '', '', '', '', '5599', '', '', '', null, '',
                                  '', '<EMAIL>', 5808.00, 290.40, 579.35, 0.00, 869.75, 99.95, 6697.75, null, 99.95,
                                  '', 0, 0, 0, '/bookkeeping/invoice/3000_300020221128100042944.pdf', null, '', '',
                                  '2022-11-28 11:00:44.201802', '828', '2022-12-07 12:10:12.202299', null, null,
                                  '/bookkeeping/logo/3000.jpg')`;
        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit,
                                          balance, bank_account, statement_period, statement_type, br_type, br_flag,
                                          plaid_transaction_id, match_flag, creator, create_time, update_time,
                                          deleted_time, currency, company_code)
                          VALUES (51, '2022-11-25', 'INTERAC ETRNSFR SENT GROCEGO DISTRIBUTION INC 2022329104377CCBD',
                                  null, null, 0.00, 365.73, 365.73, '123456', null, '1', 1, 0,
                                  'RxQrdgQ50PuVZ4j5Jw7DS8AjKLXd3qf1OeOaO', 0, null, '2022-11-30 12:35:17.587796',
                                  '2022-12-07 12:10:11.138937', null, 2, '3000');`;

        const ar1 = await queryRunner.query(ar_sql_1);
        const ar2 = await queryRunner.query(ar_sql_2);
        const ar3 = await queryRunner.query(ar_sql_3);
        const es = await queryRunner.query(es_sql_1);
        let histories;

        try {
            const params = {
                "company_code": "3000",
                "statement_id": es.insertId,
                "invoice_type": 1,
                "payment_date": "2022-11-11",
                "posting_date": "2022-11-11",
                "currency": 2,
                "bank_account": "451-90355-********",
                "creator": "201",
                "balance": 365.73,
                "invoice_list": [{
                    "invoice_id": ar1.insertId,
                    "bp_number": "********",
                    "balance": 65.03,
                    "reconcile_amount": 65.03
                }, {
                    "invoice_id": ar2.insertId,
                    "bp_number": "********",
                    "balance": 200.75,
                    "reconcile_amount": 200.75
                }, {"invoice_id": ar3.insertId, "bp_number": "********", "balance": 99.95, "reconcile_amount": 99.95}]
            }
            const dto = Object.assign(new ReconcileSubmitDto(), params);

            histories = await service.reconcileSubmit(dto);
            const arRes: Ar[] = await queryRunner.query(`select *
                                                         from ar
                                                         where id in (${ar1.insertId}, ${ar2.insertId}, ${ar3.insertId})`);
            const esRes: Es[] = await queryRunner.query(`select *
                                                         from es
                                                         where id in (${es.insertId})`);
            expect(arRes[0].balance).toBe("0.00");
            expect(arRes[0].br_flag).toBe(2);
            expect(arRes[1].balance).toBe("0.00");
            expect(arRes[1].br_flag).toBe(2);
            expect(arRes[2].balance).toBe("0.00");
            expect(arRes[2].br_flag).toBe(2);
            expect(esRes[0].balance).toBe("0.00");
            expect(esRes[0].br_flag).toBe(2);

            expect(histories[0]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "65.03",
                "br_id": histories[0].br_id,
                "br_type": 1,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[0].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "65.03",
                "reverse_number": 0,
                "transaction_id": histories[0].transaction_id,
            }));
            expect(histories[1]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "200.75",
                "br_id": histories[1].br_id,
                "br_type": 1,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[1].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "200.75",
                "reverse_number": 0,
                "transaction_id": histories[1].transaction_id,
            }));
            expect(histories[2]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "99.95",
                "br_id": histories[2].br_id,
                "br_type": 1,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[2].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "99.95",
                "reverse_number": 0,
                "transaction_id": histories[0].transaction_id,
            }));
            expect(histories[3]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "365.73",
                "br_id": histories[3].br_id,
                "br_type": 3,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[3].id,
                "max_reverse_number": 3,
                "posting_date": "2022-11-11",
                "reconcile_amount": "365.73",
                "reverse_number": 0,
                "transaction_id": histories[3].transaction_id,
            }));
        } finally {
            await queryRunner.query(`delete
                                     from ar
                                     where id in (${ar1.insertId}, ${ar2.insertId}, ${ar3.insertId})`);
            await queryRunner.query(`delete
                                     from es
                                     where id in (${es.insertId})`);
            await queryRunner.query(`delete
                                     from reconciliation_history_v2
                                     where transaction_id = '${histories[0].transaction_id}'`);
        }
    }, 70000);


    it('submit_4_ap_with_1_es_for_full_reconcile', async function () {

        //initialize ar, es
        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '3010', 30105083, 'ULTRAMAR', null, null, null, '3.0102022062916567e24',
                                  '20220405', '2', '1', '2022-06-29', '2022-06-29', '2022-04-05', 69.58, 3.48, 0.00,
                                  6.94, 10.42, 500.75, null, null, 500.75, null, 0, 1, null, null, null, null, null,
                                  null, null, null, null, null, '2022-06-29 16:02:09', '840',
                                  '2022-12-07 16:33:15.849043', null, null);`;
        const ap_sql_2 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '2068', 30105083, null, null, null, null, '2.0682022062916564e24', null, '2',
                                  '2', '2022-06-29', '2022-06-29', '2022-04-07', 47.30, 0.96, 0.00, 1.92, 0.00, 23.45,
                                  null, null, 23.45, null, 0, 0, null, null, null, null, null, null, null, null, null,
                                  null, '2022-06-29 16:02:27', '838', '2022-12-07 16:33:16.065949', null, null);`;
        const ap_sql_3 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '2068', 30105083, null, null, null, null, '2.0682022062916564e24', null, '2',
                                  '2', '2022-06-29', '2022-06-29', '2022-04-21', 84.77, 1.15, 0.00, 2.29, 0.00, 233.03,
                                  null, null, 233.03, null, 0, 0, null, null, null, null, null, null, null, null, null,
                                  null, '2022-06-29 16:03:19', '838', '2022-12-07 16:33:16.495502', null, null);`;
        const ap_sql_4 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '2068', 30105083, null, null, null, null, '2.0682022062916564e24', null, '2',
                                  '2', '2022-06-29', '2022-06-29', '2022-04-27', 435.75, 3.52, 0.00, 7.02, 0.00, 267.20,
                                  null, null, 267.20, null, 0, 0, null, null, null, null, null, null, null, null, null,
                                  null, '2022-06-29 16:05:26', '838', '2022-12-07 16:33:16.865352', null, null);`;
        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit,
                                          balance, bank_account, statement_period, statement_type, br_type, br_flag,
                                          plaid_transaction_id, match_flag, creator, create_time, update_time,
                                          deleted_time, currency, company_code)
                          VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 1024.43,
                                  '123456', null, '1', 2, 0, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null,
                                  '2022-11-30 12:35:17.613703', '2022-12-07 16:33:15.487189', null, 2, '3000'); `;

        const ap1 = await queryRunner.query(ap_sql_1);
        const ap2 = await queryRunner.query(ap_sql_2);
        const ap3 = await queryRunner.query(ap_sql_3);
        const ap4 = await queryRunner.query(ap_sql_4);
        const es = await queryRunner.query(es_sql_1);
        let histories;

        try {
            const params = {
                "company_code": "3000",
                "statement_id": es.insertId,
                "invoice_type": 2,
                "payment_date": "2022-11-11",
                "posting_date": "2022-11-11",
                "currency": 2,
                "bank_account": "451-90355-********",
                "creator": "201",
                "balance": 1024.43,
                "invoice_list": [{
                    "invoice_id": ap1.insertId,
                    "bp_number": "********",
                    "balance": 500.75,
                    "reconcile_amount": 500.75
                }, {
                    "invoice_id": ap2.insertId,
                    "bp_number": "********",
                    "balance": 23.45,
                    "reconcile_amount": 23.45
                }, {
                    "invoice_id": ap3.insertId,
                    "bp_number": "********",
                    "balance": 233.03,
                    "reconcile_amount": 233.03
                }, {"invoice_id": ap4.insertId, "bp_number": "********", "balance": 267.20, "reconcile_amount": 267.20}]
            };
            const dto = Object.assign(new ReconcileSubmitDto(), params);

            histories = await service.reconcileSubmit(dto);
            const apRes = await queryRunner.query(`select *
                                                   from ap
                                                   where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            const esRes = await queryRunner.query(`select *
                                                   from es
                                                   where id in (${es.insertId})`);

            expect(apRes[0].balance).toBe("0.00");
            expect(apRes[0].br_flag).toBe(2);
            expect(apRes[1].balance).toBe("0.00");
            expect(apRes[1].br_flag).toBe(2);
            expect(apRes[2].balance).toBe("0.00");
            expect(apRes[2].br_flag).toBe(2);
            expect(apRes[3].balance).toBe("0.00");
            expect(apRes[3].br_flag).toBe(2);

            expect(esRes[0].balance).toBe("0.00");
            expect(esRes[0].br_flag).toBe(2);

            expect(histories[0]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "500.75",
                "br_id": histories[0].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[0].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "500.75",
                "reverse_number": 0,
            }));
            expect(histories[1]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "23.45",
                "br_id": histories[1].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "id": histories[1].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "23.45",
                "reverse_number": 0,
            }));
            expect(histories[2]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "233.03",
                "br_id": histories[2].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[2].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "233.03",
                "reverse_number": 0,
            }));
            expect(histories[3]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "267.20",
                "br_id": histories[3].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "id": histories[3].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "267.20",
                "reverse_number": 0,
            }));
            expect(histories[4]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "1024.43",
                "br_id": histories[4].br_id,
                "br_type": 3,
                "company_code": "3000",
                "creator": "201",
                "id": histories[4].id,
                "max_reverse_number": 4,
                "posting_date": "2022-11-11",
                "reconcile_amount": "1024.43",
                "reverse_number": 0,
            }));
        } finally {
            await queryRunner.query(`delete
                                     from ar
                                     where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            await queryRunner.query(`delete
                                     from es
                                     where id in (${es.insertId})`);
            await queryRunner.query(`delete
                                     from reconciliation_history_v2
                                     where transaction_id = '${histories[0].transaction_id}'`);
        }
    }, 70000);

    it('submit_4_ap_with_1_es_for_partial_reconcile', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '3010', 30105065, 'RONA', null, null, null, '3.0102022062916567e24', '20220407',
                                  '2', '1', '2022-06-29', '2022-06-29', '2022-04-07', 15.63, 0.78, 0.00, 1.56, 2.34,
                                  500.75, null, null, 500.75, null, 0, 1, null, null, null, null, null, null, null,
                                  null, null, null, '2022-06-29 16:05:58', '840', '2022-12-02 22:11:09', null, null);`;
        const ap_sql_2 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '3010', 30105051, 'LES EMBALLAGES MULTIDESIGN', null, null, null,
                                  '3.0102022062916567e24', '44751', '2', '1', '2022-06-29', '2022-06-29', '2022-04-07',
                                  554.08, 27.70, 0.00, 55.27, 82.97, 23.45, null, null, 23.45, null, 0, 1, null, null,
                                  null, null, null, null, null, null, null, null, '2022-06-29 16:07:01', '840',
                                  '2022-12-02 22:11:10', null, null);`;
        const ap_sql_3 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '3010', 30105012, 'AMAZON', null, null, null, '3.0102022062916567e24',
                                  '20220424', '2', '1', '2022-06-29', '2022-06-29', '2022-04-24', 32.99, 0.00, 0.00,
                                  0.00, 0.00, 233.03, null, null, 233.03, null, 0, 1, null, null, null, null, null,
                                  null, null, null, null, null, '2022-06-29 16:09:32', '840', '2022-12-02 22:11:11',
                                  null, null);`;
        const ap_sql_4 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '3010', 30105012, 'AMAZON', null, null, null, '3.0102022062916567e24',
                                  '20220425', '2', '1', '2022-06-29', '2022-06-29', '2022-04-25', 13.99, 0.00, 0.00,
                                  0.00, 0.00, 1220.20, null, null, 1220.20, null, 0, 1, null, null, null, null, null,
                                  null, null, null, null, null, '2022-06-29 16:11:26', '840', '2022-12-02 22:11:16',
                                  null, null);`;
        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit,
                                          balance, bank_account, statement_period, statement_type, br_type, br_flag,
                                          plaid_transaction_id, match_flag, creator, create_time, update_time,
                                          deleted_time, currency, company_code)
                          VALUES (51, '2022-11-25', 'INOSSEM CANADA CAP/CDE', null, null, 1024.43, 0.00, 1024.43,
                                  '123456', null, '1', 2, 0, 'QaEj0gEZv1cKyO3ZkX1LcR5aE6Adb4UJNONVX', 0, null,
                                  '2022-11-30 12:35:17.613703', '2022-12-06 15:56:48.532270', null, 2, '3000'); `;

        const ap1 = await queryRunner.query(ap_sql_1);
        const ap2 = await queryRunner.query(ap_sql_2);
        const ap3 = await queryRunner.query(ap_sql_3);
        const ap4 = await queryRunner.query(ap_sql_4);
        const es = await queryRunner.query(es_sql_1);
        let histories;
        const params = {
            "company_id": 201,
            "company_code": "3000",
            "statement_id": es.insertId,
            "invoice_type": 2,
            "payment_date": "2022-11-11",
            "posting_date": "2022-11-11",
            "currency": 2,
            "bank_account": "451-90355-********",
            "creator": "201",
            "balance": 1024.43,
            "invoice_list": [{
                "invoice_id": ap1.insertId,
                "bp_number": "********",
                "balance": 500.75,
                "reconcile_amount": 500.75
            }, {
                "invoice_id": ap2.insertId,
                "bp_number": "********",
                "balance": 23.45,
                "reconcile_amount": 23.45
            }, {
                "invoice_id": ap3.insertId,
                "bp_number": "********",
                "balance": 233.03,
                "reconcile_amount": 233.03
            }, {"invoice_id": ap4.insertId, "bp_number": "********", "balance": 1220.20, "reconcile_amount": 267.20}]
        };
        const dto = Object.assign(new ReconcileSubmitDto(), params);

        try {
            histories = await service.reconcileSubmit(dto);
            const apRes = await queryRunner.query(`select *
                                                   from ap
                                                   where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            const esRes = await queryRunner.query(`select *
                                                   from es
                                                   where id in (${es.insertId})`);

            expect(apRes[0].balance).toBe("0.00");
            expect(apRes[0].br_flag).toBe(2);
            expect(apRes[1].balance).toBe("0.00");
            expect(apRes[1].br_flag).toBe(2);
            expect(apRes[2].balance).toBe("0.00");
            expect(apRes[2].br_flag).toBe(2);
            expect(apRes[3].balance).toBe("953.00");
            expect(apRes[3].br_flag).toBe(1);

            expect(esRes[0].balance).toBe("0.00");
            expect(esRes[0].br_flag).toBe(2);
            expect(histories[0]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "500.75",
                "br_id": histories[0].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[0].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "500.75",
                "reverse_number": 0,
            }));
            expect(histories[1]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "23.45",
                "br_id": histories[1].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "id": histories[1].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "23.45",
                "reverse_number": 0,
            }));
            expect(histories[2]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "233.03",
                "br_id": histories[2].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "deleted_time": null,
                "id": histories[2].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "233.03",
                "reverse_number": 0,
            }));
            expect(histories[3]).toEqual(expect.objectContaining({
                "after_balance": "953.00",
                "after_br_flag": 1,
                "before_balance": "1220.20",
                "br_id": histories[3].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "id": histories[3].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "267.20",
                "reverse_number": 0,
            }));
            expect(histories[4]).toEqual(expect.objectContaining({
                "after_balance": "0.00",
                "after_br_flag": 2,
                "before_balance": "1024.43",
                "br_id": histories[4].br_id,
                "br_type": 3,
                "company_code": "3000",
                "creator": "201",
                "id": histories[4].id,
                "max_reverse_number": 4,
                "posting_date": "2022-11-11",
                "reconcile_amount": "1024.43",
                "reverse_number": 0,
            }));

        } finally {
            await queryRunner.query(`delete
                                     from ar
                                     where id in (${ap1.insertId}, ${ap2.insertId}, ${ap3.insertId}, ${ap4.insertId})`);
            await queryRunner.query(`delete
                                     from es
                                     where id in (${es.insertId})`);
            await queryRunner.query(`delete
                                     from reconciliation_history_v2
                                     where transaction_id = '${histories[0].transaction_id}'`);
        }
    }, 70000);

    it('submit_1_ap_with_1_es_for_both_partial_reconcile', async function () {

        const ap_sql_1 = `INSERT INTO ap (company_id, company_code, issuer_id, issuer_name, issuer_address, issuer_tel,
                                          issuer_email, invoice_no, reference_no, invoice_currency, pay_method,
                                          invoice_create_date, invoice_due_date, posting_date, net_amount, gst, pst,
                                          qst, total_tax, total_fee, total_fee_cad, exchange_rate, balance,
                                          invoice_comments, br_flag, send_engine_status, file_id, file_page_index,
                                          file_url, po, payment_terms_day_1, payment_terms_day_2, payment_terms_day_3,
                                          payment_terms_discount_1, payment_terms_discount_2, payment_terms_discount_3,
                                          create_time, creator, update_time, deleted_time, engine_document_id)
                          VALUES (201, '3010', 30105012, 'AMAZON', null, null, null, '3.0102022062916567e24',
                                  '20220425', '2', '1', '2022-06-29', '2022-06-29', '2022-04-25', 13.99, 0.00, 0.00,
                                  0.00, 0.00, 1220.20, null, null, 953.00, null, 1, 1, null, null, null, null, null,
                                  null, null, null, null, null, '2022-06-29 16:11:26', '840',
                                  '2022-12-07 21:08:59.066529', null, null); `;
        const es_sql_1 = `INSERT INTO es (file_id, date, description, reference, payer_payee, withdrawal, deposit,
                                          balance, bank_account, statement_period, statement_type, br_type, br_flag,
                                          plaid_transaction_id, match_flag, creator, create_time, update_time,
                                          deleted_time, currency, company_code)
                          VALUES (51, '2022-11-18', 'INOSSEM CANADA CAP/CDE', null, null, 2989.37, 0.00, 2989.37,
                                  '123456', null, '1', 2, 0, 'rDAqdaAjyKTjOJ8NR5Aah9EEAMPVDJUrMdMPX', 0, null,
                                  '2022-11-30 12:35:17.397377', '2022-12-06 15:56:48.401902', null, 2, '3000'); `;

        const ap1 = await queryRunner.query(ap_sql_1);
        const es = await queryRunner.query(es_sql_1);

        let histories;
        const params = {
            "company_code": "3000",
            "statement_id": es.insertId,
            "invoice_type": 2,
            "payment_date": "2022-11-11",
            "posting_date": "2022-11-11",
            "currency": 2,
            "bank_account": "451-90355-********",
            "creator": "201",
            "balance": 2989.37,
            "invoice_list": [{
                "invoice_id": ap1.insertId,
                "bp_number": "********",
                "balance": 953.00,
                "reconcile_amount": 553.00
            }]
        };
        const dto = Object.assign(new ReconcileSubmitDto(), params);

        try {
            histories = await service.reconcileSubmit(dto);
            const apRes = await queryRunner.query(`select *
                                                   from ap
                                                   where id in (${ap1.insertId})`);
            const esRes = await queryRunner.query(`select *
                                                   from es
                                                   where id in (${es.insertId})`);

            expect(apRes[0].balance).toBe("400.00");
            expect(apRes[0].br_flag).toBe(1);

            expect(esRes[0].balance).toBe("2436.37");
            expect(esRes[0].br_flag).toBe(1);

            expect(histories[0]).toEqual(expect.objectContaining({
                "after_balance": "400.00",
                "after_br_flag": 1,
                "before_balance": "953.00",
                "br_id": histories[0].br_id,
                "br_type": 2,
                "company_code": "3000",
                "creator": "201",
                "id": histories[0].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "553.00",
                "reverse_number": 0,
            }));
            expect(histories[1]).toEqual(expect.objectContaining({
                "after_balance": "2436.37",
                "after_br_flag": 1,
                "before_balance": "2989.37",
                "br_id": histories[1].br_id,
                "br_type": 3,
                "company_code": "3000",
                "creator": "201",
                "id": histories[1].id,
                "max_reverse_number": 1,
                "posting_date": "2022-11-11",
                "reconcile_amount": "553.00",
                "reverse_number": 0,
            }));
        } finally {
            await queryRunner.query(`delete
                                     from ap
                                     where id in (${ap1.insertId})`);
            await queryRunner.query(`delete
                                     from es
                                     where id in (${es.insertId})`);
            await queryRunner.query(`delete
                                     from reconciliation_history_v2
                                     where transaction_id = '${histories[0].transaction_id}'`);
        }
    }, 70000);


    it('sendToEngin', async function () {

        const data = {
            "posting_date": '04-08-1980',
            "post_check": true,
            "clearing_line_items": [
                {
                    "document_no": "3002-2022-22",
                    "line_no": "1"
                }
            ],
            "new_line_items": [
                {
                    "gl_account": "10101",
                    "amount_tc": '34.23',
                    "dr_cr": "dr",
                    "neg_posting": false,
                }
            ]
        }
        // jest.spyOn(httpService, 'post').mockImplementationOnce(() => of({s:SelectEsReconcileDto}));
        await service.sendToEngine(data, null, null, 'http://20.151.175.32/bk/clear-post-journal-entry');
    }, 70000);

});
