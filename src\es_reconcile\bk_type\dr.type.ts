import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";
import { Ap } from "src/ap/entities/ap.entity";
import { ReconciliationHistory } from "../entities/reconciliation_history.entity";

export class DRType extends BkTypeStrategyService {

    /**
     * 自动对账，查找正数AP的票
     * @param params
     * @param es
     * @param usedMap
     * @param threshold
     */
    public async find(params, es, usedMap, threshold): Promise<MatchDto[]> {
        const aps = (await this.findApsForDepositReturn(params, es))
            .filter(ap => (new Decimal(ap.balance).comparedTo(new Decimal(es.balance).sub(es.charge_fee)) <= 0) && !usedMap.has(`${ap.id}_${ap.br_type}`));
        console.log('Es id: '+ es.id);
        return this.getReconciliationCombination(aps, new Decimal(es.balance).sub(es.charge_fee).toNumber(), true, threshold, usedMap);
    }

    /**
     * 手动对账
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        if (es.withdrawal > 0) {
            // withdraw有值，定金流水对账匹配
            return this.findApsForDeposit(params, es);
        } else {
            // deposit有值，定金返还流水对账匹配
            return this.findApsForDepositReturn(params, es);
        }
    }

    // 定金流水对账匹配
    private async findApsForDeposit(params, es: Es): Promise<MatchDto[]> {
        return (await this.findApForBrMatch(params, true, es.br_type))
            // .filter(ar => (new Decimal(ar.balance).comparedTo(es.balance) <= 0) && ar.invoice_currency === es.currency.toString())
            .filter(ap => (
                            new Decimal(ap.balance).comparedTo(0) != 0 
                            && ap.invoice_currency === es.currency
                            && (ap.br_flag === 0 || ap.br_flag === 1)
                            && ap.purpose === 'DEPOSIT_AND_RETURN'
                          )
                    )
            .map(ap => this.convertToMatchDto(ap));
    }
    // 定金返还流水对账匹配
    private async findApsForDepositReturn(params, es: Es): Promise<MatchDto[]> {
        const reconcileCountSubquery = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .select("rh.br_id")
            .addSelect("COUNT(rh.br_id)", "count")
            .where("rh.company_code = :company_code", {company_code: params.company_code})
            .andWhere("rh.br_entity_type = 1") // AP类型
            .andWhere("rh.deleted_time IS NULL")
            .andWhere("rh.sap_document_no IS NOT NULL")
            .andWhere("rh.sap_reverse_document_no IS NULL")
            .andWhere("rh.br_type = :br_type", {br_type: es.br_type})
            .groupBy("rh.br_id")
            .having("COUNT(rh.br_id) = 1")
            .getRawMany();
        const onceReconciledApIds = reconcileCountSubquery.map(item => item.rh_br_id);

        return (await this.findApForBrMatch(params, true, es.br_type))
            // .filter(ar => (new Decimal(ar.balance).comparedTo(es.balance) <= 0) && ar.invoice_currency === es.currency.toString())
            .filter(ap => (
                            new Decimal(ap.balance).comparedTo(0) == 0
                            && ap.invoice_currency === es.currency
                            && ap.br_flag === 2
                            && ap.purpose === 'DEPOSIT'
                            && onceReconciledApIds.includes(ap.id)
                          )
                    )
            .map(ap => this.convertToMatchDto(ap));
    }

    public async getDrCr(submitDto) {

        if(BkTypeEnum.RP === submitDto.br_type.toString() || BkTypeEnum.PY === submitDto.br_type.toString()){
            return 'cr'
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of RP type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }
}