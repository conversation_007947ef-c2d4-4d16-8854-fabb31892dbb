import {
    Column,
    CreateDateColumn,
    DeleteDateColumn,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

export class BaseEntity {
    // 主键id
    @PrimaryGeneratedColumn()
    id: number;

    // 创建时间
    @CreateDateColumn()
    create_time: Date;

    @Column({nullable: true})
        // 创建人
    creator: string;

    // 更新时间
    @UpdateDateColumn()
    update_time: Date;

    // 逻辑删除
    @DeleteDateColumn()
    deleted_time: Date;

}
