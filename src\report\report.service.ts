import {BadRequestException, HttpException, Injectable, Logger} from '@nestjs/common';
import {BaseService} from "../common/service/base.service";
import {Ar} from "../ar/entities/ar.entity";
import {Ap} from "../ap/entities/ap.entity";
import {InjectRepository} from "@nestjs/typeorm";
import {Between, In, IsNull, Not, Repository} from "typeorm";
import {ArService} from "../ar/ar.service";
import {ApService} from "../ap/ap.service";
import {ReportInvoiceDto} from "./dto/report_invoice.dto";
import {ReportHeaderDto} from "./dto/report_header.dto";
import {ReportDetailItemDto} from "./dto/report_detail_item.dto";
import {Decimal} from "decimal.js";
import {ReportDetailDto} from "./dto/report_detail.dto";
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import {resolve} from "dns";
import {ReportDetailSummaryDto} from "./dto/report_detail_summary.dto";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {BkTypeEnum} from "../common/enum/bk.type.enum";

@Injectable()
export class ReportService {
    private readonly logger = new Logger(ReportService.name);
    private defaultDate = '1900-01-01';
    private status_receive = 'Receive';
    private status_payment = 'Payment';
    private status_invoice = 'Invoice';
    constructor(
        @InjectRepository(Ar) private readonly arRepository: Repository<Ar>,
        @InjectRepository(Ap) private readonly apRepository: Repository<Ap>,
        @InjectRepository(ReconciliationHistory) private readonly reconciliationHistory: Repository<ReconciliationHistory>,
    ) {
    }

    async reportArApToJson(params: any) {
        // valid params
        this.validReportArApParams(params);

        // get list
        let invoiceList;
        if (params.report_type === 'AR') invoiceList = await this.getReportArReconciles(params);
        if (params.report_type === 'AP') invoiceList = await this.getReportApReconciles(params);

        if (!invoiceList) throw new BadRequestException('No invoices exist.')
        let reportList = this.generateReportArApList(invoiceList, params);

        return reportList
    }

    async reportSalesPurchaseToJson(params: any) {
        // valid params
        this.validReportArApParams(params);

        // get list
        let invoiceList = [];
        if (params.report_type === 'AR') invoiceList = await this.getReportArInvoices(params);
        if (params.report_type === 'AP') invoiceList = await this.getReportApInvoices(params);
        // handle return value
        let salesPurchaseList = [];
        let summary = [];
        for (let item of invoiceList) {
            let findGst, findPst, findQst;
            // handle tax content
            if (item.tax_content) {
                findGst = item.tax_content.find(element => element.fieldName === 'GST');
                findPst = item.tax_content.find(element => element.fieldName === 'PST');
                findQst = item.tax_content.find(element => element.fieldName === 'QST');
            }
            salesPurchaseList.push({
                posting_date: item.posting_date,
                engine_doc_no: item.engine_doc_no,
                bp_no: item.bp_no,
                bp_name: item.bp_name,
                currency: item.invoice_currency,
                net_amount: item.net_amount,
                gst: findGst ? findGst.value : 0,
                pst: findPst ? findPst.value : 0,
                qst: findQst ? findQst.value : 0,
                total_tax: item.total_tax,
                total_fee: item.total_fee
            });

            let currentSummaryItem = summary.find(summaryItem => item.invoice_currency === summaryItem.currency);
            if (currentSummaryItem) {
                currentSummaryItem.net_amount = this.add(currentSummaryItem.net_amount, item.net_amount);
                currentSummaryItem.gst = this.add(currentSummaryItem.gst, findGst ? findGst.value : 0);
                currentSummaryItem.pst = this.add(currentSummaryItem.pst, findPst ? findPst.value : 0);
                currentSummaryItem.qst = this.add(currentSummaryItem.qst, findQst ? findQst.value : 0);
                currentSummaryItem.total_tax = this.add(currentSummaryItem.total_tax, item.total_tax);
                currentSummaryItem.total_fee = this.add(currentSummaryItem.total_fee, item.total_fee);
            }else {
                summary.push(
                    {
                        currency: item.invoice_currency,
                        net_amount: item.net_amount,
                        gst: findGst ? findGst.value : 0,
                        pst: findPst ? findPst.value : 0,
                        qst: findQst ? findQst.value : 0,
                        total_tax: item.total_tax,
                        total_fee: item.total_fee
                    }
                )
            }
        }

        return {
            company_code: params.company_code,
            company_name: params.company_name,
            report_type: params.report_type,
            posting_period: params.start_date == this.defaultDate ? `As of ${params.end_date}` : `${params.start_date} to ${params.end_date}`,
            summary: summary,
            list: salesPurchaseList
        };
    }

    async reportIntegrationSalesPurchaseToJson(params: any) {
        // valid params
        this.validReportArApParams(params);

        // get list
        let invoiceList = [];
        if (params.report_type === 'AR') invoiceList = await this.getReportArInvoices(params);
        if (params.report_type === 'AP') invoiceList = await this.getReportApInvoices(params);
        // handle return value
        let salesPurchaseList = [];
        let summary = [];
        for (let item of invoiceList) {
            // let findGst, findPst, findQst;
            // handle tax content
            const taxContent = item.tax_content.reduce((acc, item) => {
                acc[item.fieldName?.toLowerCase()] = item.value;
                return acc;
            }, {} as Record<string, number>);
            salesPurchaseList.push({
                id: item.id,
                posting_date: item.posting_date,
                engine_doc_no: item.engine_doc_no,
                sap_document_id: item.sap_document_id,
                bp_no: item.bp_no,
                bp_name: item.bp_name,
                currency: item.invoice_currency,
                net_amount: item.net_amount,
                discount: item.discount,
                mx_isr: item.mx_isr,
                mx_iva: item.mx_iva,
                total_tax: item.total_tax,
                total_fee: item.total_fee,
                ...taxContent
            });

            let currentSummaryItem = summary.find(summaryItem => item.invoice_currency === summaryItem.currency);
            if (currentSummaryItem) {
                currentSummaryItem.net_amount = this.add(currentSummaryItem.net_amount, item.net_amount);
                currentSummaryItem.discount = this.add(currentSummaryItem.discount || 0, item.discount || 0);
                currentSummaryItem.mx_isr = this.add(currentSummaryItem.mx_isr || 0, item.mx_isr || 0);
                currentSummaryItem.mx_iva = this.add(currentSummaryItem.mx_iva || 0, item.mx_iva || 0);
                currentSummaryItem.total_tax = this.add(currentSummaryItem.total_tax, item.total_tax);
                currentSummaryItem.total_fee = this.add(currentSummaryItem.total_fee, item.total_fee);
                item.tax_content.forEach(tax => {
                    const key = tax.fieldName?.toLowerCase();
                    currentSummaryItem[key] = this.add(currentSummaryItem[key] || 0, tax.value);
                });
            }else {
                const taxContent = item.tax_content.reduce((acc, item) => {
                    acc[item.fieldName?.toLowerCase()] = item.value;
                    return acc;
                }, {} as Record<string, number>);
                summary.push(
                    {
                        currency: item.invoice_currency,
                        net_amount: item.net_amount,
                        discount: item.discount || 0,
                        mx_isr: item.mx_isr || 0,
                        mx_iva: item.mx_iva || 0,
                        total_tax: item.total_tax,
                        total_fee: item.total_fee,
                        ...taxContent
                    }
                )
            }
        }
        return {
            company_code: params.company_code,
            company_name: params.company_name,
            report_type: params.report_type,
            posting_period: params.start_date == this.defaultDate ? `As of ${params.end_date}` : `${params.start_date} to ${params.end_date}`,
            summary: summary,
            list: salesPurchaseList
        };
    }

    async reportPostingDate(params: any) {
        // valid params
        if (!params.company_code) throw new BadRequestException('company_code is required.');
        if (params.report_type !== 'AR' &&
            params.report_type !== 'AP' &&
            params.report_type !== 'PR' &&
            params.report_type !== 'BR' )
            throw new BadRequestException('report_type is not correct.');
        if (params.report_type === 'AR') {
            const arList = await this.arRepository.find({
                where: {
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2])
                },
                order: {
                    posting_date: 'DESC',
                    create_time: 'DESC'
                },
                take: 1
            });
            if (arList.length > 0) return ({company_code: arList[0].company_code, posting_date: arList[0].posting_date, create_time: arList[0].create_time});
        }
        if (params.report_type === 'AP') {
            const apList = await this.apRepository.find({
                where: {
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2])
                },
                order: {
                    posting_date: 'DESC',
                    create_time: 'DESC'
                },
                take: 1
            });
            if (apList.length > 0) return ({company_code: apList[0].company_code, posting_date: apList[0].posting_date, create_time: apList[0].create_time});
        }
        if (params.report_type === 'PR') {
            const prList = await this.apRepository.find({
                where: {
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2]),
                    br_type: '5'
                },
                order: {
                    posting_date: 'DESC',
                    create_time: 'DESC'
                },
                take: 1
            });
            if (prList.length > 0) return ({company_code: prList[0].company_code, posting_date: prList[0].posting_date, create_time: prList[0].create_time});
        }
        if (params.report_type === 'BR') {
            const brList = await this.reconciliationHistory.find({
                where: {
                    company_code: params.company_code,
                    reverse_document_no: IsNull()
                },
                order: {
                    posting_date: 'DESC',
                    create_time: 'DESC'
                },
                take: 1
            });
            if (brList.length > 0) return ({company_code: brList[0].company_code, posting_date: brList[0].posting_date, create_time: brList[0].create_time});
        }
        return null;
    }

    async reportArApToExcel(params: any) {
        // generate data from params
        const reportHeader = await this.reportArApToJson(params);

        // generate Excel data
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Sheet 1');

    }

    private validReportArApParams(params: any) {
        if (!params.company_code) throw new BadRequestException('company_code is required.');
        if (!params.company_name) throw new BadRequestException('company_name is required.');
        if (!params.report_type) throw new BadRequestException('report_type is required.');
        if (params.report_type !== 'AR' && params.report_type !== 'AP') throw new BadRequestException('report_type is not correct.');
        if (!params.start_date) params.start_date = this.defaultDate;
        if (!params.end_date) throw new BadRequestException('end_date is required.');
        if (!params.report_date) params.report_date = params.end_date;

    }

    private async getReportArInvoices(params: any): Promise<ReportInvoiceDto[]> {
        try {
            let arList = await this.arRepository.find({
                where: {
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2]),
                    posting_date: Between(params.start_date, params.end_date)
                },
                order: {
                    bill_to_customer_id: 'ASC',
                    posting_date: 'ASC'
                }
            });
            let res = [];
            for (let item of arList) {
                res.push(new ReportInvoiceDto().convertToDtoFromAr(item));
            }
            return res;
        }catch (e) {
            throw new HttpException('get report ar invoice list error', 500);
        }
    }

    private async getReportApInvoices(params: any): Promise<ReportInvoiceDto[]> {
        try {
            let apList = await this.apRepository.find({
                where: {
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2]),
                    br_type: Not(In(['5'])),
                    posting_date: Between(params.start_date, params.end_date)
                },
                order: {
                    issuer_id: 'ASC',
                    posting_date: 'ASC'
                }
            });
            let res = [];
            for (let item of apList) {
                res.push(new ReportInvoiceDto().convertToDtoFromAp(item));
            }
            return res;
        }catch (e) {
            throw new HttpException('get report ap invoice list error', 500);
        }
    }

    private async getReportArReconciles(params: any): Promise<ReportInvoiceDto[]> {
        try {
            const queryBuilder = this.arRepository.createQueryBuilder('ar')
                .leftJoinAndSelect('reconciliation_history_v2','his', 'his.br_id = ar.id AND his.br_entity_type = 0 AND his.company_code = :company_code AND his.posting_date BETWEEN :posting_date_start AND :posting_date_end AND his.reverse_document_no IS NULL', {company_code: params.company_code, posting_date_start: params.start_date, posting_date_end: params.end_date})
                .select([
                    'ar.id AS id',
                    'ar.company_code AS company_code',
                    'ar.invoice_no AS invoice_no',
                    'ar.bill_to_customer_id AS bill_to_customer_id',
                    'ar.bill_to_company AS bill_to_company',
                    'ar.invoice_currency AS invoice_currency',
                    'ar.reference_no AS reference_no',
                    'ar.engine_document_id AS engine_document_id',
                    'ar.net_amount AS net_amount',
                    'ar.tax_content AS tax_content',
                    'ar.total_tax AS total_tax',
                    'ar.total_fee AS total_fee',
                    'ar.balance AS balance',
                    'ar.posting_date AS posting_date',
                    'ar.br_flag AS br_flag',
                    'ar.br_type AS br_type',
                    'his.posting_date AS reconcile_posting_date',
                    'his.reconcile_amount AS reconcile_amount',
                    'his.after_balance AS after_balance'
                ])
                .where({
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2]),
                    posting_date: Between(params.start_date, params.end_date)
                })
                .orderBy({
                    'ar.bill_to_customer_id': 'ASC',
                    'ar.posting_date': 'ASC'
                });
            if (params.reference) queryBuilder.andWhere('ar.reference_no LIKE :reference', { reference: `%${params.reference}%` });
            const arList: any = await queryBuilder.getRawMany();
            let res = [];
            for (let item of arList) {
                res.push(new ReportInvoiceDto().convertToDtoFromAr(item));
            }
            return res;
        }catch (e) {
            throw new HttpException('get report ap invoice list error', 500);
        }
    }

    private async getReportApReconciles(params: any): Promise<ReportInvoiceDto[]> {
        try {
            const queryBuilder = this.apRepository.createQueryBuilder('ap')
                .leftJoinAndSelect('reconciliation_history_v2','his', 'his.br_id = ap.id AND his.br_entity_type = 1 AND his.company_code = :company_code AND his.posting_date BETWEEN :posting_date_start AND :posting_date_end AND his.reverse_document_no IS NULL', {company_code: params.company_code, posting_date_start: params.start_date, posting_date_end: params.end_date})
                .select([
                    'ap.id AS id',
                    'ap.company_code AS company_code',
                    'ap.invoice_no AS invoice_no',
                    'ap.issuer_id AS issuer_id',
                    'ap.issuer_name AS issuer_name',
                    'ap.invoice_currency AS invoice_currency',
                    'ap.reference_no AS reference_no',
                    'ap.engine_document_id AS engine_document_id',
                    'ap.net_amount AS net_amount',
                    'ap.tax_content AS tax_content',
                    'ap.total_tax AS total_tax',
                    'ap.total_fee AS total_fee',
                    'ap.balance AS balance',
                    'ap.posting_date AS posting_date',
                    'ap.br_flag AS br_flag',
                    'ap.br_type AS br_type',
                    'his.posting_date AS reconcile_posting_date',
                    'his.reconcile_amount AS reconcile_amount',
                    'his.after_balance AS after_balance'
                ])
                .where({
                    company_code: params.company_code,
                    br_flag: In([0, 1, 2]),
                    br_type: Not(In(['5'])),
                    posting_date: Between(params.start_date, params.end_date)
                })
                .orderBy({
                    'ap.issuer_id': 'ASC',
                    'ap.posting_date': 'ASC'
                });
            const apList: any = await queryBuilder.getRawMany();
            let res = [];
            for (let item of apList) {
                res.push(new ReportInvoiceDto().convertToDtoFromAp(item));
            }
            return res;
        }catch (e) {
            throw new HttpException('get report ap invoice list error', 500);
        }
    }

    private generateReportArApList(invoiceList: ReportInvoiceDto[], params: any) {
        const groupedData = invoiceList.reduce((result: ReportDetailDto[], record) => {
            const { bp_no } = record;
            let reportItemWithBpNo = result.find(item => item.bp_no == bp_no)
            if (!reportItemWithBpNo) {
                reportItemWithBpNo = new ReportDetailDto();
                reportItemWithBpNo.bp_no = bp_no;
                reportItemWithBpNo.bp_name = record.bp_name;
                reportItemWithBpNo.summary = [];
                reportItemWithBpNo.detail = [];
                result.push(reportItemWithBpNo);
            }
            // add reportDetailItem one by one
            reportItemWithBpNo.detail.push(...this.buildFromReportArApDto(record, params, reportItemWithBpNo.detail, invoiceList));
            return result;
        }, []);

        const reportHeader = new ReportHeaderDto();
        reportHeader.company_code = params.company_code;
        reportHeader.company_name = params.company_name;
        reportHeader.report_type = params.report_type;
        reportHeader.posting_period = params.start_date == this.defaultDate ? `As of ${params.end_date}` : `${params.start_date} to ${params.end_date}`;
        reportHeader.report_date = params.report_date;
        reportHeader.detail = groupedData ? groupedData : [];
        return this.handleArApSummaryTotal(reportHeader);
    }

    private buildInitialReportDetailItemForInvoice(engineDocNo: String, invoiceList: ReportInvoiceDto[], params: any): ReportDetailItemDto {
        let findDetails = invoiceList.filter(item => item.engine_doc_no == engineDocNo);
        if (findDetails.length > 0) {
            const reportInvoiceDto = findDetails[0];
            let reportDetailItemForInvoice = new ReportDetailItemDto();
            reportDetailItemForInvoice.engine_doc_no = reportInvoiceDto.engine_doc_no;
            reportDetailItemForInvoice.invoice_no = reportInvoiceDto.invoice_no;
            reportDetailItemForInvoice.posting_date =  reportInvoiceDto.posting_date;
            reportDetailItemForInvoice.invoice_currency = reportInvoiceDto.invoice_currency;
            reportDetailItemForInvoice.invoice_reference = reportInvoiceDto.invoice_reference;
            reportDetailItemForInvoice.status = this.status_invoice;
            reportDetailItemForInvoice.reconcile_amount = 0;
            reportDetailItemForInvoice.br_type = reportInvoiceDto.br_type;
            // handle invoice to diff category by to_0, to_31, to_61, to_91
            this.handleGroupByDiffDay(reportDetailItemForInvoice, reportInvoiceDto.total_fee, 0, params);
            return reportDetailItemForInvoice;
        }
        return null;
    }

    private buildInitialReportDetailItemForNotPaid(engineDocNo: String, invoiceList: ReportInvoiceDto[], params: any): ReportDetailItemDto {
        let findDetails = invoiceList.filter(item => item.engine_doc_no == engineDocNo);
        if (findDetails.length > 0) {
            const reportInvoiceDto = findDetails[0];
            let balance = reportInvoiceDto.total_fee;
            findDetails.forEach(item => balance = this.sub(balance, item.reconcile_amount ?? 0, item.br_type));
            if (balance != 0) { // not paid
                let reportDetailItemForNotPaid = new ReportDetailItemDto();
                reportDetailItemForNotPaid.engine_doc_no = reportInvoiceDto.engine_doc_no;
                reportDetailItemForNotPaid.invoice_no = reportInvoiceDto.invoice_no;
                reportDetailItemForNotPaid.posting_date = reportInvoiceDto.posting_date;
                reportDetailItemForNotPaid.invoice_currency = reportInvoiceDto.invoice_currency;
                reportDetailItemForNotPaid.invoice_reference = reportInvoiceDto.invoice_reference;
                reportDetailItemForNotPaid.status = null;
                reportDetailItemForNotPaid.reconcile_amount = 0;
                reportDetailItemForNotPaid.br_type = reportInvoiceDto.br_type;
                // handle invoice to diff category by to_0, to_31, to_61, to_91
                this.handleGroupByDiffDay(reportDetailItemForNotPaid, reportInvoiceDto.total_fee, balance, params);
                return reportDetailItemForNotPaid;
            }
        }
        return null;
    }

    private buildFromReportArApDto(reportInvoiceDto: ReportInvoiceDto, params: any, exitDetails: ReportDetailItemDto[], invoiceList: ReportInvoiceDto[]): ReportDetailItemDto[] {
        let reportDetailItems: ReportDetailItemDto[] = [];

        const findDetails= exitDetails.find(item => item.engine_doc_no == reportInvoiceDto.engine_doc_no);
        if (!findDetails) {
            const addDetailItemForInvoice = this.buildInitialReportDetailItemForInvoice(reportInvoiceDto.engine_doc_no, invoiceList, params);
            const addDetailItemForNotPaid = this.buildInitialReportDetailItemForNotPaid(reportInvoiceDto.engine_doc_no, invoiceList, params);
            if (addDetailItemForInvoice) reportDetailItems.push(addDetailItemForInvoice);
            if (addDetailItemForNotPaid) reportDetailItems.push(addDetailItemForNotPaid);
        }

        // if (reportInvoiceDto.balance === 0) { // full paid
        if (reportInvoiceDto.reconcile_posting_date) { // full paid OR partial paid
            let reportDetailItemForPaid = new ReportDetailItemDto();
            reportDetailItemForPaid.engine_doc_no = reportInvoiceDto.engine_doc_no;
            reportDetailItemForPaid.invoice_no = reportInvoiceDto.invoice_no;
            reportDetailItemForPaid.posting_date = reportInvoiceDto.reconcile_posting_date;
            reportDetailItemForPaid.invoice_currency = reportInvoiceDto.invoice_currency;
            reportDetailItemForPaid.invoice_reference = reportInvoiceDto.invoice_reference;
            reportDetailItemForPaid.br_type = reportInvoiceDto.br_type;
            reportDetailItemForPaid.status = params.report_type == 'AR' ? this.status_receive : this.status_payment;
            reportDetailItemForPaid.reconcile_amount = reportInvoiceDto.reconcile_amount;
            // handle invoice to diff category by to_0, to_31, to_61, to_91
            this.handleGroupByDiffDay(reportDetailItemForPaid, reportInvoiceDto.total_fee, 0, params);
            reportDetailItems.push(reportDetailItemForPaid);
        }
        // else { // not paid
        //     let reportDetailItemForNotPaid = new ReportDetailItemDto();
        //     reportDetailItemForNotPaid.engine_doc_no = reportInvoiceDto.engine_doc_no;
        //     reportDetailItemForNotPaid.posting_date = reportInvoiceDto.reconcile_posting_date;
        //     reportDetailItemForNotPaid.invoice_currency = reportInvoiceDto.invoice_currency;
        //     reportDetailItemForNotPaid.invoice_reference = reportInvoiceDto.invoice_reference;
        //     reportDetailItemForNotPaid.status = this.status_invoice;
        //     reportDetailItemForNotPaid.reconcile_amount = reportInvoiceDto.reconcile_amount;
        //     // handle invoice to diff category by to_0, to_31, to_61, to_91
        //     this.handleGroupByDiffDay(reportDetailItemForNotPaid, reportInvoiceDto.total_fee, reportInvoiceDto.total_fee, params);
        //     reportDetailItems.push(reportDetailItemForNotPaid);
        // }
        return reportDetailItems;
    }

    private getDiffDay(date_1, date_2) {
        // 计算两个日期之间的差值
        let totalDays,diffDate
        let myDate_1 = Date.parse(date_1)
        let myDate_2 = Date.parse(date_2)
        // 将两个日期都转换为毫秒格式，然后做差
        totalDays = Math.floor(Math.abs(myDate_1 - myDate_2) / (1000 * 3600 * 24)) // 向下取整
        return totalDays    // 相差的天数
    }

    private handleGroupByDiffDay(dto: ReportDetailItemDto, amount: number, balance: number, params: any) {
        dto.total_amount = dto.status === this.status_invoice ? amount : 0;
        dto.balance = balance ?? 0;
        dto.to_0 = 0;
        dto.to_31 = 0;
        dto.to_61 = 0;
        dto.to_91 = 0;
        if (!dto.status) {
            const diffDay = this.getDiffDay(params.report_date, dto.posting_date);
            switch (true) {
                case diffDay > 90:
                    dto.to_91 = balance;
                    break;
                case diffDay > 60:
                    dto.to_61 = balance;
                    break;
                case diffDay > 30:
                    dto.to_31 = balance;
                    break;
                default:
                    dto.to_0 = balance;
                    break;
            }
        }
    }

    private handleArApSummaryTotal(reportHeader: ReportHeaderDto) {
        let headerSummary: ReportDetailSummaryDto[] = [];
        const headerDetails = reportHeader.detail;
        for (let detail of headerDetails) {
            let detailSummary: ReportDetailSummaryDto[] = [];
            const detailItems = detail.detail;
            for (let item of detailItems) {
                // handle detail summary
                this.calArApSummary(detailSummary, item);
                // handle header summary
                this.calArApSummary(headerSummary, item);
            }
            // set value for detail obj
            detail.summary = detailSummary;
        }
        // set value for header obj
        reportHeader.summary = headerSummary;
        return reportHeader;
    }

    private calArApSummary(summaryList: ReportDetailSummaryDto[], detailItem: ReportDetailItemDto) {
        // handle summary
        let summaryByCurrency = summaryList.find(element => detailItem.invoice_currency == element.invoice_currency);
        if (!summaryByCurrency) {
            summaryByCurrency = new ReportDetailSummaryDto();
            summaryByCurrency.invoice_currency = detailItem.invoice_currency;
            summaryByCurrency.total_amount = 0;
            summaryByCurrency.reconcile_amount = 0;
            summaryByCurrency.balance = 0;
            summaryByCurrency.balance_to_0 = 0;
            summaryByCurrency.balance_to_31 = 0;
            summaryByCurrency.balance_to_61 = 0;
            summaryByCurrency.balance_to_91 = 0;
            summaryList.push(summaryByCurrency);
        }
        // add total
        summaryByCurrency.balance_to_0 = this.add(summaryByCurrency.balance_to_0, detailItem.to_0);
        summaryByCurrency.balance_to_31 = this.add(summaryByCurrency.balance_to_31, detailItem.to_31);
        summaryByCurrency.balance_to_61 = this.add(summaryByCurrency.balance_to_61, detailItem.to_61);
        summaryByCurrency.balance_to_91 = this.add(summaryByCurrency.balance_to_91, detailItem.to_91);
        summaryByCurrency.balance = this.multiAdd([summaryByCurrency.balance, detailItem.to_0, detailItem.to_31, detailItem.to_61, detailItem.to_91]);
        summaryByCurrency.total_amount = this.add(summaryByCurrency.total_amount, detailItem.total_amount);
        summaryByCurrency.reconcile_amount = (detailItem.br_type == BkTypeEnum.PR || detailItem.br_type == BkTypeEnum.SR) ?  this.sub(summaryByCurrency.reconcile_amount, detailItem.reconcile_amount, detailItem.br_type) : this.add(summaryByCurrency.reconcile_amount, detailItem.reconcile_amount);

    }

    private add(num1: number, num2: number): number {
        let decimalNum1 = new Decimal(num1);
        let decimalNum2 = new Decimal(num2);
        return Number(decimalNum1.plus(decimalNum2).toFixed(2));
    }

    private sub(num1: number, num2: number, brType: String): number {
        let decimalNum1 = new Decimal(num1);
        let decimalNum2 = new Decimal(num2);
        return num1 < 0 && (brType == BkTypeEnum.PR || brType == BkTypeEnum.SR) ? Number(decimalNum1.add(decimalNum2).toFixed(2)) : Number(decimalNum1.sub(decimalNum2).toFixed(2));
    }

    private multiAdd(numArray: number[]): number {
        if (numArray.length === 0) return 0;
        let total = new Decimal(0);
        for (let num of numArray) {
            let decimalNum = new Decimal(num);
            total = total.plus(decimalNum);
        }
        return Number(total.toFixed(2));
    }
}
