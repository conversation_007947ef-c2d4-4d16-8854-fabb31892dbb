import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger
} from '@nestjs/common';
import { CreateApDto } from './dto/create-ap.dto';
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {generateLocalDateTimeByFormat} from "../common/utils/function.util";
import {Paginated} from "../common/utils/index.util";
import {Ap} from "./entities/ap.entity";
import {ApItem} from "./entities/ap.item.entity";
import {BaseService} from "../common/service/base.service";
import {EngineInvoiceDto} from "./dto/engine-invoice.dto";
import {EngineInvoiceItemDto} from "./dto/engine-invoice-item.dto";
import {ConfigService} from "@nestjs/config";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {HttpService} from "@nestjs/axios";
import {FileUploadService} from "../file-upload/file-upload.service";
import {BrStatusEnum} from "../common/enum/br.status.enum";
import {FileUpload} from "../file-upload/entities/file-upload.entity";
import {EsReconcileReverseService} from "../es_reconcile/es_reconcile_reverse/es_reconcile_reverse.service";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {JushiPoDto} from "./dto/jushi-po.dto";
import {BkEntityTypeEnum} from "../common/enum/bk.entity_type.enum";
import {FileStatusEnum} from "../common/enum/file.status.enum";
import {PayMethodEnum} from "../common/enum/pay.method.enum";
import {EsReconcileService} from "../es_reconcile/es_reconcile.service";
import {ReconcileSubmitDto} from "../es_reconcile/dto/reconcile_submit.dto";
import {JushiPostDto} from "./dto/jushi-post.dto";
import {JushiPostItemDto} from "./dto/jushi-post-item.dto";
import {Decimal} from "decimal.js";
import {EnumUtil} from "../common/enum/enum.util";
import {LogService} from "../log/log.service";
import {Gl} from "../gl/entities/gl.entity";
import {CoaTopInitService} from "../coa_top_init/coa_top_init.service";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";
import {HttpServiceUtil} from "../common/utils/httpService.util";

@Injectable()
export class ApService extends BaseService<Ap> {

  private readonly logger = new Logger(ApService.name);

  constructor(
      @InjectRepository(Ap) private readonly apRepository: Repository<Ap>,
      @InjectRepository(ApItem) private readonly apItemRepository: Repository<ApItem>,
      @InjectRepository(CoaTopInit) private readonly coaTopInitRepository: Repository<CoaTopInit>,
      @InjectRepository(ReconciliationHistory) private readonly historyRepository: Repository<ReconciliationHistory>,
      private dataSource: DataSource,
      private configService: ConfigService,
      private httpService: HttpService,
      private fileUploadService: FileUploadService,
      private esReconcileService: EsReconcileService,
      private esReconcileReverseService: EsReconcileReverseService,
      private logService: LogService,
  ) {
    super(apRepository, 'ap');
  }

  async createAp(createApDto: CreateApDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let ap: Ap = new Ap();
    ap = Object.assign(ap, createApDto);

    //
    let extension_items = null;
    if (createApDto.extension_items) {
      extension_items = createApDto.extension_items;
    }

    // set invoice no
    ap.invoice_no = generateLocalDateTimeByFormat(ap.company_code, new Date());
    // set balance
    ap.balance = ap.total_fee;
    // set br_flag
    const companyInfo = await this.findCompanyInfo(ap.company_code);
    Logger.log(`company info = ${companyInfo}`);
    ap.br_flag = companyInfo.sap_paymente_integration == 1 ? BrStatusEnum.NOT_PAID_INTEGRATION : BrStatusEnum.NOT_PAID;
    Logger.log(`br_flag = ${ap.br_flag}`);

    try {
      // check items length
      if (createApDto.items.length <= 0) throw new HttpException('items cannot be empty.', 400);
      // add ap data
      await queryRunner.manager.save<Ap>(ap);
      let lineItemTotalAmount = new Decimal(0);
      for (let i in createApDto.items) {
        let apItem: ApItem = new ApItem();
        apItem = Object.assign(apItem, createApDto.items[i]);
        apItem.ap = ap;
        lineItemTotalAmount = Decimal.add(lineItemTotalAmount, apItem.total);
        // add ap item data
        await queryRunner.manager.save<ApItem>(apItem);
      }
      if (!ap.issuer_id)
        throw new HttpException('issuer id is required.', 400);
      if (!lineItemTotalAmount.equals(ap.net_amount))
        throw new HttpException(`The net amount (${ap.net_amount}) does not match the line item total (${lineItemTotalAmount}).`, 400);
      if (!ap.pay_method || ap.pay_method.length == 0)
        throw new HttpException('Pay method is required.', 400);
      if (ap.pay_method == PayMethodEnum.CASH_PAID && ap.br_type != BkTypeEnum.CP)
        throw new HttpException('Br_type is not correct.', 400);

      // update status to upload-file table with file url
      if (ap.file_id)
        await queryRunner.manager.update<FileUpload>(FileUpload, ap.file_id, {invoice_created_status: FileStatusEnum.GENERATED});

      // send ap data to engine
      const enginePostInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/post-invoice';
      const engineInput = this.engineInvoiceBuilder(ap, extension_items);
      this.logger.log('[AP][engine][url]', enginePostInvoiceUrl);
      this.logger.log('[AP][engine][input]', JSON.stringify(engineInput));
      const engineRes = await lastValueFrom(this.httpService.post(enginePostInvoiceUrl, engineInput)
          .pipe(
              tap(response => {
                this.logger.log(JSON.stringify(response.data));
                this.logService.sendLogDataToServer(enginePostInvoiceUrl, JSON.stringify(engineInput), response.status.toString(), response.data);
              }),
              map((response) => {
                return response.data;
              }),
              catchError(e => {
                this.logger.log(e.response.data);
                e.response.message = '[Engine Error] ' + JSON.stringify(e.response.data.errors);
                this.logService.sendLogDataToServer(enginePostInvoiceUrl, JSON.stringify(engineInput), e.response.status.toString(), e.response.data);
                throw new HttpException(e.response, e.response.status);
              })
          ));
      this.logger.log('res', JSON.stringify(engineRes));
      if (!engineRes || !engineRes.data || !engineRes.data.success || engineRes.data.success == false)
        throw new HttpException(engineRes, 500);
      if (createApDto.pay_method != PayMethodEnum.CASH_PAID && (!engineRes.data.data.document_no || engineRes.data.data.document_no.length == 0))
        throw new HttpException('[Engine Error] No invoice no return from engine.', 500);
      if (createApDto.pay_method == PayMethodEnum.CASH_PAID && (!engineRes.data.invoice_no || engineRes.data.invoice_no.length == 0))
        throw new HttpException('[Engine Error] No invoice no return because of cash paid.', 500);
      if (createApDto.pay_method == PayMethodEnum.CASH_PAID && (!engineRes.data.payment_no || engineRes.data.invoice_no.length == 0))
        throw new HttpException('[Engine Error] No payment no return because of cash paid.', 500);

      ap.engine_document_id = createApDto.pay_method != PayMethodEnum.CASH_PAID ? engineRes.data.data.document_no : engineRes.data.invoice_no;
      ap.cash_engine_payment_no = createApDto.pay_method == PayMethodEnum.CASH_PAID ? engineRes.data.payment_no : null;

      await queryRunner.manager.update<Ap>(Ap, ap.id, {engine_document_id: ap.engine_document_id, cash_engine_payment_no: ap.cash_engine_payment_no, send_engine_status: 1});
      // commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }

    // if invoice is cash paid, then call BR API to reconcile
    if (createApDto.pay_method == PayMethodEnum.CASH_PAID)
      await this.cashPaidReconcile(ap);

    return ap;
  }

  async createApIntegration(createApDto: CreateApDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let ap: Ap = new Ap();
    ap = Object.assign(ap, createApDto);

    //
    let extension_items = null;
    if (createApDto.extension_items) {
      extension_items = createApDto.extension_items;
    }

    // set invoice no
    ap.invoice_no = generateLocalDateTimeByFormat(ap.company_code, new Date());
    // set balance
    if (!ap.mx_isr) ap.mx_isr = 0;
    if (!ap.mx_iva) ap.mx_iva = 0;
    ap.balance = ap.total_fee - ap.mx_isr - ap.mx_iva;
    // set br_flag
    const companyInfo = await this.findCompanyInfo(ap.company_code);
    Logger.log(`company info = ${companyInfo}`);
    ap.br_flag = companyInfo.sap_paymente_integration == 1 && ap.pay_method != PayMethodEnum.CASH_PAID ? BrStatusEnum.NOT_PAID_INTEGRATION : BrStatusEnum.NOT_PAID;
    Logger.log(`br_flag = ${ap.br_flag}`);

    try {
      // check items length
      if (createApDto.items.length <= 0) throw new HttpException('items cannot be empty.', 400);
      // add ap data
      await queryRunner.manager.save<Ap>(ap);
      let lineItemTotalAmount = new Decimal(0);
      for (let i in createApDto.items) {
        let apItem: ApItem = new ApItem();
        apItem = Object.assign(apItem, createApDto.items[i]);
        apItem.ap = ap;
        lineItemTotalAmount = Decimal.add(lineItemTotalAmount, apItem.total);
        // add ap item data
        await queryRunner.manager.save<ApItem>(apItem);
      }
      if (!ap.issuer_id)
        throw new HttpException('issuer id is required.', 400);
      if (!lineItemTotalAmount.equals(ap.net_amount))
        throw new HttpException(`The net amount (${ap.net_amount}) does not match the line item total (${lineItemTotalAmount}).`, 400);
      if (!ap.pay_method || ap.pay_method.length == 0)
        throw new HttpException('Pay method is required.', 400);
      // if (ap.pay_method == PayMethodEnum.CASH_PAID && ap.br_type != BkTypeEnum.CP)
      //   throw new HttpException('Br_type is not correct.', 400);

      // update status to upload-file table with file url
      if (ap.file_id)
        await queryRunner.manager.update<FileUpload>(FileUpload, ap.file_id, {invoice_created_status: FileStatusEnum.GENERATED});

      ap.send_sap_status = 3;

      // send ap data to engine
      // const enginePostInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/post-invoice';
      // const engineInput = this.engineInvoiceBuilder(ap, extension_items);
      // this.logger.log('[AP][engine][url]', enginePostInvoiceUrl);
      // this.logger.log('[AP][engine][input]', JSON.stringify(engineInput));
      // const engineRes = await lastValueFrom(this.httpService.post(enginePostInvoiceUrl, engineInput)
      //     .pipe(
      //         tap(response => {
      //           this.logger.log(JSON.stringify(response.data));
      //           this.logService.sendLogDataToServer(enginePostInvoiceUrl, JSON.stringify(engineInput), response.status.toString(), response.data);
      //         }),
      //         map((response) => {
      //           return response.data;
      //         }),
      //         catchError(e => {
      //           this.logger.log(e.response.data);
      //           e.response.message = '[Engine Error] ' + JSON.stringify(e.response.data.errors);
      //           this.logService.sendLogDataToServer(enginePostInvoiceUrl, JSON.stringify(engineInput), e.response.status.toString(), e.response.data);
      //           throw new HttpException(e.response, e.response.status);
      //         })
      //     ));
      // this.logger.log('res', JSON.stringify(engineRes));
      // if (!engineRes || !engineRes.data || !engineRes.data.success || engineRes.data.success == false)
      //   throw new HttpException(engineRes, 500);
      // if (createApDto.pay_method != PayMethodEnum.CASH_PAID && (!engineRes.data.data.document_no || engineRes.data.data.document_no.length == 0))
      //   throw new HttpException('[Engine Error] No invoice no return from engine.', 500);
      // if (createApDto.pay_method == PayMethodEnum.CASH_PAID && (!engineRes.data.invoice_no || engineRes.data.invoice_no.length == 0))
      //   throw new HttpException('[Engine Error] No invoice no return because of cash paid.', 500);
      // if (createApDto.pay_method == PayMethodEnum.CASH_PAID && (!engineRes.data.payment_no || engineRes.data.invoice_no.length == 0))
      //   throw new HttpException('[Engine Error] No payment no return because of cash paid.', 500);
      //
      // ap.engine_document_id = createApDto.pay_method != PayMethodEnum.CASH_PAID ? engineRes.data.data.document_no : engineRes.data.invoice_no;
      // ap.cash_engine_payment_no = createApDto.pay_method == PayMethodEnum.CASH_PAID ? engineRes.data.payment_no : null;
      //
      // await queryRunner.manager.update<Ap>(Ap, ap.id, {engine_document_id: ap.engine_document_id, cash_engine_payment_no: ap.cash_engine_payment_no, send_engine_status: 1});

      // commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }

    // if invoice is cash paid, then call BR API to reconcile
    // if (createApDto.pay_method == PayMethodEnum.CASH_PAID)
    //   await this.cashPaidReconcile(ap);

    return ap;
  }

  async createApBatch(createApDtoList: CreateApDto[]) {
    if (!createApDtoList || !createApDtoList.length || createApDtoList.length == 0)
      throw new BadRequestException('the purchase data is required.');

    let successIds = '';

    for (let i=0; i<createApDtoList.length; i++) {
      try {
        const newAp = await this.createAp(createApDtoList[i]);
        successIds += successIds.length == 0 ? newAp.id : ',' + newAp.id;
      }catch (e) {
        throw new HttpException(e.message, e.status);
      }
    }

    return successIds;
  }

  async cashPaidReconcile(dto: Ap) {
    try {
      await this.esReconcileService.reconcileSubmit(this.cashPaidReconcileBuilder(dto));
    }catch (error) {
      throw new HttpException(error, 500);
    }
  }

  private cashPaidReconcileBuilder(dto: Ap): ReconcileSubmitDto {
    const cashPaidReconParam = {
      company_code: dto.company_code,
      statement_id: -1,
      br_type: parseInt(dto.br_type),
      br_entity_type: parseInt(BkTypeEnum.RP),
      posting_date: dto.posting_date.toString(),
      currency: parseInt(dto.invoice_currency),
      bank_account: '',
      balance: dto.total_fee,
      gl_account: '',
      creator: dto.creator,
      invoice_list: [{
        invoice_id: dto.id,
        balance: dto.total_fee,
        bp_number: dto.issuer_id,
        br_type: parseInt(dto.br_type),
        br_entity_type: parseInt(BkTypeEnum.RP),
        reconcile_amount: dto.total_fee,
        engine_document_id: dto.cash_engine_payment_no,
        gl_account: ''
      }]
    };
    const res = Object.assign(new ReconcileSubmitDto(), cashPaidReconParam);
    this.logger.log('cashPaidReconcileBuilder', JSON.stringify(res));
    return res;
  }

    async topCoA(params: any) {  
      if (!params.issuer_id || params.issuer_id.length === 0) throw new HttpException('issuer_id is required', HttpStatus.BAD_REQUEST);
      if (!params.company_code || params.company_code.length === 0) throw new HttpException('company_code is required', HttpStatus.BAD_REQUEST);
      const issuer_id = params.issuer_id;
      const company_code = params.company_code;

      const qb = this.apItemRepository.createQueryBuilder('apItem');
      qb.select('apItem.credit_coa_id, apItem.credit_coa_code, COUNT(apItem.id) as count')
          .innerJoin(Ap, 'ap', 'ap.id = apItem.invoice_id AND ap.deleted_time IS NULL')
          .where('ap.company_code = :companyCode AND ap.issuer_id = :clientId', { companyCode: company_code, clientId: issuer_id })
          .andWhere('apItem.deleted_time IS NULL AND apItem.credit_coa_id IS NOT NULL')
          .groupBy('apItem.credit_coa_id, apItem.credit_coa_code')
          .orderBy('count', 'DESC');
      const result = await qb.getRawMany<{ credit_coa_id: number; credit_coa_code: string; count: string }>();

      if (result?.length > 0) {
        return result.map(result => ({ ...result, count: parseInt(result.count) }));
      }else {
        return await this.coaTopInitRepository
            .createQueryBuilder('coaTopInit')
            .select('coaTopInit.coa_id', 'credit_coa_id')
            .addSelect('coaTopInit.coa_code', 'credit_coa_code')
            .addSelect('coaTopInit.count', 'count')
            .where('coaTopInit.company_code = :companyCode', { companyCode: company_code })
            .andWhere('coaTopInit.contact_id = :clientId', { clientId: issuer_id })
            .andWhere('coaTopInit.contact_type = :contactType', { contactType: 'P' })
            .orderBy('coaTopInit.count', 'DESC')
            .getRawMany<{ credit_coa_id: number; credit_coa_code: string; count: string }>();
      }
  }

  async findAllAp(params: any) {
      try {
        const {where, order} = this.parseParams.parseQuery(params);
        if (params.page_index) {
          const {page_index, page_size = 10} = params;
          const [result, total] = await this.apRepository.findAndCount({
            where: where,
            relations: {items: true},
            order: order,
            take: page_size,
            skip: (page_index - 1) * page_size
          });
          return new Paginated(total, page_size, page_index, result)
        }
        return await this.apRepository.find({
          where: where,
          relations: {items: true},
          order: order
        });
      } catch (e) {
        throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
      }
  }

  async findOne(id: number) {
    return await this.apRepository.findOne({where: {id: id}, relations: {items: true} });
  }

  async integrationStatusUpdate(id: number, param: any) {
    const invoiceDetail = await this.findOne(id);
    if (!invoiceDetail) {
      throw new BadRequestException('this invoice is not exist.')
    }
    if (invoiceDetail.br_flag != BrStatusEnum.NOT_PAID_INTEGRATION
        && invoiceDetail.br_flag != BrStatusEnum.PAYMENT
        && invoiceDetail.br_flag != BrStatusEnum.PAY_IN_PROGRESS) {
      throw new BadRequestException('this invoice status error.')
    }
    if (!param || !param.creator || param.creator.length == 0) {
      throw new BadRequestException('the param creator is required.')
    }
    if (!param.creator_name || param.creator_name.length == 0) {
      throw new BadRequestException('the param creator_name is required.')
    }
    if (!param || !param.status ||
        (param.status != BrStatusEnum.NOT_PAID_INTEGRATION
        && param.status != BrStatusEnum.PAYMENT
        && param.status != BrStatusEnum.PAY_IN_PROGRESS
        && param.status != BrStatusEnum.NOT_PAID)) {
      throw new BadRequestException('the param status is wrong.')
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // update invoice br_flag to reversed status
      await queryRunner.manager.update<Ap>(Ap, id, {br_flag: param.status, creator: param.creator, creator_name: param.creator_name});
      // commit transaction
      return await queryRunner.commitTransaction();
    }catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
  }

  async integrationSapStatusUpdate(id: number, param: any) {
    const invoiceDetail = await this.findOne(id);
    if (!invoiceDetail) {
      throw new BadRequestException('this invoice is not exist.')
    }
    if (!param || !param.creator || param.creator.length == 0) {
      throw new BadRequestException('the param creator is required.')
    }
    if (!param.creator_name || param.creator_name.length == 0) {
      throw new BadRequestException('the param creator_name is required.')
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // update invoice br_flag to reversed status
      await queryRunner.manager.update<Ap>(Ap, id, {send_sap_status: param.status, creator: param.creator, creator_name: param.creator_name});
      // commit transaction
      return await queryRunner.commitTransaction();
    }catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
  }

  async integrationBrSapStatusUpdate(id: number, param: any) {
    const brEsDetail = await this.historyRepository.findOne({where: {id: id}});
    if (!brEsDetail) {
      throw new BadRequestException('this br es is not exist.')
    }
    if (!param || !param.transaction_id || param.transaction_id.length == 0) {
      throw new BadRequestException('the param transaction_id is required.')
    }
    if (!param || !param.creator || param.creator.length == 0) {
      throw new BadRequestException('the param creator is required.')
    }
    // if (!param.creator_name || param.creator_name.length == 0) {
    //   throw new BadRequestException('the param creator_name is required.')
    // }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // update invoice br_flag to reversed status
      await queryRunner.manager.update<ReconciliationHistory>(ReconciliationHistory, id, {send_sap_status: param.status, creator: param.creator});
      // commit transaction
      return await queryRunner.commitTransaction();
    }catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
  }

  async reverse(id: number, param: any) {
    const invoiceDetail = await this.findOne(id);
    if (!invoiceDetail) {
      throw new BadRequestException('this invoice is not exist.')
    }
    if (invoiceDetail.br_flag == BrStatusEnum.REVERSED) {
      throw new BadRequestException('this invoice has been reversed.')
    }
    if (!param || !param.creator || param.creator.length == 0) {
      throw new BadRequestException('the param creator is required.')
    }
    if (!param.creator_name || param.creator_name.length == 0) {
      throw new BadRequestException('the param creator_name is required.')
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // reverse ES and Invoice
      let reverseId = '';
      if (invoiceDetail.br_flag == BrStatusEnum.PARTIAL_PAID || invoiceDetail.br_flag == BrStatusEnum.PAID) {
        // call es reconcile reverse api
        const esBrRes = await this.esReconcileReverseService.reverseInvoice(id, parseInt(BkEntityTypeEnum.AP), param.creator);
        if (invoiceDetail.pay_method == PayMethodEnum.CASH_PAID) reverseId = esBrRes[0];
      }
      // send reverse data to engine
      const engineReverseInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/reverse-journal-entry';
      const engineReverseInput = {document_no: invoiceDetail.engine_document_id, posting_date: param.posting_date || invoiceDetail.posting_date, creator: param.creator};
      this.logger.log('[AP][engine][url]', engineReverseInvoiceUrl);
      this.logger.log('[AP][engine][input]', JSON.stringify(engineReverseInput));
      const reverseEngineRes = await lastValueFrom(this.httpService.post(engineReverseInvoiceUrl, engineReverseInput)
          .pipe(
              tap(response => {
                this.logger.log(JSON.stringify(response.data));
                this.logService.sendLogDataToServer(engineReverseInvoiceUrl, JSON.stringify(engineReverseInput), response.status.toString(), response.data);
              }),
              map((response) => {
                return response.data;
              }),
              catchError(e => {
                this.logger.log(e.response.data);
                e.response.message = '[Engine Error] ' + JSON.stringify(e.response.data.errors);
                this.logService.sendLogDataToServer(engineReverseInvoiceUrl, JSON.stringify(engineReverseInput), e.response.status.toString(), e.response.data);
                throw new HttpException(e.response, e.response.status);
              })
          ));
      if (!reverseEngineRes || !reverseEngineRes.data || !reverseEngineRes.data.success || reverseEngineRes.data.success == false) {
        throw new HttpException(reverseEngineRes, 500);
      }
      if (reverseEngineRes.data.success == true) {
        reverseId += reverseId && reverseId != '' ? ',' + reverseEngineRes.data.document_no : reverseEngineRes.data.document_no;
      }

      // update invoice br_flag to reversed status
      await queryRunner.manager.update<Ap>(Ap, id, {br_flag: BrStatusEnum.REVERSED, creator: param.creator, creator_name: param.creator_name, engine_reverse_document_id: reverseId});
      if (invoiceDetail.file_id) {
        // update file invoice_created_status to not created status
        await queryRunner.manager.update<FileUpload>(FileUpload, invoiceDetail.file_id, {invoice_created_status: FileStatusEnum.NOT_GENERATED});
      }
      // commit transaction
      return await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
  }

  async reverseIntegration(id: number, param: any) {
    const invoiceDetail = await this.findOne(id);
    if (!invoiceDetail) {
      throw new BadRequestException('this invoice is not exist.')
    }
    if (invoiceDetail.br_flag == BrStatusEnum.REVERSED) {
      throw new BadRequestException('this invoice has been reversed.')
    }
    if (!param || !param.creator || param.creator.length == 0) {
      throw new BadRequestException('the param creator is required.')
    }
    if (!param.creator_name || param.creator_name.length == 0) {
      throw new BadRequestException('the param creator_name is required.')
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
      // reverse ES and Invoice
      let reverseId = '';
      if (invoiceDetail.br_flag == BrStatusEnum.PARTIAL_PAID || invoiceDetail.br_flag == BrStatusEnum.PAID) {
        // call es reconcile reverse api
        const esBrRes = await this.esReconcileReverseService.reverseInvoice(id, parseInt(BkEntityTypeEnum.AP), param.creator);
        if (invoiceDetail.pay_method == PayMethodEnum.CASH_PAID) reverseId = esBrRes[0];
      }
      // send reverse data to engine
      // const engineReverseInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/reverse-journal-entry';
      // const engineReverseInput = {document_no: invoiceDetail.engine_document_id, posting_date: param.posting_date || invoiceDetail.posting_date, creator: param.creator};
      // this.logger.log('[AP][engine][url]', engineReverseInvoiceUrl);
      // this.logger.log('[AP][engine][input]', JSON.stringify(engineReverseInput));
      // const reverseEngineRes = await lastValueFrom(this.httpService.post(engineReverseInvoiceUrl, engineReverseInput)
      //     .pipe(
      //         tap(response => {
      //           this.logger.log(JSON.stringify(response.data));
      //           this.logService.sendLogDataToServer(engineReverseInvoiceUrl, JSON.stringify(engineReverseInput), response.status.toString(), response.data);
      //         }),
      //         map((response) => {
      //           return response.data;
      //         }),
      //         catchError(e => {
      //           this.logger.log(e.response.data);
      //           e.response.message = '[Engine Error] ' + JSON.stringify(e.response.data.errors);
      //           this.logService.sendLogDataToServer(engineReverseInvoiceUrl, JSON.stringify(engineReverseInput), e.response.status.toString(), e.response.data);
      //           throw new HttpException(e.response, e.response.status);
      //         })
      //     ));
      // if (!reverseEngineRes || !reverseEngineRes.data || !reverseEngineRes.data.success || reverseEngineRes.data.success == false) {
      //   throw new HttpException(reverseEngineRes, 500);
      // }
      // if (reverseEngineRes.data.success == true) {
      //   reverseId += reverseId && reverseId != '' ? ',' + reverseEngineRes.data.document_no : reverseEngineRes.data.document_no;
      // }

      // update invoice br_flag to reversed status
      await queryRunner.manager.update<Ap>(Ap, id, {br_flag: BrStatusEnum.REVERSED, creator: param.creator, creator_name: param.creator_name});
      // if (invoiceDetail.file_id) {
      //   // update file invoice_created_status to not created status
      //   await queryRunner.manager.update<FileUpload>(FileUpload, invoiceDetail.file_id, {invoice_created_status: FileStatusEnum.NOT_GENERATED});
      // }
      // commit transaction
      return await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
  }

  async reverseBatch(param: any) {
    this.logger.log(`reverse batch param = ${JSON.stringify(param)}`);

    let failIds = '';

    // valid params
    if (!param.company_code || param.company_code.length == 0)
      throw new BadRequestException('the param company_code is required.');
    if (!param.creator || param.creator.length == 0)
      throw new BadRequestException('the param creator is required.');
    if (!param.creator_name || param.creator_name.length == 0)
      throw new BadRequestException('the param creator_name is required.');
    if (!param.type || param.type.length == 0)
      throw new BadRequestException('the param type is required.');
    if (!param.invoice_ids || param.invoice_ids.length == 0)
      throw new BadRequestException('the param invoice_ids is required.');

    // const invoiceList: [] = this.findAllAp({company_code: param.company_code, id: [param.invoice_ids]});
    const invoice_id_list = param.invoice_ids.split(',');
    if (!invoice_id_list || invoice_id_list.length == 0)
      throw new BadRequestException('the param invoice_ids is invalid.');
    // reverse one by one
    for (let i=0; i<invoice_id_list.length; i++) {
      try {
        await this.reverse(invoice_id_list[i], {creator: param.creator, creator_name: param.creator_name});
        const detail = await this.findOne(invoice_id_list[i]);
        if (!detail) throw new BadRequestException('the id is invalid.');
      }catch (error) {
        // fail
        failIds += failIds.length == 0 ? invoice_id_list[i] : ',' + invoice_id_list[i];
        continue;
      }
    }
    return failIds;
  }

  async convertToCash(id: number, param: any) {
    const invoiceDetail: Ap = await this.findOne(id);
    if (invoiceDetail.pay_method == PayMethodEnum.CASH_PAID)
      throw new BadRequestException('The invoice is cash paid and cannot convert to cash paid.');
    if (invoiceDetail.br_flag != BrStatusEnum.NOT_PAID)
      throw new BadRequestException('The invoice has wrong br_flag and cannot convert to cash paid.');

    try {
      // step1: reverse
      await this.reverse(id, param);
      // step2: create cash paid invoice
      let createApDto = Object.assign(new CreateApDto(), invoiceDetail);
      createApDto.pay_method = PayMethodEnum.CASH_PAID;
      createApDto.br_type = BkTypeEnum.CP;
      await this.createAp(createApDto);
    }catch (error) {
      throw new HttpException(error, error.status);
    }
  }

  private engineInvoiceBuilder(dto: Ap, extension_items: JSON): EngineInvoiceDto {
    let engineDto = new EngineInvoiceDto();
    engineDto.company_code = dto.company_code;
    engineDto.cashPaidFlag = dto.pay_method == PayMethodEnum.CASH_PAID ? 1 : 0
    engineDto.bp = dto.issuer_id;
    engineDto.customer_vendor = this.getBpType(dto);
    engineDto.posting_date = dto.posting_date.toString();
    engineDto.currency = dto.invoice_currency;
    engineDto.header_text = dto.invoice_comments;
    engineDto.invoice_no = dto.invoice_no;
    engineDto.due_date = dto.invoice_due_date.toString();
    // engineDto.HST = Math.abs(dto.gst);
    // engineDto.QST = Math.abs(dto.qst);
    // engineDto.GST = Math.abs(dto.gst);
    // engineDto.PST = Math.abs(dto.pst);
    engineDto.tax = dto.tax_content;
    engineDto.extension_items = extension_items ? extension_items : null;
    engineDto.creator = dto.creator;

    let itemList: EngineInvoiceItemDto[] = [];
    for (let i in dto.items) {
      let dtoItem: ApItem = new ApItem();
      dtoItem = Object.assign(dtoItem, dto.items[i]);

      let engineDtoItem: EngineInvoiceItemDto = new EngineInvoiceItemDto();
      engineDtoItem.dr_cr = dtoItem.dr_cr;
      engineDtoItem.gl_account = dtoItem.credit_coa_code;
      engineDtoItem.business_partner = dtoItem.business_partner;
      engineDtoItem.amount_tc = Math.abs(dtoItem.total);
      // engineDtoItem.neg_posting = dtoItem.total > 0 ? false : true;
      itemList.push(engineDtoItem);
    }
    engineDto.line_items = itemList;
    return engineDto;
  }

  private getBpType(dto: Ap) {
    let bpType;
    // customer_vendor: V
    if (dto.br_type != BkTypeEnum.PY)
      return 'V';
    // customer_vendor: CRA
    if (dto.issuer_id == dto.company_code && dto.issuer_name.substring(0, 4) == 'CRA-')
      bpType = 'CRA';
    // customer_vendor: MRQ
    else if (dto.issuer_id == dto.company_code && dto.issuer_name.substring(0, 4) == 'MRQ-')
      bpType = 'MRQ';
    else
      // customer_vendor: E
      bpType = 'E';

    return bpType;
  }

  private engineJushiPostBuilder(dto: Ap): JushiPostDto {
    let jushiPostDto = new JushiPostDto();
    jushiPostDto.vendor = dto.issuer_name;
    jushiPostDto["Invoice Date"] = dto.invoice_create_date.toString();
    jushiPostDto.Reference = dto.reference_no;
    jushiPostDto.Amount = dto.total_fee;
    jushiPostDto.Purchase_Order = dto.po;
    jushiPostDto.Due_On = dto.invoice_due_date.toString();

    let itemList: JushiPostItemDto[] = [];
    for (let i in dto.items) {
      let dtoItem: ApItem = new ApItem();
      dtoItem = Object.assign(dtoItem, dto.items[i]);

      let jushiPostDtoItem: JushiPostItemDto = new JushiPostItemDto();
      jushiPostDtoItem.item_no = dtoItem.item_no.toString();
      jushiPostDtoItem.sku = dtoItem.model;
      jushiPostDtoItem.uom = dtoItem.uom;
      jushiPostDtoItem.price = dtoItem.unit_price;
      jushiPostDtoItem.quantity = dtoItem.qty;
      jushiPostDtoItem.amount = dtoItem.total;
      itemList.push(jushiPostDtoItem);
    }
    jushiPostDto.line_items = itemList;
    return jushiPostDto;
  }

  public async getJushiSapToken(): Promise<string> {
    const tokenUrl = this.configService.get('JUSHI_TOKEN_URL');
    const tokenUser = this.configService.get('JUSHI_TOKEN_USER');
    const tokenPassword = this.configService.get('JUSHI_TOKEN_PASSWORD');

    // get token
    const tokenInput = {
      username: tokenUser,
      password: tokenPassword
    }
    this.logger.log('[JUSHI][token][url]', tokenUrl);
    this.logger.log('[JUSHI][token][input]', JSON.stringify(tokenInput));
    const jushiTokenRes = await lastValueFrom(this.httpService.post(tokenUrl, tokenInput)
        .pipe(
            tap(response => {
              this.logger.log(JSON.stringify(response.data));
              this.logService.sendLogDataToServer(tokenUrl, JSON.stringify(tokenInput), response.status.toString(), response.data);
            }),
            map((response) => {
              return response.data;
            }),
            catchError(e => {
              this.logger.log(e.response.data);
              e.response.message = '[Jushi SAP Error] ' + e.response.data.errors;
              this.logService.sendLogDataToServer(tokenUrl, JSON.stringify(tokenInput), e.response.status.toString(), e.response.data);
              throw new HttpException(e.response, e.response.status);
            })
        ));
    if (!jushiTokenRes || !jushiTokenRes.data || jushiTokenRes.code != 200 || !jushiTokenRes.data.token) {
      throw new HttpException(jushiTokenRes, 500);
    }
    const token = jushiTokenRes.data.token;
    this.logger.log('token', token);
    return token;
  }

  public async getJushiDataFromSap(jushiPoDto: JushiPoDto) {
    try {
      if (!jushiPoDto.po || jushiPoDto.po.length == 0) {
        throw new HttpException('PO is required.', 400);
      }

      // get jushi sap token
      const token = await this.getJushiSapToken();

      // get jushi data from sap
      const getDataUrl = this.configService.get('JUSHI_GET_PO_URL');
      const dataInput = {
        PO: jushiPoDto.po
      }
      const config = {
        headers: {
          token: token,
          'Content-Type': 'application/json;charset=UTF-8'
        }
      }
      this.logger.log('[JUSHI][getPO][url]', getDataUrl);
      this.logger.log('[JUSHI][getPO][input]', JSON.stringify(dataInput));
      const jushiPoRes = await lastValueFrom(this.httpService.post(getDataUrl, dataInput, config)
          .pipe(
              tap(response => {
                this.logger.log(JSON.stringify(response.data));
                this.logService.sendLogDataToServer(getDataUrl, JSON.stringify(dataInput), response.status.toString(), response.data);
              }),
              map((response) => {
                return response.data;
              }),
              catchError(e => {
                this.logger.log(e.response.data);
                e.response.message = '[Jushi SAP Error] ' + e.response.data.msg;
                this.logService.sendLogDataToServer(getDataUrl, JSON.stringify(dataInput), e.response.status.toString(), e.response.data);
                throw new HttpException(e.response, e.response.code);
              })
          ));
      if (!jushiPoRes) {
        throw new HttpException(jushiPoRes, 500);
      }
      if (jushiPoRes.code != 200) {
        jushiPoRes.message = jushiPoRes.msg;
        throw new HttpException(jushiPoRes, jushiPoRes.code);
      }

      return jushiPoRes.data;
    } catch (error) {
      throw new HttpException(error, error.status);
    }

  }

  public async postJushiDataToSap(createApDto: CreateApDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let ap: Ap = new Ap();
    ap = Object.assign(ap, createApDto);
    // set invoice no
    ap.invoice_no = generateLocalDateTimeByFormat(ap.company_code, new Date());
    // set balance
    ap.balance = ap.total_fee;

    try {
      if (!createApDto.po || createApDto.po.length == 0) {
        throw new HttpException('PO is required.', 400);
      }

      // add ap data
      await queryRunner.manager.save<Ap>(ap);
      let lineItemTotalAmount = 0;
      for (let i in createApDto.items) {
        let apItem: ApItem = new ApItem();
        apItem = Object.assign(apItem, createApDto.items[i]);
        apItem.ap = ap;
        lineItemTotalAmount += apItem.total;
        // add ap item data
        await queryRunner.manager.save<ApItem>(apItem);
      }
      if (ap.net_amount != lineItemTotalAmount) {
        throw new HttpException(`The net amount (${ap.net_amount}) does not match the line item total (${lineItemTotalAmount}).`, 400);
      }

      // update status to upload-file table with file url
      if (ap.file_id) {
        await queryRunner.manager.update<FileUpload>(FileUpload, ap.file_id, {invoice_created_status: FileStatusEnum.GENERATED});
      }

      // get jushi sap token
      const token = await this.getJushiSapToken();

      // send ap data to engine
      const jushiPostInvoiceUrl = this.configService.get('JUSHI_SNED_PO_URL');
      const jushiPostInput = this.engineJushiPostBuilder(ap);
      this.logger.log('[Jushi][POST PO][url]', jushiPostInvoiceUrl);
      this.logger.log('[Jushi][POST PO][input]', JSON.stringify(jushiPostInput));

      const config = {
        headers: {
          token: token,
          'Content-Type': 'application/json;charset=UTF-8'
        }
      }

      const jushiPostRes = await lastValueFrom(this.httpService.post(jushiPostInvoiceUrl, jushiPostInput, config)
          .pipe(
              tap(response => {
                this.logger.log(JSON.stringify(response.data));
                this.logService.sendLogDataToServer(jushiPostInvoiceUrl, JSON.stringify(jushiPostInput), response.status.toString(), response.data);
              }),
              map((response) => {
                return response.data;
              }),
              catchError(e => {
                this.logger.log(e.response);
                e.response.message = '[JUSHI SAP Error] ' + JSON.stringify(e.response);
                this.logService.sendLogDataToServer(jushiPostInvoiceUrl, JSON.stringify(jushiPostInput), e.response.status.toString(), e.response.data);
                throw new HttpException(e.response, e.response.status);
              })
          ));
      this.logger.log('res', JSON.stringify(jushiPostRes));
      if (!jushiPostRes || jushiPostRes.code != 200 || !jushiPostRes.data || !jushiPostRes.data.businessKey) {
        throw new HttpException(JSON.stringify(jushiPostRes), 500);
      }
      const businessKey = jushiPostRes.data.businessKey;
      await queryRunner.manager.update<Ap>(Ap, ap.id, {send_engine_status: 1, engine_document_id: businessKey});

      // commit transaction
      await queryRunner.commitTransaction();
      return ap;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
  }

  public async findContactDataFromSap(param: any) {

    try {
      if (!param.keyword || param.keyword.length == 0) {
        throw new HttpException('Keyword is required.', 400);
      }

      // get ap sap token
      const token = await this.getApSapToken();

      // send ap data to engine
      const apSapFindContactUrl = this.configService.get('AP_SAP_FIND_CONTACT_URL');
      const apSapPostInput = {
        IV_ABSTRACT_NAME: param.keyword,
        IV_LOG_JSON: 'X'
      }
      this.logger.log('[AP_SAP_CONTACT][POST][url]', apSapFindContactUrl);
      this.logger.log('[AP_SAP_CONTACT][POST][input]', JSON.stringify(apSapPostInput));

      const config = {
        headers: {
          token: token,
          'Content-Type': 'application/json;charset=UTF-8'
        }
      }

      const apSapPostRes = await lastValueFrom(this.httpService.post(apSapFindContactUrl, apSapPostInput, config)
          .pipe(
              tap(response => {
                this.logger.log(JSON.stringify(response.data));
                this.logService.sendLogDataToServer(apSapFindContactUrl, JSON.stringify(apSapPostInput), response.status.toString(), response.data);
              }),
              map((response) => {
                return response.data;
              }),
              catchError(e => {
                this.logger.log(e.response);
                e.response.message = '[AP_SAP_CONTACT Error] ' + JSON.stringify(e.response);
                this.logService.sendLogDataToServer(apSapFindContactUrl, JSON.stringify(apSapPostInput), e.response.status.toString(), e.response.data);
                throw new HttpException(e.response, e.response.status);
              })
          ));
      this.logger.log('res', JSON.stringify(apSapPostRes));
      if (!apSapPostRes || apSapPostRes.code != 200 || !apSapPostRes.data) {
        throw new HttpException(JSON.stringify(apSapPostRes), 500);
      }
      return apSapPostRes.data;
    } catch (error) {
      throw new HttpException(error, error.status);
    }
  }

  public async findMasterDataFromSap(param: any) {

    try {
      // if (!param.keyword || param.keyword.length == 0) {
      //   throw new HttpException('Keyword is required.', 400);
      // }

      // get ap sap token
      const token = await this.getApSapToken();

      // send ap data to engine
      const apSapFindContactUrl = this.configService.get('AP_SAP_FIND_MASTER_URL');
      const apSapPostInput = {
        IV_LOG_JSON: 'X'
      }
      this.logger.log('[AP_SAP_MASTER][POST][url]', apSapFindContactUrl);
      this.logger.log('[AP_SAP_MASTER][POST][input]', JSON.stringify(apSapPostInput));

      const config = {
        headers: {
          token: token,
          'Content-Type': 'application/json;charset=UTF-8'
        }
      }

      const apSapPostRes = await lastValueFrom(this.httpService.post(apSapFindContactUrl, apSapPostInput, config)
          .pipe(
              tap(response => {
                this.logger.log(JSON.stringify(response.data));
                this.logService.sendLogDataToServer(apSapFindContactUrl, JSON.stringify(apSapPostInput), response.status.toString(), response.data);
              }),
              map((response) => {
                return response.data;
              }),
              catchError(e => {
                this.logger.log(e.response);
                e.response.message = '[AP_SAP_MASTER Error] ' + JSON.stringify(e.response);
                this.logService.sendLogDataToServer(apSapFindContactUrl, JSON.stringify(apSapPostInput), e.response.status.toString(), e.response.data);
                throw new HttpException(e.response, e.response.status);
              })
          ));
      this.logger.log('res', JSON.stringify(apSapPostRes));
      if (!apSapPostRes || apSapPostRes.code != 200 || !apSapPostRes.data) {
        throw new HttpException(JSON.stringify(apSapPostRes), 500);
      }
      return apSapPostRes.data;
    } catch (error) {
      throw new HttpException(error, error.status);
    }
  }

  public async getApSapToken(): Promise<string> {
    const tokenUrl = this.configService.get('AP_SAP_TOKEN_URL');
    const tokenUser = this.configService.get('AP_SAP_TOKEN_USER');
    const tokenPassword = this.configService.get('AP_SAP_TOKEN_PASSWORD');

    // get token
    const tokenInput = {
      username: tokenUser,
      password: tokenPassword
    }
    this.logger.log('[AP_SAP][token][url]', tokenUrl);
    this.logger.log('[AP_SAP][token][input]', JSON.stringify(tokenInput));
    const apSapTokenRes = await lastValueFrom(this.httpService.post(tokenUrl, tokenInput)
        .pipe(
            tap(response => {
              this.logger.log(JSON.stringify(response.data));
              this.logService.sendLogDataToServer(tokenUrl, JSON.stringify(tokenInput), response.status.toString(), response.data);
            }),
            map((response) => {
              return response.data;
            }),
            catchError(e => {
              this.logger.log(e.response.data);
              e.response.message = '[AP SAP Error] ' + e.response.data.errors;
              this.logService.sendLogDataToServer(tokenUrl, JSON.stringify(tokenInput), e.response.status.toString(), e.response.data);
              throw new HttpException(e.response, e.response.status);
            })
        ));
    if (!apSapTokenRes || !apSapTokenRes.data || apSapTokenRes.code != 200 || !apSapTokenRes.data.token) {
      throw new HttpException(apSapTokenRes, 500);
    }
    const token = apSapTokenRes.data.token;
    this.logger.log('AP_SAP token', token);
    return token;
  }

  async findAllApDetails(params: any) {
    try {
      const payrollAuthenticationUrl = this.configService.get('PAYROLL_AUTHENTICATION_URL');
      const authenticationParams = {
        "account": this.configService.get('PAYROLL_USER'),
        "password": this.configService.get('PAYROLL_PASSWORD'),
        "strategy": "local"
      }
      const authentication = await new HttpServiceUtil(this.httpService).post(payrollAuthenticationUrl, authenticationParams, null, '[plaid_salary_detail] Get payroll access token error');

      const {where, order} = this.parseParams.parseQuery(params);
      if (params.page_index) {
        const {page_index, page_size = 10} = params;
        const [result, total] = await this.apRepository.findAndCount({
          where: where,
          relations: {items: true},
          order: order,
          take: page_size,
          skip: (page_index - 1) * page_size
        });

        for (let ap of result) {
          if(!ap.resource_key || !ap.issuer_name.includes('Salary') || !ap.total_fee) {
            continue;
          }
          let salaryUrl = this.configService.get('PAYROLL_BASE_URL') + `/salaries/api/v1/salary-detail?$limit=500&$select[]=pay_item_amount&$select[]=emp_no&pay_period_id=${ap.resource_key}&pay_item_code=/110`
          let {data} = await new HttpServiceUtil(this.httpService).get(salaryUrl, authentication.accessToken, '[plaid_salary_detail] Get salary detail net_pay error');
          ap.salary_details = data;
        }

        return new Paginated(total, page_size, page_index, result)
      } else {
        const result = await this.apRepository.find({
          where: where,
          relations: {items: true},
          order: order
        });

        for (let ap of result) {
          if(!ap.resource_key || !ap.issuer_name.includes('Salary') || !ap.total_fee) {
            continue;
          }
          let salaryUrl = this.configService.get('PAYROLL_BASE_URL') + `/salaries/api/v1/salary-detail?$limit=500&$select[]=pay_item_amount&$select[]=emp_no&pay_period_id=${ap.resource_key}&pay_item_code=/110`
          let {data} = await new HttpServiceUtil(this.httpService).get(salaryUrl, authentication.accessToken, '[plaid_salary_detail] Get salary detail net_pay error');
          ap.salary_details = data;
        }

        return result;
      }
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }

  }

  async findCompanyInfo(companyCode: string) {
    try {
      const payrollAuthenticationUrl = this.configService.get('PAYROLL_AUTHENTICATION_URL');
      const authenticationParams = {
        "account": this.configService.get('PAYROLL_USER'),
        "password": this.configService.get('PAYROLL_PASSWORD'),
        "strategy": "local"
      }
      const authentication = await new HttpServiceUtil(this.httpService).post(payrollAuthenticationUrl, authenticationParams, null, '[plaid_salary_detail] Get payroll access token error');
      let userServiceUrl = this.configService.get('PAYROLL_BASE_URL') + `/users/api/v1/company?code=${companyCode}`
      let {data} = await new HttpServiceUtil(this.httpService).get(userServiceUrl, authentication.accessToken, '[users_company_info] Get company detail error');
      return data[0];
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  async remove(id:number) {
    const currentInvoice = await this.findOne(id);
    if (!currentInvoice) {
      return 'this invoice is not found'
    }
    this.logger.log('currentInvoice', currentInvoice);
    if (currentInvoice.sap_document_id != null && currentInvoice.sap_document_id.length > 0) {
      return 'this invoice can not be delete!';
    }
    await this.update(id, {deleted_time: new Date()});
    return'this invoice delete successful!';
  }

  async findApInvoiceBrHistory(id: number, params: any):  Promise<ReconciliationHistory[]> {
    let builder = this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
        .where("rh.br_id = :id", {id: id})
        .andWhere("rh.company_code = :company_code", {company_code: params.company_code})
        .andWhere('rh.sap_document_no is not null and rh.sap_reverse_document_no is null')
        .andWhere("rh.br_entity_type = 1");
    return await builder.getMany();
  }
}
