PORT=3030
ENV=production

# ------- DB相关 ---------------------
DATABASE_HOST=dc.inossemcanada.com
DATABASE_NAME=eo-db
DATABASE_USERNAME=ntdbuser
DATABASE_PASSWORD=Init6666
DATABASE_PORT=3306
DATABASE_TYPE=mysql

# ------- Swagger相关 ---------------------
# Api文档访问路径
SWAGGER_SETUP_PATH=/api/v1/docs
# 标题及描述
SWAGGER_UI_TITLE=BK API Document
SWAGGER_UI_TITLE_DESC=BK API Document
# API版本
SWAGGER_API_VERSION=1.0

# ------- File-upload相关 ---------------------
FILE_PATH=/home/<USER>/work/upload

# ------- PAGINATE 相关 ---------------------
PAGINATE_SIZE=10
PAGINATE_MAX=50000
PAGINATE_INDEX=1

# ------- Reconcile 相关 ---------------------
INVOICE_COMBINED_SIZE_THRESHOLD: 10        #流水对发票的组合数
ESTATEMENT_COMBINED_SIZE_THRESHOLD: 10     #发票对流水的组合数


# ------- PLAID 相关 ---------------------
PLAID_CLIENT_ID: 62c3141f64be9c001214c3cf
PLAID_SECRET: 8b0ecda0588d72f24d7953fa2a2f59
PLAID_TRANSACTIONS_URL: https://production.plaid.com/transactions/sync
PLAID_AUTH_URL: https://production.plaid.com/auth/get
PLAID_TRANSACTIONS_COUNT: 100
PLAID_FETCH_DATE_BEFORE_INIT: 60

# ------- EO-ENGINE 相关 ---------------------
ENGINE_BASE_URL: http://bkp-engine-service:3038

# ------- PAYROLL 相关 ---------------------
PAYROLL_BASE_URL: https://eo-gateway.inossemtimes.com
PAYROLL_AUTHENTICATION_URL: https://eo-gateway.inossemtimes.com/users/api/authentication
PAYROLL_USER: kycadmin
PAYROLL_PASSWORD: Admin@inossem2024!

# ------- EO-PDF-EMAIL-SERVICE 相关 ---------------------
PDF_EMAIL_SERVICE_BASE_URL: https://eo-gateway.inossemtimes.com/koa
UPLOAD_FILE_PATH: http://bk-prod.inossemcanada.com/web/static1
ES_FILE_PARSE_URL: https://eo-gateway.inossem.com/ESHubService/bank/process

# ------- EO-ABBYY-OCR 相关 ---------------------
OCR_BASE_URL: https://vantage-us.abbyy.com/api/publicapi/v1
OCR_LOGIN_URL: https://vantage-us.abbyy.com/auth2/connect/token
OCR_USER_NAME: <EMAIL>
OCR_PASSWORD: "Ai123.com"
OCR_CLIENT_ID: Szv_tf5XSfP-LUrLAFKyYr2z09Qo8h
OCR_CLIENT_SECRET: bpFL-xypAxLxli4XNtZq9bJBgQxhNurJLh
OCR_UPLOAD_URL: https://vantage-us.abbyy.com/api/publicapi/v1/transactions/launch?skillId=c2ec87c5-8f7b-4109-9ffa-c0504de6079d

# ------- JUSHI-SAP 相关 ---------------------
JUSHI_TOKEN_URL: http://20.116.137.30:8081/api/login
JUSHI_TOKEN_USER: jushibp
JUSHI_TOKEN_PASSWORD: 111111
JUSHI_GET_PO_URL: http://20.116.137.30:8081/pgo/sync/startFlow/8a74888c83d71d6001840a9d1d7d0043
JUSHI_SNED_PO_URL: http://20.116.137.30:8081/pgo/startFlow/8a7488a084c03d2e01850d25d22a00e7

# ------- AP-SAP 相关 ---------------------
AP_SAP_TOKEN_URL: https://inossem-times-pgo-backend.inossemcanada.com/api/login
AP_SAP_TOKEN_USER: ntbp
AP_SAP_TOKEN_PASSWORD: Inossem@7531
AP_SAP_FIND_CONTACT_URL: https://inossem-times-pgo-backend.inossemcanada.com/pgo/runConnector?connectorId=8ab883908fcd9967018ff44807da0031&compose=1
AP_SAP_FIND_MASTER_URL: https://inossem-times-pgo-backend.inossemcanada.com/pgo/runConnector?connectorId=8ab884a08ff4852b018ff5c02d2f0002&compose=1

# ------- LOG-SERVICE 相关 ---------------------
LOG_SERVICE_URL: https://eo-gateway.inossemtimes.com/logs/logs

# ------- AR-INVOICE-PDF-SERVICE 相关 ---------------------
AR_INVOICE_PDF_SERVICE_URL: https://eo-gateway.inossemtimes.com/handleExport/invoice-pdf
AR_INTEGRATION_INVOICE_PDF_SERVICE_URL: https://eo-gateway.inossem.com/ESHubService/bank/process

# ------- APIM网关 相关 ---------------------
EO_APIM: https://eo-gateway.inossemtimes.com
HANDLEEXPORT_SERVICE: /handleexport