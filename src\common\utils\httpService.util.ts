import {HttpException, Injectable} from "@nestjs/common";
import {HttpService} from "@nestjs/axios";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";

@Injectable()
export class HttpServiceUtil {
    constructor(private httpService: HttpService) {}

    async get(url: string, accessToken:string, log: string): Promise<any> {
        return await lastValueFrom(this.httpService.get(url, {
            headers: {
                'Authorization': 'Bearer ' + `${accessToken}`
            }
        })
            .pipe(
                tap(response => console.log(JSON.stringify(response.data))),
                map(async (response) => {
                    if (response.data) {
                        return response.data;
                    }
                    throw new HttpException(`${JSON.stringify(response.data)}`, response.status);
                }),
                catchError(e => {
                    throw new HttpException(`${log}: ${JSON.stringify(e.response.data)}`, e.response.status);
                })
            ));
    }

    async post(url: string, data, accessToken:string,errorLog: string): Promise<any> {
        return await lastValueFrom(this.httpService.post(url, data)
            .pipe(
                tap(response => console.log(JSON.stringify(response.data))),
                map(async (response) => {
                    if (response.data) {
                        return response.data;
                    }
                    throw new HttpException(`${JSON.stringify(response.data)}`, response.status);
                }),
                catchError(e => {
                    throw new HttpException(`${errorLog}: ${JSON.stringify(e.response.data)}`, e.response.status);
                })
            ));
    }

}
