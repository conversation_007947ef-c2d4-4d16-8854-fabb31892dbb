import {Repository, Select<PERSON><PERSON>y<PERSON>uilder} from "typeorm";
import {IBaseService} from "./IBase.service";
import {HttpException, HttpStatus, Injectable} from "@nestjs/common";
import {BaseEntity} from "../entity/base.entity";
import {Paginated} from "../utils/index.util";
import {ParseParams} from "./parseParams";

@Injectable()
export class BaseService<T extends BaseEntity> implements IBaseService<T> {

    selectQueryBuilder: SelectQueryBuilder<T>;
    parseParams: ParseParams;

    constructor(private readonly _repository: Repository<T>, private readonly alias: string) {
        this.selectQueryBuilder = _repository.createQueryBuilder(alias);
        this.parseParams = new ParseParams();
    }

    async create(entity: T): Promise<T> {
        try {
            return await this._repository.save(entity);
        } catch (e) {
            throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
        }
    }

    async batchCreate(entities: T[]): Promise<T[]> {
        return await this._repository.save(entities);
    }

    async delete(id: any) {
        return await this._repository.softDelete(id);
    }

    async update(id: number, entity: any): Promise<T> {
        await this._repository.update(id, entity);
        return await this.findOne(id);
    }

    async findAll(params?: any): Promise<T[]> {
        try {
            const {where, order,  withDeleted = false} = this.parseParams.parseQuery(params);
            return await this._repository.find({
                withDeleted: withDeleted,
                where: where,
                order: order,
            });
        } catch (e) {
            throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
        }
    }

    async findAllOnPaging(params?: any): Promise<Paginated<T>> {
        try {
            const {where, paginate, order, withDeleted = false} = this.parseParams.parseQuery(params);
            const [result, total] = await this._repository.findAndCount({
                where: where,
                withDeleted: withDeleted,
                order: order,
                take: paginate.page_size,
                skip: (paginate.page_index - 1) * paginate.page_size
            });
            return new Paginated(total, paginate.page_size, paginate.page_index, result)

        } catch (e) {
            throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
        }
    }

    findOne(id: any): Promise<T> {
        try {
            return <Promise<T>>this._repository.findOneBy({id});
        } catch (e) {
            throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
        }
    }
}