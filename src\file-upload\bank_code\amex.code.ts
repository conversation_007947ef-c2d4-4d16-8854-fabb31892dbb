import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {BadRequestException, HttpException, HttpStatus} from "@nestjs/common";
import {BankCodeStrategyService} from "../bank_code_strategy.service";
import {Express} from "express";
import {CsvHeaderDataDetector} from "../dto/csv-header-data-detector";
import {FileUpload} from "../entities/file-upload.entity";
import {BankTypeEnum} from "../../common/enum/bank_type.enum";
import {EsCsvDesKeyForRefund, EsCsvDesKeyForTransfer} from "../../common/utils/const";
import {EnumUtil} from "../../common/enum/enum.util";

export class AMEXCode extends BankCodeStrategyService {
    /**
     * American Express
     * parseCsvData()
     * 标准CSV文件格式，父类方法处理
     * @param file
     */


    /**
     * American Express
     * detectCsvHeaderAndData()
     * 特殊数据格式，针对性处理
     * @param csvData
     * @param keywords
     */
    public detectCsvHeaderAndData(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
        const csvHeaderDataRes = new CsvHeaderDataDetector();
        csvHeaderDataRes.header = [];
        csvHeaderDataRes.data = [];
        for (let i=0; i<csvData.length; i++) {
            let itemList: Array<string> = csvData[i];
            if (csvHeaderDataRes.header.length == 0 && i == 0) {
                csvHeaderDataRes.header = itemList;
            }else if (csvHeaderDataRes.header.length > 0) {
                csvHeaderDataRes.data.push(itemList);
            }
        }
        return csvHeaderDataRes;
    }

    /**
     * American Express
     * buildEsDtoList()
     * 特殊数据格式，针对性处理
     * @param csvHeaderData
     */
    public buildEsDtoList(csvHeaderData: CsvHeaderDataDetector, fileUploadDto: FileUpload, bankType: String): Es[] {
        const headerLine = csvHeaderData.header;
        const dataLine = csvHeaderData.data;
        const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
        let createEsDtoList: Es[] = [];
        for (let i = 0; i < dataLine.length; i++) {
            const esItem = dataLine[i];
            // valid data line
            let dataContent = '';
            for (let t = 0; t < esItem.length; t++) {
                dataContent += esItem[t];
            }
            if (dataContent.trim() == '') continue;
            let createEsDto: Es = new Es();
            createEsDto.company_code = fileUploadDto.company_code;
            createEsDto.currency = fileUploadDto.file_currency;
            createEsDto.bank_account = fileUploadDto.bank_account;
            createEsDto.creator = fileUploadDto.creator;
            createEsDto.file_id = fileUploadDto.id;
            for (let j = 0; j < headerLine.length; j++) {
                const headerStr = headerLine[j];
                // account type
                createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
                // account number
                if (['first bank card','account #','account number','accountid'].includes(headerStr.trim().toLowerCase())) {
                    // if (createEsDto.bank_account != esItem[j]) throw new Error('The bank account number on CSV file does not match the one selected.')
                    createEsDto.bank_account = esItem[j];
                }
                // date
                if (['date posted','date', '\"date\"','transaction date','tansaction date'].includes(headerStr.trim().toLowerCase())) {
                    let dateStrFromCsv: string = esItem[j];
                    let dateStrToDatabase: string = null;
                    if (dateStrFromCsv.indexOf('/') != -1) {
                        const dateStrArray: string[] = dateStrFromCsv.split('/');
                        dateStrToDatabase = dateStrArray[2] + '-' + dateStrArray[0] + '-' + dateStrArray[1];
                    } else {
                        dateStrToDatabase = dateStrFromCsv;
                    }
                    createEsDto.date = dateStrToDatabase;
                }
                // description
                if (['description','description 1', 'merchant name'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.description = esItem[j];
                }
                // deposit
                if (['deposit','deposit amount','cad$','transaction amount', 'amount', 'debitcredit', 'debit'].includes(headerStr.trim().toLowerCase())) {
                    const numStr = esItem[j].trim() ? esItem[j] : '0';
                    const num = parseFloat(numStr);
                    if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Deposit is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
                    if (bankType == 'Credit') {
                        if (num >= 0) {
                            createEsDto.withdrawal = num;
                            createEsDto.deposit = 0.00;
                        } else {
                            createEsDto.deposit = -num;
                            createEsDto.withdrawal = 0.00;
                        }
                    } else {
                        if (num >= 0) {
                            createEsDto.deposit = num;
                            createEsDto.withdrawal = 0.00;
                        } else {
                            createEsDto.withdrawal = -num;
                            createEsDto.deposit = 0.00;
                        }
                    }
                }
                // reference
                if (['reference', 'transaction', 'transaction reference', 'member name'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.reference = esItem[j];
                }
                // payer payee
                if (['payerpayee'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.payer_payee = esItem[j];
                }
                // description 2
                if (['description 2'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.description = createEsDto.description.length === 0 ?
                        esItem[j] : createEsDto.description + ' && ' + esItem[j];
                }
            }
            try {
                // valid account type
                if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
                // valid account number
                if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
                // valid transaction date
                if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
                // valid description
                if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

                // valid withdrawal and deposit
                if (createEsDto.deposit == null)
                    throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == null)
                    throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
                    continue;
                    // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
                if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
                    throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
                // set balance and br_type
                let isRefund, isTransfer = false;
                for (let key of EsCsvDesKeyForRefund) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isRefund = true;
                        break;
                    }
                }
                for (let key of EsCsvDesKeyForTransfer) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isTransfer = true;
                        break;
                    }
                }
                // set br_type
                if (isTransfer) {
                    createEsDto.br_type = parseInt(BkTypeEnum.FT);
                }else if (isRefund) {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
                }else {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
                }
                createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
                // set balance
                createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

                createEsDtoList.push(createEsDto);
            } catch (e) {
                this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
                throw new HttpException(e.response, e.status);
            }
        }
        return createEsDtoList;
    }
}