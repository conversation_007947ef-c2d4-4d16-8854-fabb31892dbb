import {forwardR<PERSON>, Module} from '@nestjs/common';
import { CheckService } from './check.service';
import { Check<PERSON>ontroller } from './check.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {HttpModule} from "@nestjs/axios";
import {ConfigModule} from "@nestjs/config";
import {Check} from "./entities/check.entity";
import {Ap} from "../ap/entities/ap.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Check, Ap]), HttpModule, ConfigModule, ],
  controllers: [CheckController],
  providers: [CheckService]
})
export class CheckModule {
}
