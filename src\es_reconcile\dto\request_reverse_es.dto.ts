import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class RequestReverseEsDto {

    @ApiProperty({type: Number, description: 'E-statement id', example: 101})
    @IsNotEmpty()
    statement_id: number;

    @ApiProperty({type: Number, description: 'creator', example: 120})
    @IsNotEmpty()
    creator: number;

    constructor(statement_id: number, creator: number) {
        this.statement_id = statement_id;
        this.creator = creator;
    }



}