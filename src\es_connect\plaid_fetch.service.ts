import {HttpException, Injectable, Logger} from "@nestjs/common";
import {HttpService} from "@nestjs/axios";
import {ConfigService} from "@nestjs/config";
import {catchError, map, tap} from "rxjs/operators";
import {lastValueFrom} from "rxjs";

@Injectable()
export class PlaidFetchService {

    private readonly logger = new Logger(PlaidFetchService.name);
    private readonly clientId: string;
    private readonly secret: string;
    private readonly transactionsUrl: string;
    private readonly authUrl: string;
    private readonly count: number;
    private readonly fetchDateBeforeInit: number;

    constructor(private httpService: HttpService, private configService: ConfigService) {
        this.clientId = this.configService.get("PLAID_CLIENT_ID");
        this.secret = this.configService.get("PLAID_SECRET");
        this.transactionsUrl = this.configService.get("PLAID_TRANSACTIONS_URL");
        this.authUrl = this.configService.get("PLAID_AUTH_URL");
        this.count = this.configService.get("PLAID_TRANSACTIONS_COUNT");
        this.fetchDateBeforeInit = this.configService.get("PLAID_FETCH_DATE_BEFORE_INIT")
    }

    public async getAuth(accessToken: string) {
        return this.connectWithPlaid({}, this.authUrl, accessToken)
    }

    public async getTransactions(data, accessToken: string) {
        return this.connectWithPlaid(data, this.transactionsUrl, accessToken);

    }

    private async connectWithPlaid(data: any, url: string, accessToken: string) {

        data["client_id"] = this.clientId;
        data["secret"] = this.secret;
        data["access_token"] = accessToken;

        return await lastValueFrom(this.httpService.post(url, data, {
            headers: {
                "Content-Type": "application/json",
                "charset": "UTF-8"
            }
        })
            .pipe(
                tap(response => this.logger.log(JSON.stringify(response.data))),
                map((response) => {
                    return response.data;
                }),
                catchError(e => {
                    this.logger.log(JSON.stringify(e.response.data));
                    throw new HttpException(JSON.stringify(e.response.data), e.response.status);
                })
            ));
    }

    public getCount(): number {
        return this.count;
    }
    getFetchDateBeforeInit(): number {
        return this.fetchDateBeforeInit;
    }
}
