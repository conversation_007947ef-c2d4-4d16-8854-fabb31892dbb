import {Column, Entity, OneToMany} from "typeorm";
import {ArItem} from "./ar.item.entity";
import {BaseEntity} from "../../common/entity/base.entity";

@Entity()
export class Ar extends BaseEntity{
    @Column({name: 'company_id', comment: 'company id'})
    company_id: number;

    @Column({name: 'company_code', comment: 'company code'})
    company_code: string;

    @Column({name: 'company_name', nullable: true, comment: 'company name'})
    company_name: string;

    @Column({name: 'company_address', nullable: true, comment: 'company address'})
    company_address: string;

    @Column({name: 'company_tel', nullable: true, comment: 'company tel'})
    company_tel: string;

    @Column({name: 'company_email', nullable: true, comment: 'company email'})
    company_email: string;

    @Column({name: 'company_logo', type: 'text', nullable: true, comment: 'company logo'})
    company_logo: string;

    @Column({name: 'company_gst_no', nullable: true, comment: 'company gst no'})
    company_gst_no: string;

    @Column({name: 'company_pst_no', nullable: true, comment: 'company pst no'})
    company_pst_no: string;

    @Column({name: 'company_currency', nullable: true, comment: 'company currency: CNY; CAD; USD'})
    company_currency: string;

    @Column({name: 'invoice_no', nullable: true, comment: 'invoice no'})
    invoice_no: string;

    @Column({name: 'reference_no', nullable: true, comment: 'reference no'})
    reference_no: string;

    @Column({name: 'invoice_currency', nullable: true, comment: 'invoice currency: 1 - CNY; 2 - CAD; 3 - USD'})
    invoice_currency: string;

    @Column({name: 'pay_method', nullable: true, comment: 'pay method: 1 - NOT PAID; 2 - CASH PAID; 3 - FUNDING TRANSFER; 4 - INTERCOM'})
    pay_method: string;

    @Column({name: 'invoice_create_date', nullable: true, type: 'date', comment: 'invoice create date'})
    invoice_create_date: Date;

    @Column({name: 'invoice_due_date', nullable: true, type: 'date', comment: 'invoice due date'})
    invoice_due_date: Date;

    @Column({name: 'posting_date', nullable: true, type: 'date', comment: 'posting date'})
    posting_date: Date;

    @Column({name: 'bill_to_customer_id', nullable: true, comment: 'bill to customer id'})
    bill_to_customer_id: string;

    @Column({name: 'bill_to_receiver', nullable: true, comment: 'bill to receiver'})
    bill_to_receiver: string;

    @Column({name: 'bill_to_company', nullable: true, comment: 'bill to company'})
    bill_to_company: string;

    @Column({name: 'bill_to_street', nullable: true, comment: 'bill to street'})
    bill_to_street: string;

    @Column({name: 'bill_to_city', nullable: true, comment: 'bill to city'})
    bill_to_city: string;

    @Column({name: 'bill_to_province', nullable: true, comment: 'bill to province'})
    bill_to_province: string;

    @Column({name: 'bill_to_country', nullable: true, comment: 'bill to country'})
    bill_to_country: string;

    @Column({name: 'bill_to_postal_code', nullable: true, comment: 'bill to postal code'})
    bill_to_postal_code: string;

    @Column({name: 'bill_to_tel', nullable: true, comment: 'bill to tel'})
    bill_to_tel: string;

    @Column({name: 'bill_to_email', nullable: true, comment: 'bill to email'})
    bill_to_email: string;

    @Column({name: 'ship_to_receiver', nullable: true, comment: 'ship to receiver'})
    ship_to_receiver: string;

    @Column({name: 'ship_to_company', nullable: true, comment: 'ship to company'})
    ship_to_company: string;

    @Column({name: 'ship_to_street', nullable: true, comment: 'ship to street'})
    ship_to_street: string;

    @Column({name: 'ship_to_city', nullable: true, comment: 'ship to city'})
    ship_to_city: string;

    @Column({name: 'ship_to_province', nullable: true, comment: 'ship to province'})
    ship_to_province: string;

    @Column({name: 'ship_to_country', nullable: true, comment: 'ship to country'})
    ship_to_country: string;

    @Column({name: 'ship_to_postal_code', nullable: true, comment: 'ship to postal code'})
    ship_to_postal_code: string;

    @Column({name: 'ship_to_tel', nullable: true, comment: 'ship to tel'})
    ship_to_tel: string;

    @Column({name: 'ship_to_email', nullable: true, comment: 'ship to email'})
    ship_to_email: string;

    @Column({name: 'net_amount', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'net amount'})
    net_amount: number;

    @Column({name: 'gst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'gst'})
    gst: number;

    @Column({name: 'pst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'pst'})
    pst: number;

    @Column({name: 'qst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'qst'})
    qst: number;

    @Column({name: 'total_tax', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total tax'})
    total_tax: number;

    @Column({name: 'total_fee', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total fee'})
    total_fee: number;

    @Column({name: 'total_fee_local', nullable: true, type: 'numeric', precision: 10, scale: 2, comment: 'total fee local'})
    total_fee_local: number;

    @Column({name: 'exchange_rate', nullable: true, type: 'numeric', precision: 10, scale: 5, comment: 'exchange rate: other currency -> local'})
    exchange_rate: number;

    @Column({name: 'balance', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'balance'})
    balance: number;

    @Column({name: 'invoice_comments', nullable: true, comment: 'invoice comments'})
    invoice_comments: string;

    @Column({name: 'br_flag', default: 0, comment: 'br flag: 0 not paid; 1 partial paid; 2 paid; 3 - reversed'})
    br_flag: number;

    @Column({name: 'send_engine_status', default: 0, comment: 'send engine status: 0 - not send; 1 - sent success; 2 - sent fail'})
    send_engine_status: number;

    @Column({name: 'engine_document_id', nullable: true, comment: 'engine document id'})
    engine_document_id: string;

    @Column({name: 'cash_engine_payment_no', nullable: true, comment: 'cash engine payment no'})
    cash_engine_payment_no: string;

    @Column({name: 'engine_reverse_document_id', nullable: true, comment: 'engine reverse document id'})
    engine_reverse_document_id: string;

    @Column({name: 'send_sap_status', default: 0, comment: 'send sap status: 0 - not sent;1 - sending;2 - sent successfully;3 - sent fail'})
    send_sap_status: number;

    @Column({name: 'sap_document_id', nullable: true, comment: 'sap document id'})
    sap_document_id: string;

    @Column({name: 'sap_reverse_document_id', nullable: true, comment: 'sap reverse document id'})
    sap_reverse_document_id: string;

    @Column({name: 'sap_message', nullable: true, comment: 'sap message'})
    sap_message: string;

    @Column({name: 'sap_cancel_bill_doc', nullable: true, comment: 'sap cancel bill doc'})
    sap_cancel_bill_doc: string;

    @Column({name: 'sap_original_bill_doc', nullable: true, comment: 'sap original bill doc'})
    sap_original_bill_doc: string;

    @Column({name: 'file_id', nullable: true, comment: 'file id'})
    file_id: number;

    @Column({name: 'file_page_index', nullable: true, comment: 'file page index'})
    file_page_index: number;

    @Column({name: 'file_url', nullable: true, comment: 'file url'})
    file_url: string;

    @Column({name: 'send_email_status', default: 0, comment: 'send engine status: 0 - not send; 1 - sent success; 2 - sent fail'})
    send_email_status: number;

    @Column({name: 'invoice_url', nullable: true, comment: 'invoice url'})
    invoice_url: string;

    @Column({name: 'invoice_file_name', nullable: true, comment: 'invoice file name'})
    invoice_file_name: string;

    @Column({name: 'invoice_file_b64', nullable: true, comment: 'invoice file b64'})
    invoice_file_b64: string;

    @Column({name: 'br_type', default: '0', comment: 'RS（regular sales）0, RP（regular purchase）1, PR（purchase refund）2, SR（sales refund）3, FT（funding trans）4, PY（payroll）5, FX(es of different currency) 6, ES(e-statement) 7'})
    br_type: string;

    @Column({name: 'bank_id', nullable: true, comment: 'bank id'})
    bank_id: number;

    @Column({name: 'bank_account', nullable: true, comment: 'bank account'})
    bank_account: string;

    @Column({name: 'bank_name', nullable: true, comment: 'bank name'})
    bank_name: string;

    @Column('json', { name: 'tax_content', nullable: true, comment: 'tax content' })
    tax_content: JSON;

    @Column('json', { name: 'original_file_content', nullable: true, comment: 'original file content' })
    original_file_content: JSON;

    @Column('json', { name: 'reverse_file_content', nullable: true, comment: 'reverse file content' })
    reverse_file_content: JSON;

    @Column({name: 'sap_billing_type', nullable: true, comment: 'sap billing type'})
    sap_billing_type: string;

    @Column({name: 'creator_name', nullable: true, comment: 'creator name'})
    creator_name: string;

    @Column({name: 'mx_discount', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'mx_discount'})
    mx_discount: number;

    @Column({name: 'mx_isr', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'mx_isr'})
    mx_isr: number;

    @Column({name: 'mx_iva', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'mx_iva'})
    mx_iva: number;

    @Column({name: 'col_ica', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'col_ica'})
    col_ica: number;

    @Column({name: 'xml_status', nullable: true, comment: 'PENDING, SUCCESS, FAILURE'})
    xml_status: string;

    @Column({name: 'xml_message', nullable: true, comment: 'xml message'})
    xml_message: string;

    @Column({name: 'xml_uuid', nullable: true, comment: 'xml uuid'})
    xml_uuid: string;

    @Column('json',{name: 'edicom', nullable: true, comment: 'edicom'})
    edicom: JSON;

    @Column({name: 'mx_rfc_no', nullable: true, comment: 'mx_rfc_no'})
    mx_rfc_no: string;

    @Column({name: 'payment_term', nullable: true, comment: 'payment term'})
    payment_term: string;

    @Column({name: 'customer_po_number', nullable: true, comment: 'customer po number'})
    customer_po_number: string;

    @Column({name: 'customer_vat_number', nullable: true, default: '99', comment: 'customer vat number'})
    customer_vat_number: string;

    @OneToMany(() => ArItem, arItem => arItem.ar)
    items: ArItem[]
}
