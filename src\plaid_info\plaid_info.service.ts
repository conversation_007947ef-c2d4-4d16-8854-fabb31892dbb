import {Injectable} from '@nestjs/common';
import {BaseService} from "../common/service/base.service";
import {PlaidInfo} from "./entities/plaid_info.entity";
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {SearchCompanyBankDto} from "./dto/search-company-bank.dto";
import {HttpServiceUtil} from "../common/utils/httpService.util";
import {HttpService} from "@nestjs/axios";
import {ConfigService} from "@nestjs/config";
import {CompanyBankResponseDto} from "./dto/company-bank-response.dto";
import {Paginated} from "../common/utils/index.util";

@Injectable()
export class PlaidInfoService extends BaseService<PlaidInfo>{

  constructor(@InjectRepository(PlaidInfo) private readonly repository: Repository<PlaidInfo>, protected dataSource: DataSource, protected httpService: HttpService, protected configService: ConfigService) {
    super(repository, 'plaid_info');
  }

  async findByBankId(bank_id: number): Promise<PlaidInfo[]> {
    return this.repository.find({
      where: {
        bank_id: bank_id
      }
    })
  }

  async findByCodeAndBankName(company_code: string, bank_name: string) {
    return await this.repository.find({
      where: {
        company_code: company_code,
        bank_name: bank_name
      }
    });
  }

  async findCompanyBank(params: SearchCompanyBankDto): Promise<CompanyBankResponseDto[] | Paginated<CompanyBankResponseDto>> {

    const payrollAuthenticationUrl = this.configService.get('PAYROLL_AUTHENTICATION_URL');
    const getCompanyBankUrl = this.configService.get('PAYROLL_BASE_URL') + `/users/api/v1/company-bank?company_code=${params.company_code}&$limit=${params.page_size}&$skip=${(params.page_index -1) * params.page_size}&$sort[create_time]=desc`;
    const authenticationParams = {
      "account": this.configService.get('PAYROLL_USER'),
      "password": this.configService.get('PAYROLL_PASSWORD'),
      "strategy": "local"
    }
    const authentication = await new HttpServiceUtil(this.httpService).post(payrollAuthenticationUrl, authenticationParams, null, '[plaid_company_bank] Get payroll access token error');
    const {total, data} = await new HttpServiceUtil(this.httpService).get(getCompanyBankUrl, authentication.accessToken, '[plaid_company_bank] Get companyBank home currency error');
    const res: CompanyBankResponseDto[] = [];
    for (const bank of data) {
      const responseDto = new CompanyBankResponseDto();
      Object.assign(responseDto, bank);

      const plaidInfo: PlaidInfo[] = await this.findByBankId(responseDto.id);
      if(plaidInfo.length > 0) {
        responseDto.plaid_status = plaidInfo[0].plaid_status;
      }
      res.push(responseDto);
    }

    return new Paginated(total, params.page_size, params.page_index, res);
  }
}
