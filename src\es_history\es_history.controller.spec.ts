import {Test, TestingModule} from '@nestjs/testing';
import {EsHistoryController} from './es_history.controller';
import {EsHistoryService} from './es_history.service';

describe('EsHistoryController', () => {
    let controller: EsHistoryController;

    const mockEsHistoryService = {
        //列出EsHistoryService中定义的方法。jest.fn() 不加实现返回的undefined。 jest.fn()的参数dto是调用时传入的参数
        create: jest.fn(dto => {
            return {id: Date.now(), ...dto}
        }),
        findOne: jest.fn().mockReturnValue({id: 1, name: '<PERSON>'}),
        //update 方法调用需要2个参数(id, dto)
        update: jest.fn().mockImplementation((id, dto) => {
            return {
                id,
                ...dto
            }
        })
    }

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            controllers: [EsHistoryController],
            providers: [EsHistoryService],
        })
            //override Provider
            .overrideProvider(EsHistoryService).useValue(mockEsHistoryService)
            .compile();

        controller = module.get<EsHistoryController>(EsHistoryController);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
    });

    it('should_find_history', function () {
        expect(controller.findOne("1")).toEqual({
            id: 1,
            name: "Marius"
        })
    });


    it('should_create_history', function () {
        const dto = {name: 'Marius'};
        expect(controller.create(dto)).toEqual({
                id: expect.any(Number),
                name: dto.name
            }
        )
        expect(mockEsHistoryService.create).toHaveBeenCalled();
        expect(mockEsHistoryService.create).toHaveBeenCalledWith(dto);
    });

    it('should_update_history', function () {
        const dto = {name: 'Marius'};
        expect(controller.update("1", dto)).toEqual({
            id: 1,
            ...dto
        })
    });
});
