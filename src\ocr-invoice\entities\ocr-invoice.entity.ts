import {Column, Entity, OneToMany} from "typeorm";
import {BaseEntity} from "../../common/entity/base.entity";
import {OcrInvoiceItem} from "./ocr-invoice-item.entity";

@Entity()
export class OcrInvoice extends BaseEntity {
    @Column({name: 'company_id', nullable: true, comment: 'company id'})
    company_id: number;

    @Column({name: 'company_code', comment: 'company code'})
    company_code: string;

    @Column({name: 'contact_name', nullable: true, comment: 'contact name'})
    contact_name: string;

    @Column({name: 'contact_address', nullable: true, comment: 'contact address'})
    contact_address: string;

    @Column({name: 'contact_tel', nullable: true, comment: 'contact tel'})
    contact_tel: string;

    @Column({name: 'contact_email', nullable: true, comment: 'contact email'})
    contact_email: string;

    @Column({name: 'gst_hst', nullable: true, comment: 'gst hst'})
    gst_hst: string;

    @Column({name: 'invoice_no', nullable: true, comment: 'invoice no'})
    invoice_no: string;

    @Column({name: 'invoice_create_date', nullable: true, comment: 'invoice create date'})
    invoice_create_date: string;

    @Column({name: 'invoice_due_date', nullable: true, comment: 'invoice due date'})
    invoice_due_date: string;

    @Column({name: 'amount', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'amount'})
    amount: number;

    @Column({name: 'gst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'gst'})
    gst: number;

    @Column({name: 'pst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'pst'})
    pst: number;

    @Column({name: 'qst', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'qst'})
    qst: number;

    @Column({name: 'total_tax', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total tax'})
    total_tax: number;

    @Column({name: 'total_taxable', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total_taxable'})
    total_taxable: number;

    @Column({name: 'total_fee', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total fee'})
    total_fee: number;

    @Column({name: 'deposit', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'deposit'})
    deposit: number;

    @Column({name: 'balance', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'balance'})
    balance: number;

    @Column({name: 'invoice_created_status', default: 0, comment: 'invoice created status: 0 - not created; 1 - created'})
    invoice_created_status: number;

    @Column({name: 'ocr_page_url', nullable: true, comment: 'ocr page url'})
    ocr_page_url: string;

    @Column({name: 'invoice_currency', nullable: true, comment: 'invoice_currency'})
    invoice_currency: string;

    @Column({name: 'reference_no', nullable: true, comment: 'reference_no'})
    reference_no: string;

    @Column({name: 'file_id', nullable: true, comment: 'file id'})
    file_id: number;

    @Column({name: 'file_page_index', nullable: true, comment: 'file page index'})
    file_page_index: number;

    @OneToMany(() => OcrInvoiceItem, ocrInvoiceItem => ocrInvoiceItem.ocrInvoice)
    items: OcrInvoiceItem[]
}
