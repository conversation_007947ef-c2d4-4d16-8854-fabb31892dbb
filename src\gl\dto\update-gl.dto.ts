import {ApiProperty, PartialType} from '@nestjs/swagger';
import { CreateGlDto } from './create-gl.dto';
import {GlItem} from "../entities/gl.item.entity";

export class UpdateGlDto extends PartialType(CreateGlDto) {

    @ApiProperty({ description: 'glNo' })
    gl_no: string;

    @ApiProperty({ description: 'description' })
    description: string;

    @ApiProperty({ description: 'createDate' })
    create_date: Date;

    @ApiProperty({ description: 'postingDate' })
    posting_date: Date;

    @ApiProperty({ description: 'currency' })
    currency: string;

    @ApiProperty({ description: 'exchangeRate' })
    exchange_rate: number;

    @ApiProperty({ description: 'totalDebit' })
    total_debit: number;

    @ApiProperty({ description: 'totalDebitCad' })
    total_debit_cad: number;

    @ApiProperty({ description: 'totalCredit' })
    total_credit: number;

    @ApiProperty({ description: 'totalCreditCad' })
    total_credit_cad: number;

    @ApiProperty({ description: 'status' })
    status: number;

    @ApiProperty({ description: 'sendEngineStatus' })
    send_engine_status: number;

    @ApiProperty({ description: 'items' })
    items: [GlItem];
}
