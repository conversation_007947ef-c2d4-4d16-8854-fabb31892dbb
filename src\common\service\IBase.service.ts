import {DeepPartial} from "typeorm";
import {Paginated} from "../utils/index.util";

export interface IBaseService<T> {

    findAll(params: T): Promise<T[]>;

    findAllOnPaging(params: T): Promise<Paginated<T>>;

    findOne(id: number): Promise<T>;

    create(entity: T): Promise<T>;

    batchCreate(entities: T[]): Promise<T[]>

    update(id: number, entity: DeepPartial<T>): Promise<T>;

    delete(id: number);

}