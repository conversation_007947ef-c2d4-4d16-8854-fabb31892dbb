import {forwardRef, Module} from '@nestjs/common';
import {EsReconcileService} from './es_reconcile.service';
import {EsReconcileController} from './es_reconcile.controller';
import {EsModule} from "../es/es.module";
import {ArModule} from "../ar/ar.module";
import {ApModule} from "../ap/ap.module";
import {ConfigModule} from "@nestjs/config";
import {TypeOrmModule} from "@nestjs/typeorm";
import {ReconciliationHistory} from "./entities/reconciliation_history.entity";
import {EsReconcileReverseController} from './es_reconcile_reverse/es_reconcile_reverse.controller';
import {EsReconcileReverseService} from './es_reconcile_reverse/es_reconcile_reverse.service';
import {HttpModule} from "@nestjs/axios";
import {LogModule} from "../log/log.module";

@Module({
    imports: [EsModule, forwardRef(() => ArModule), forwardRef(() => ApModule),
        ConfigModule, TypeOrmModule.forFeature([ReconciliationHistory]), HttpModule, LogModule],
    controllers: [EsReconcileController, EsReconcileReverseController],
    providers: [EsReconcileService, EsReconcileReverseService],
    exports: [EsReconcileService, EsReconcileReverseService]
})
export class EsReconcileModule {
}
