import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {BadRequestException, HttpException, HttpStatus} from "@nestjs/common";
import {BankCodeStrategyService} from "../bank_code_strategy.service";
import {CsvHeaderDataDetector} from "../dto/csv-header-data-detector";
import {FileUpload} from "../entities/file-upload.entity";
import {BankTypeEnum} from "../../common/enum/bank_type.enum";
import {EsCsvDesKeyForRefund, EsCsvDesKeyForTransfer} from "../../common/utils/const";
import {EnumUtil} from "../../common/enum/enum.util";

export class SANTANDERCode extends BankCodeStrategyService {
    // private specialKeys = ['divisa', 'fecha operación', 'fecha de liquidación', 'descripción', 'emisora serie', 'referencia', 'cantidad plazo', 'tasa rend prima unitaria', 'precio strike', 'importe'];
    /**
     * SANTANDER bank
     * parseCsvData()
     * 标准CSV文件格式，父类方法处理
     * @param file
     */


    /**
     * SANTANDER bank
     * detectCsvHeaderAndData()
     * 特殊数据格式，针对性处理
     * @param csvData
     * @param keywords
     */
    public detectCsvHeaderAndData(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
        const csvHeaderDataRes = new CsvHeaderDataDetector();
        
        // 检查文件类型特征
        const isCheckingAccount = this.isCheckingAccountFormat(csvData);
        const isCreditCardAccount = this.isCreditCardFormat(csvData);
        
        if (!isCheckingAccount && !isCreditCardAccount) {
            throw new BadRequestException('Unsupported Santander bank statement format');
        }
        
        if (isCheckingAccount) {
            return this.processCheckingAccount(csvData);
        } else {
            return this.processCreditCardAccount(csvData);
        }

        // todo 验证银行对账单类型与银行类型是否相符
        
        
        // for (let i=0; i<csvData.length; i++) {
        //     let itemList: Array<string> = csvData[i];
        //     if (csvHeaderDataRes.header.length == 0 && i == 0) {
        //         csvHeaderDataRes.header = itemList;
        //     }else if (csvHeaderDataRes.header.length > 0) {
        //         csvHeaderDataRes.data.push(itemList);
        //     }
        // }
        // return csvHeaderDataRes;
    }

    /**
     * SANTANDER bank
     * buildEsDtoList()
     * 通用数据格式，父类方法处理
     * @param csvHeaderData
     */
    public buildEsDtoList(csvHeaderData: CsvHeaderDataDetector, fileUploadDto: FileUpload, bankType: String): Es[] {
        const headerLine = csvHeaderData.header;
        const dataLine = csvHeaderData.data;
        const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
        let createEsDtoList: Es[] = [];
        for (let i = 0; i < dataLine.length; i++) {
            const esItem = dataLine[i];
            // valid data line
            let dataContent = '';
            for (let t = 0; t < esItem.length; t++) {
                dataContent += esItem[t];
            }
            if (dataContent.trim() == '') continue;
            let createEsDto: Es = new Es();
            createEsDto.company_code = fileUploadDto.company_code;
            createEsDto.currency = fileUploadDto.file_currency;
            createEsDto.bank_account = fileUploadDto.bank_account;
            createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
            createEsDto.creator = fileUploadDto.creator;
            createEsDto.file_id = fileUploadDto.id;
            // date - column 2
            let dateStrFromCsv: string = esItem[1].replace('\'', '');
            createEsDto.date = dateStrFromCsv.substring(4, 8) + '-' + dateStrFromCsv.substring(2, 4) + '-' + dateStrFromCsv.substring(0, 2);
            // description - column 10
            createEsDto.description = esItem[9];
            // deposit - withdrawal - column 7
            const numSignal = esItem[5].trim() ? esItem[5] : '+';
            const numStr = esItem[6].trim() ? esItem[6].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
            const num = parseFloat(numStr);
            if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
            if (numSignal == '+') {
                createEsDto.deposit = num;
                createEsDto.withdrawal = 0.00;
            } else {
                createEsDto.deposit = 0.00;
                createEsDto.withdrawal = num;
            }
            // reference - column 9
            createEsDto.reference = esItem[8];
            // payer payee - column 11
            createEsDto.payer_payee = esItem[10];

            // for (let j = 0; j < headerLine.length; j++) {
            //     const headerStr = headerLine[j];
            //     // date
            //     if (['date', 'fecha'].includes(headerStr.trim().toLowerCase())) {
            //         let dateStrFromCsv: string = esItem[j];
            //         createEsDto.date = dateStrFromCsv.substring(5, 9) + '-' + dateStrFromCsv.substring(3, 5) + '-' + dateStrFromCsv.substring(1, 3);
            //     }
            //     // description
            //     if (['concepto', 'concept'].includes(headerStr.trim().toLowerCase())) {
            //         createEsDto.description = esItem[j];
            //     }
            //     // deposit - withdrawal
            //     if (['amount', 'importe'].includes(headerStr.trim().toLowerCase())) {
            //         const numSignal = esItem[j - 1].trim() ? esItem[j - 1] : '+';
            //         const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
            //         const num = parseFloat(numStr);
            //         if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
            //         if (numSignal == '+') {
            //             createEsDto.deposit = num;
            //             createEsDto.withdrawal = 0.00;
            //         } else {
            //             createEsDto.deposit = 0.00;
            //             createEsDto.withdrawal = num;
            //         }
            //     }
            //     // reference
            //     if (['reference', 'referencia'].includes(headerStr.trim().toLowerCase())) {
            //         createEsDto.reference = esItem[j];
            //     }
            //     // payer payee
            //     if (['payer account', 'cta ordenante'].includes(headerStr.trim().toLowerCase())) {
            //         createEsDto.payer_payee = esItem[j];
            //     }
            // }
            try {
                // valid account type
                if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
                // valid account number
                if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
                // valid transaction date
                if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
                // valid description
                if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

                // handle negative value
                if (createEsDto.deposit < 0) {
                    createEsDto.withdrawal = Math.abs(createEsDto.deposit);
                    createEsDto.deposit = 0.00;
                } else if (createEsDto.withdrawal < 0) {
                    createEsDto.deposit = Math.abs(createEsDto.withdrawal);
                    createEsDto.withdrawal = 0.00;
                }

                // valid withdrawal and deposit
                if (createEsDto.deposit == null)
                    throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == null)
                    throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
                    continue;
                    // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
                if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
                    throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
                // set balance and br_type
                let isRefund, isTransfer = false;
                for (let key of EsCsvDesKeyForRefund) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isRefund = true;
                        break;
                    }
                }
                for (let key of EsCsvDesKeyForTransfer) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isTransfer = true;
                        break;
                    }
                }
                // set br_type
                if (isTransfer) {
                    createEsDto.br_type = parseInt(BkTypeEnum.FT);
                } else if (isRefund) {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
                } else {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
                }
                createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
                // set balance
                createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

                createEsDtoList.push(createEsDto);
            } catch (e) {
                this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
                throw new HttpException(e.response, e.status);
            }
        }
        return createEsDtoList;
    }

    private isCheckingAccountFormat(csvData: string[][]): boolean {
        // 支票账户特征判断
        if (csvData.length === 0) return false;
        // const requiredHeaders = ['account', 'date'];
        // const firstRow = csvData[0].map(item => item.trim().toLowerCase());
        // // 检查所有必需的表头是否都存在
        // return requiredHeaders.every(header => firstRow.includes(header));
        return csvData[0].length > 10;
    }

    private isCreditCardFormat(csvData: string[][]): boolean {
        // 信用卡账户特征判断
        if (csvData.length === 0) return false;
        // const requiredHeaders = ['credit card', 'transaction date'];
        // const firstRow = csvData[0].map(item => item.trim().toLowerCase());
        // // 检查所有必需的表头是否都存在
        // return requiredHeaders.every(header => firstRow.includes(header));
        return csvData[0].length == 10;
    }

    private processCheckingAccount(csvData: string[][]): CsvHeaderDataDetector {
        let csvHeaderDataRes = new CsvHeaderDataDetector();
        csvHeaderDataRes.header = [];
        csvHeaderDataRes.data = [];
        for (let i = 0; i < csvData.length; i++) {
            let itemList: Array<string> = csvData[i];
            if (csvHeaderDataRes.header.length == 0 && i == 0) {
                csvHeaderDataRes.header = itemList;
            }else if (csvHeaderDataRes.header.length > 0) {
                // 过滤空行，无关键数据行
                const dateStrFromCsv: string = itemList[1].replace(/\t/g, '').trim();
                const descriptionFromCsv: string = itemList[9].replace(/\t/g, '').trim();
                const withdrawDepositFromCsv: string = itemList[6].trim();
                const signal: string = itemList[5].trim();
                if (dateStrFromCsv && descriptionFromCsv && withdrawDepositFromCsv && signal) {
                    csvHeaderDataRes.data.push(itemList);
                }
            }
        }
        return csvHeaderDataRes;
        // const keywords = ['account', 'date', 'time', 'branch', 'description', 'deposits/position', 'amount', 'balance', 'reference', 'concepto', 'banco participante', 'domestic account (clabe)', 'nombre beneficiario', 'payer account', 'payer name'];
        // const minColumns = 3
        // for (let i = 0; i < csvData.length; i++) {
        //     const row = csvData[i];
        //     if (row.length < minColumns) continue;
        //
        //     const score = row.filter(cell =>
        //         keywords.some(keyword => cell.toLowerCase().includes(keyword)),
        //     ).length;
        //
        //     if (score >= 5) {
        //         let csvHeaderDataRes = new CsvHeaderDataDetector();
        //         csvHeaderDataRes.header = row;
        //         csvHeaderDataRes.data = csvData.slice(i + 1);
        //         return csvHeaderDataRes;
        //     }
        // }
        // throw new Error('No CSV header or data found.');
    }

    private processCreditCardAccount(csvData: string[][]): CsvHeaderDataDetector {
        let csvHeaderDataRes = new CsvHeaderDataDetector();
        csvHeaderDataRes.header = this.standardizeCreditCardHeaders(csvData[0]);
        csvHeaderDataRes.data = this.standardizeCreditCardData(csvData.slice(0 + 1));
        return csvHeaderDataRes;
        // const keywords = ['credit card','transaction date','time','branch','description','sign','amount','availabe credit','reference','concept'];
        // const minColumns = 3
        // for (let i = 0; i < csvData.length; i++) {
        //     const row = csvData[i];
        //     if (row.length < minColumns) continue;
        //
        //     const score = row.filter(cell =>
        //         keywords.some(keyword => cell.toLowerCase().includes(keyword)),
        //     ).length;
        //
        //     if (score >= 4) {
        //         let csvHeaderDataRes = new CsvHeaderDataDetector();
        //         csvHeaderDataRes.header = this.standardizeCreditCardHeaders(row);
        //         csvHeaderDataRes.data = this.standardizeCreditCardData(csvData.slice(i + 1));
        //         return csvHeaderDataRes;
        //     }
        // }
        // throw new Error('No CSV header or data found.');
    }

    private standardizeCreditCardHeaders(headers: string[]): string[] {
        // 将信用卡账单的表头标准化为统一格式
        return headers.map(header => {
            const h = header.trim().toLowerCase();
            switch (h) {
                case 'credit card':
                    return 'account';
                case 'transaction date':
                    return 'date';
                case 'sign':
                    return 'deposits/position'
                default:
                    return header;
            }
        });
    }

    private standardizeCreditCardData(data: string[][]): string[][] {
        // 将信用卡账单的数据标准化为统一格式
        // 处理每一行数据
        return data.map(row => {
            // 对每行的列进行处理
            return row.map((cell, columnIndex) => {
                // 日期列 (columnIndex === 1)
                if (columnIndex === 1 && cell) {
                    let dateStr = cell.trim();
                    if (dateStr.length === 5) {
                        dateStr = '0' + dateStr;
                    }
                    if (dateStr.length === 6) {
                        // 从 'MMDDYY' 格式转换为 'YYYY-MM-DD' 格式
                        const month = dateStr.substring(0, 2);
                        const day = dateStr.substring(2, 4);
                        const year = '20' + dateStr.substring(4, 6);
                        return `${day}${month}${year}`;
                    }
                }
                // sign符号列处理 (columnIndex === 5)
                if (columnIndex === 5 && cell && cell.trim()) {
                    const sign = cell.trim();
                    if (sign === '+') {
                        return '-';
                    } else if (sign === '-') {
                        return '+';
                    }
                }
                return cell;
            });
        });
    }

}