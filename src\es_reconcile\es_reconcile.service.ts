import {HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import {EsService} from "../es/es.service";
import {Paginated} from "../common/utils/index.util";
import {ResponseEsReconcileDto} from "./dto/response_es_reconcile.dto";
import {SelectEsReconcileDto} from "./dto/select_es_reconcile.dto";
import {Es} from "../es/entities/es.entity";
import {MatchDto} from "./dto/match.dto";
import {DataSource, EntityManager} from "typeorm";
import {Ar} from "../ar/entities/ar.entity";
import {Decimal} from 'decimal.js';
import {Ap} from "../ap/entities/ap.entity";
import {ConfigService} from "@nestjs/config";
import {ReconcileSubmitDto} from "./dto/reconcile_submit.dto";
import {BrStatusEnum} from "../common/enum/br.status.enum";
import {BaseEntity} from "../common/entity/base.entity";
import {ReconciliationHistory} from "./entities/reconciliation_history.entity";
import {EntityTarget} from "typeorm/common/EntityTarget";
import {v4 as uuidv4} from 'uuid';
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {HttpService} from "@nestjs/axios";
import {ManualEsReconcileDto} from "./dto/manual_es_reconcile.dto";
import {es} from "date-fns/locale";
import {BkTypeFactory} from "./bk.type.factory";
import {EnumUtil} from "../common/enum/enum.util";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import Invoice = ReconcileSubmitDto.Invoice;
import {LogService} from "../log/log.service";
import {ReconcileSubmitSpDto} from "./dto/reconcile_submit_sp.dto";
import {ReconcileSubmitEeDto} from "./dto/reconcile_submit_ee.dto";

@Injectable()
export class EsReconcileService {

    private readonly logger = new Logger(EsReconcileService.name);

    //The number of combinations of es to invoices
    invoiceCombinedSizeThreshold: number;

    //The number of combinations of invoice to es
    eStatementCombinedSizeThreshold: number;
    // brTypeStrategyMap: Map<string, BkTypeStrategyService> = BkTypeFactory.getStrategyMap(this.dataSource, this.httpService, this.configService);
    bkTypeFactory: BkTypeFactory = new BkTypeFactory(this.dataSource, this.httpService, this.configService);
    constructor(private esService: EsService,
                private dataSource: DataSource,
                public configService: ConfigService,
                public httpService: HttpService,
                public logService: LogService,) {
        this.invoiceCombinedSizeThreshold = this.configService.get<number>('INVOICE_COMBINED_SIZE_THRESHOLD');
        this.eStatementCombinedSizeThreshold = this.configService.get<number>('ESTATEMENT_COMBINED_SIZE_THRESHOLD');
    }

    async findEsReconcileList(params: SelectEsReconcileDto): Promise<Paginated<ResponseEsReconcileDto>> {
        let [esList, number] = await this.findEsList(params);

        // @ts-ignore
        const usedMap = new Map<string, number>();
        const res: ResponseEsReconcileDto[] = [];
        try {
            for (const es of esList) {
                const dto = this.transformToDto(es);

                //0 means auto matching , 1 means manual matching
                if (es.match_flag != null && 0 === es.match_flag) {
                    dto.match_list = await this.getReconciliation(es, params, usedMap);
                }
                res.push(dto);
            }
        } catch (e) {
            this.logger.error(`[Es-Reconcile-List] find Es Reconcile List failed. ${e.stack}`)
            throw new HttpException(`[Es-Reconcile-List] find Es Reconcile List failed. Error: ${e}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new Paginated(number, params.page_size, params.page_index, res);
    }

    async findEsList(params: SelectEsReconcileDto): Promise<[Es[], number]> {
        let builder = this.dataSource.createQueryBuilder(Es, 'es')
            .where("es.br_flag IN (:...br_flag)", {br_flag: [0, 1]})
            .andWhere("es.company_code = :company_code", {company_code: params.company_code,})
            .andWhere("es.balance is not null AND es.balance <> :balance", {balance: 0});

        if (params.bank_account) {
            await builder.andWhere("es.bank_account like :bank_account", {bank_account: `%${params.bank_account}%`})
        }

        if (params.start_date && params.end_date) {
            await builder.andWhere("DATE_FORMAT(es.date,'%Y-%m-%d') between :start_date and :end_date", {
                start_date: params.start_date,
                end_date: params.end_date
            })
        }
        if (params.balance_min) {
            await builder.andWhere("es.balance >= :balanceMix", {balanceMix: Number(params.balance_min)})
        }
        if (params.balance_max) {
            await builder.andWhere("es.balance <= :balanceMax", {balanceMax: Number(params.balance_max)})
        }
        if (params.sort) {
            await builder.orderBy(params.sort);
        }
        builder.skip((params.page_index - 1) * params.page_size).take(params.page_size)
        return await builder.getManyAndCount();
    }

    private async getReconciliation(es: Es, params: SelectEsReconcileDto, usedMap): Promise<MatchDto | MatchDto[]> {
        return await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum))
            .find(params, es, usedMap, this.invoiceCombinedSizeThreshold);
    }

    transformToDto(es: Es): ResponseEsReconcileDto {
        // return Object.assign<ResponseEsReconcileDto, Es>(new ResponseEsReconcileDto(), this);
        //TODO: generic Dto to entity
        let res = JSON.parse(JSON.stringify(es));
        res.br_entity_type = 7;
        return res;
    }

    async reconcileSubmit(submitDto: ReconcileSubmitDto): Promise<ReconciliationHistory[]> {
        if (submitDto.charge_fee == null) submitDto.charge_fee = 0.00
        const bkTypeStrategy = this.bkTypeFactory.getBkTypeStrategy(submitDto.br_type.toString(), (submitDto as ReconcileSubmitEeDto).document_no);
        return await bkTypeStrategy.submit(submitDto, this.generateUUID(), this);
    }

    async reconcileSubmitIntegration(submitDto: ReconcileSubmitDto): Promise<ReconciliationHistory[]> {
        if (submitDto.charge_fee == null) submitDto.charge_fee = 0.00
        const bkTypeStrategy = this.bkTypeFactory.getBkTypeStrategy(submitDto.br_type.toString(), (submitDto as ReconcileSubmitEeDto).document_no);
        return await bkTypeStrategy.submitIntegration(submitDto, this.generateUUID(), this);
    }

    async validateSpParams(submitDto: ReconcileSubmitSpDto) {
        let errMsg;
        for (const invoice of submitDto.invoice_list) {
            if (!invoice.invoice_id) errMsg = 'invoice_id is empty';
            if (!invoice.balance) errMsg = errMsg + '; ' + 'balance is empty';
            if (!invoice.bp_number) errMsg = errMsg + '; ' + 'bp_number is empty';
            if (!invoice.reconcile_amount) errMsg = errMsg + '; ' + 'reconcile_amount is empty';
            if (!invoice.engine_document_id) errMsg = errMsg + '; ' + 'engine_document_id is empty';
            if (!invoice.br_type) errMsg = errMsg + '; ' + 'br_type is empty';
            if (!invoice.br_entity_type) errMsg = errMsg + '; ' + 'br_entity_type is empty';
        }
        if(errMsg) {
            await this.dataSource.createQueryBuilder()
                .update(Es)
                .set({'stripe_msg': 'stripe data is incomplete'})
                .where('id = :id', {id: submitDto.statement_id})
                .execute();
            throw new HttpException('stripe data is incomplete!', HttpStatus.BAD_REQUEST);
        }
    }

    public async sendToEngine(data, transactionalEntityManager: EntityManager, uuid, url: string): Promise<any> {
        this.logger.log(`[es_reconcile_submit] Url: ${url}, Data: ${JSON.stringify(data)}`);
        const engineRes = await lastValueFrom(this.httpService.post(url, data)
            .pipe(
                tap(response => {
                    this.logger.log(JSON.stringify(response.data));
                    this.logService.sendLogDataToServer(url, JSON.stringify(data), response.status.toString(), response.data);
                }),
                map(async (response) => {
                    if (response.data.data.success) {
                        // await transactionalEntityManager.update(ReconciliationHistory, {transaction_id: uuid}, {
                        //     document_no: response.data.data.document_no,
                        // });
                        return response.data.data;
                    }
                    throw new HttpException(`${JSON.stringify(response.data)}`, response.status);
                }),
                catchError(e => {
                    this.logger.log(`[es_reconcile_submit] Engine response error: ${JSON.stringify(e.response.data)}`);
                    this.logService.sendLogDataToServer(url, JSON.stringify(data), e.response.status, JSON.stringify(e.response.data));
                    throw new HttpException(`Engine response error: ${JSON.stringify(e.response.data)}`, e.response.status);
                })
            ));
        this.logger.log('[es_reconcile_submit] Engine response successfully: ', JSON.stringify(engineRes));
        return engineRes;
    }

    public generateUUID() {
        return uuidv4();
    }

    async updateEs(newEsBalance: Decimal, es: Es, transactionalEntityManager: EntityManager) {
        const esBrFlag = this.getBrFlag(newEsBalance, es);
        await transactionalEntityManager.update(Es, {id: es.id}, {
            br_flag: esBrFlag,
            balance: newEsBalance.toNumber()
        });
        return esBrFlag;
    }

    async saveHistories(historyList: ReconciliationHistory[], transactionalEntityManager: EntityManager) {
        //insert history
        await transactionalEntityManager.save(ReconciliationHistory, historyList);
    }

    async findEs(submitDto: ReconcileSubmitDto) {
        const es = await this.esService.findOne(submitDto.statement_id);
        if (!es) {
            this.logger.log(`[es_reconcile_submit] E-Statement with id: ${submitDto.statement_id} can not find!`);
            throw new HttpException(`E-Statement with id: ${submitDto.statement_id} can not find!`, HttpStatus.BAD_REQUEST);
        }
        // 2、判断es的对账状态
        if (es.br_flag === BrStatusEnum.PAID) {
            this.logger.log(`[es_reconcile_submit] E-Statement with id: ${submitDto.statement_id} has been reconciled already!`);
            throw new HttpException(`E-Statement with id: ${submitDto.statement_id} has been reconciled already!`, HttpStatus.BAD_REQUEST);
        }
        if (new Decimal(submitDto.balance).comparedTo(submitDto.br_type == 1 ? new Decimal(es.balance).sub(submitDto.charge_fee) : new Decimal(es.balance).add(submitDto.charge_fee)) !== 0.00) {
            this.logger.log(`[es_reconcile_submit] E-Statement with id: ${submitDto.statement_id} has been reconciled already!`);
            throw new HttpException(`Balance (${es.balance}) of E-Statement with id: ${submitDto.statement_id} does not match with input balance (${submitDto.balance})!`, HttpStatus.BAD_REQUEST);
        }
        return es;
    }

    async updateInvoice(entity: EntityTarget<any>, transactionalEntityManager: EntityManager, invoice: ReconcileSubmitDto.Invoice) {
        const findOne = await this.findInvoice(entity, transactionalEntityManager, invoice);
        const newBalance = new Decimal(findOne.balance).sub(new Decimal(invoice.reconcile_amount));
        const brFlag = this.getBrFlag(newBalance, findOne);
        // update invoice
        await transactionalEntityManager.update(entity, {id: findOne.id}, {
            br_flag: brFlag,
            balance: newBalance.toString()
        });
        return {newBalance, brFlag};
    }

    public async findInvoice(entity: EntityTarget<any>, transactionalEntityManager: EntityManager, invoice: ReconcileSubmitDto.Invoice) {
        const res = await transactionalEntityManager.findOne(entity, {where: {id: invoice.invoice_id}});
        if (!res) {
            throw new HttpException(`[es_reconcile_submit] Invoice with ${invoice.invoice_id} can not find!`, HttpStatus.BAD_REQUEST);
        }
        if (new Decimal(res.balance).comparedTo(invoice.balance) !== 0) {
            throw new HttpException(`[es_reconcile_submit] Balance of Ap with ${invoice.invoice_id} does not match with DB!`, HttpStatus.BAD_REQUEST);
        }
        return res;
    }

    public calculateBalance(invoice: ReconcileSubmitDto.Invoice): Decimal {
        return new Decimal(invoice.balance).sub(new Decimal(invoice.reconcile_amount));
    }

    buildHistory(submitDto: ReconcileSubmitDto, newBalance: Decimal, brFlag: number, uuid: string, reconcileAmount: number, invoice: Invoice | Es, brId: number, brEntityType: number, max_reverse_number: number, target: number): ReconciliationHistory {
        const history = new ReconciliationHistory();
        // history.company_id = submitDto.company_id;
        history.company_code = submitDto.company_code;
        history.br_type = invoice.br_type;
        history.br_entity_type = brEntityType;
        history.br_id = brId;
        history.before_balance = invoice.balance;
        history.after_balance = newBalance.toNumber();
        history.after_reverse_balance = newBalance.toNumber();
        history.posting_date = submitDto.posting_date;
        history.reconcile_amount = reconcileAmount;
        history.creator = submitDto.creator;
        history.transaction_id = uuid;
        history.after_br_flag = brFlag;
        history.max_reverse_number = max_reverse_number;
        // CP类型传递的的是创建cp票时引擎返回的对账成功的document_no
        if (invoice.br_type.toString() === BkTypeEnum.CP) {
            if (!(invoice instanceof Es)) {
                history.document_no = invoice.engine_document_id;
            }
        }
        history.target = target;
        return history;
    }

    public getBrFlag(balance: Decimal, entity: BaseEntity): number {
        if (balance.comparedTo(0) === 0) return BrStatusEnum.PAID;
        if (balance.comparedTo(0) === 1) return BrStatusEnum.PARTIAL_PAID;
        //deal with negative
        if (new Decimal((entity as Ap | Ar).balance).comparedTo(0) === -1 && balance.comparedTo((entity as Ap | Ar).balance) === 1) return BrStatusEnum.PARTIAL_PAID;

        throw new HttpException(`[es_reconcile_submit] Balance of ${entity.constructor.name} id:${entity.id} can not less than reconcile amount`, HttpStatus.BAD_REQUEST);

    }


    /**
     * Manual Reconcile
     * @param params
     */
    async findEsManualReconcile(params: ManualEsReconcileDto) {

        const es = await this.esService.findOne(params.statement_id);
        if (!es) {
            this.logger.log(`[Es-Manual-Reconcile] E-Statement with id: ${params.statement_id} can not find!`);
            throw new HttpException(`E-Statement with id: ${params.statement_id} can not find!`, HttpStatus.BAD_REQUEST);
        }

        try {
            const res = this.transformToDto(es);
            if (params.ap_integration && params.ap_integration == 1 && es.br_type.toString() == BkTypeEnum.RP) {
                // search for RP
                const rp_match_list = await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum)).findForManual(params, es);
                // search for PR
                es.br_type = Number(BkTypeEnum.PR);
                const pr_match_list = await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum)).findForManual(params, es);
                //
                res.match_list = [];
                if (Array.isArray(res.match_list)) {
                    res.match_list.push(...rp_match_list, ...pr_match_list);
                }
            }else if (params.ap_integration && params.ap_integration == 1 && es.br_type.toString() == BkTypeEnum.RS) {
                // search for RS
                const rs_match_list = await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum)).findForManual(params, es);
                // search for SR
                es.br_type = Number(BkTypeEnum.SR);
                const sr_match_list = await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum)).findForManual(params, es);
                //
                res.match_list = [];
                if (Array.isArray(res.match_list)) {
                    res.match_list.push(...rs_match_list, ...sr_match_list);
                }
            } else {
                res.match_list = await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum)).findForManual(params, es);
            }
            return res;
        } catch (e) {
            this.logger.error(`[Es-Manual-Reconcile] find Es Manual Reconcile failed. ${e.stack}`)
            throw new HttpException(`[Es-Manual-Reconcile] find Es Manual Reconcile failed. . Error: ${e}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async findEsReconcileById(params) {
        let es = await this.dataSource.createQueryBuilder(Es, 'es')
            .andWhere("es.id = :id", {id: params.id}).getOne();
        // @ts-ignore
        const usedMap = new Map<string, number>();
        const res: ResponseEsReconcileDto[] = [];
        const dto = this.transformToDto(es);
        dto.match_list = await this.getReconciliation(es, params, usedMap);
        return dto;

    }

    private isArrayOfMatchDto(list: MatchDto | MatchDto[]): list is MatchDto[] {
        return Array.isArray(list);
    }
}