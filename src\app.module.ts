import {Module} from '@nestjs/common';
import {AppController} from './app.controller';
import {AppService} from './app.service';
import {DatabaseModule} from "./database/database.module";
import {APP_INTERCEPTOR} from "@nestjs/core";
import {LoggingInterceptor} from "./interceptor/logging.interceptor";
import {ResponseInterceptor} from "./interceptor/response.interceptor";
import {GlModule} from './gl/gl.module';
import {ArModule} from './ar/ar.module';
import {ApModule} from './ap/ap.module';
import {FileUploadModule} from './file-upload/file-upload.module';
import {EsModule} from './es/es.module';
import {EsConnectModule} from './es_connect/es_connect.module';
import {EsHistoryModule} from './es_history/es_history.module';
import {EsReconcileModule} from './es_reconcile/es_reconcile.module';
import {OcrEsModule} from './ocr-es/ocr-es.module';
import {PaginateInterceptor} from "./interceptor/paginate.interceptor";
import {PlaidInfoModule} from './plaid_info/plaid_info.module';
import {OcrInvoiceModule} from './ocr-invoice/ocr-invoice.module';
import {WinstonModule} from 'nest-winston';
import winstonConfig from "../config/winston.config";
import {ConfigModule} from "@nestjs/config";
import configuration from "../config/configuration";
import {OcrUtilsModule} from "./ocr-utils/ocr-utils.module";
import { LogModule } from './log/log.module';
import { CheckModule } from './check/check.module';
import { CoaTopInitModule } from './coa_top_init/coa_top_init.module';
import { ReportModule } from './report/report.module';
import { BasiqModule } from './basiq/basiq.module';
import { ContactModule } from './contact/contact.module';
import { TaskModule } from './task/task.module';
import { InvoiceFlowModule } from './invoice_flow/invoice_flow.module';
import { PaymentFlowModule } from './payment_flow/payment_flow.module';
import { CompanyFlowModule } from './company_flow/company_flow.module';


@Module({
    imports: [
        WinstonModule.forRoot(winstonConfig),
        ConfigModule.forRoot({
            envFilePath: `${process.cwd()}/config/env/${process.env.NODE_ENV}.env`,
            isGlobal: true,
            load: [configuration],
        }),
        DatabaseModule,
        GlModule,
        ArModule,
        ApModule,
        FileUploadModule,
        EsModule,
        EsConnectModule,
        EsHistoryModule,
        EsReconcileModule,
        OcrEsModule,
        PlaidInfoModule,
        OcrInvoiceModule,
        OcrUtilsModule,
        OcrInvoiceModule,
        LogModule,
        CheckModule,
        CoaTopInitModule,
        ReportModule,
        BasiqModule,
        ContactModule,
        TaskModule,
        InvoiceFlowModule,
        PaymentFlowModule,
        CompanyFlowModule
    ],
    controllers: [AppController],
    providers: [
        AppService,
        {provide:APP_INTERCEPTOR,useClass: LoggingInterceptor,},
        {provide:APP_INTERCEPTOR,useClass: ResponseInterceptor,},
        {provide:APP_INTERCEPTOR,useClass: PaginateInterceptor,},
    ],
})
export class AppModule {
}
