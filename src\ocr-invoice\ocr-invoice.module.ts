import {forwardR<PERSON>, Module} from '@nestjs/common';
import { OcrInvoiceService } from './ocr-invoice.service';
import { OcrInvoiceController } from './ocr-invoice.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {HttpModule} from "@nestjs/axios";
import {ConfigModule} from "@nestjs/config";
import {OcrInvoice} from "./entities/ocr-invoice.entity";
import {OcrInvoiceItem} from "./entities/ocr-invoice-item.entity";
import {FileUploadModule} from "../file-upload/file-upload.module";

@Module({
  imports: [TypeOrmModule.forFeature([OcrInvoice, OcrInvoiceItem])],
  controllers: [OcrInvoiceController],
  providers: [OcrInvoiceService],
  exports: [OcrInvoiceService]
})
export class OcrInvoiceModule {}
