import { BaseEntity } from "src/common/entity/base.entity";
import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity } from "typeorm";

@Entity('basiq')
export class Basiq extends BaseEntity {

  @ApiProperty({type: 'string', description: 'company code', example: '3000'})
  @Column({name: 'company_code', type: 'varchar', comment: 'company code'})
  company_code: string;

  @ApiProperty({type: 'number', description: 'bank id associate with payroll', example: '110'})
  @Column({name: 'bank_id', type: 'int', comment: 'bank id associate with payroll'})
  bank_id: number;

  @ApiProperty({type: 'string', description: 'bank account', example: '001-36721-1983873'})
  @Column({name: 'bank_account', nullable: true, type: 'varchar', length: 50, comment: 'bank account'})
  bank_account: string;

  @ApiProperty({type: 'string', description: 'bank short name', example: 'BMO'})
  @Column({name: 'bank_name', nullable: true, type: 'varchar', length: 50, comment: 'bank name'})
  bank_name: string;

  @ApiProperty({type: 'string', description: 'currency:  1 CNY; 2 CAD; 3 USD;', example: '2'})
  @Column({name: 'currency', nullable: true, type: 'varchar', length: 30, comment: 'currency:  1 CNY; 2 CAD; 3 USD;'})
  currency: string;

  @ApiProperty({type: 'string', description: 'account type: 1 cheque; 2 saving; 3 credit', example: '1'})
  @Column({name: 'account_type', nullable: true, type: 'tinyint', comment: 'account type: 1 cheque; 2 saving; 3 credit'})
  account_type: number;

  @ApiProperty({type: 'string', description: 'user_id in basiq', example: '7434d49606b54abeb6c39dcc15299e49'})
  @Column({name: 'basiq_user_id', nullable: true, type: 'varchar', length: 200, comment: 'userId in basiq'})
  basiq_user_id: string;

  @ApiProperty({type: 'string', description: 'account unique id in basiq'})
  @Column({name: 'basiq_account_id', nullable: true, type: 'varchar', length: 50,  comment: 'bank unique id in basiq'})
  basiq_account_id: string;

  @ApiProperty({type: 'number', description: 'basiq return stats ： 0 initialization 、 1 success  、 2 failed', default: 0, example: 0})
  @Column({name: 'basiq_status', nullable: true, type: 'tinyint', default: 0, comment: 'basiq return stats ： 0 initialization 、 1 success  、 2 failed'})
  basiq_status: number;

  @ApiProperty({type: 'string', description: 'basiq token generate time'})
  @Column({name: 'basiq_access_time', nullable: true, type: 'datetime', comment: 'basiq token generate time'})
  basiq_access_time: string;

  @ApiProperty({type: 'string', description: 'basiq init fetch date'})
  @Column({name: 'basiq_init_fetch_date', nullable: true, type: 'date', comment: 'basiq init fetch date'})
  basiq_init_fetch_date: string;

  @ApiProperty({type: 'string', description: 'basiq last fetch date'})
  @Column({name: 'basiq_last_fetch_date', nullable: true, type: 'date', comment: 'basiq last fetch date'})
  basiq_last_fetch_date: string;

  // @ApiProperty({type: 'string', description: 'basiq transaction id'})
  // @Column({name: 'basiq_transaction_id', nullable: true, type: 'varchar', length: 100,  comment: 'fetch bank es'})
  // basiq_transaction_id: string;

  // @ApiProperty({nullable: true, type: 'string', description: 'Sending mail status: 0 not sending/failed, 1 sending successfully'})
  // @Column({name: 'email_status', nullable: true, type: 'tinyint', default: 0,  comment: 'Sending mail status: 0 not sending/failed, 1 sending successfully'})
  // email_status: number;
}
