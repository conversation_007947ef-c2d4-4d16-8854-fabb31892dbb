import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class ManualEsReconcileDto {

    @ApiProperty({type: String, description: 'company code', example: '3000'})
    @IsNotEmpty()
    company_code: string;


    @ApiProperty({type: Number, description: 'es id', example: 1314})
    @IsNotEmpty()
    statement_id: number;

    @ApiProperty({type: String, required: false,description: 'Bank account', example: '001-36721-1983873'})
    bank_account: string;

    @ApiProperty({type: String, required: false,description: 'ar integration'})
    ar_integration: number;

    @ApiProperty({type: String, required: false,description: 'ap integration'})
    ap_integration: number;

    // @ApiProperty({type: String, required: false, description: 'order by', example: 'sort[date]=desc'})
    // sort: string;

}