import {<PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor} from '@nestjs/common';
import {map, Observable} from 'rxjs';
import {Paginated} from "../common/utils/index.util";

export interface Response {
    statusCode: number;
    message: string;
    data: any;
    paginated?: {
        total: number;
        page_index: number;
        page_size: number;
        page_total: number;
    }
}

export class ResponseInterceptor<T extends Paginated<T>> implements NestInterceptor<T, Response> {
    intercept(
        context: ExecutionContext,
        next: CallHandler<T>,
    ): Observable<Response> | Promise<Observable<Response>> {
        return next.handle().pipe(
            map((data) => {
                //if paginated response
                if (data && data.constructor.name === 'Paginated') {
                    return {
                        statusCode: 200,
                        message: 'Success',
                        data: data.data,
                        paginated: {
                            total: data.total,
                            page_index: data.page_index,
                            page_size: data.page_size,
                            page_total: data.page_total,
                        }
                    }
                }
                return {
                    statusCode: 200,
                    message: 'Success',
                    data: data
                }
            }),
        );
    }
}
