import {Body, Controller, Get, Post, Query} from '@nestjs/common';
import {PlaidInfoService} from './plaid_info.service';
import {ApiOperation, ApiResponse, ApiTags} from "@nestjs/swagger";
import {BaseController} from "../common/controller/base.controller";
import {PlaidInfo} from "./entities/plaid_info.entity";
import {Paginated} from "../common/utils/index.util";
import {SearchCompanyBankDto} from "./dto/search-company-bank.dto";
import {CompanyBankResponseDto} from "./dto/company-bank-response.dto";

@ApiTags('Plaid-Bank-Info')
@Controller('plaid-bank-info')
export class PlaidInfoController extends BaseController<PlaidInfo> {
  constructor(private readonly plaidInfoService: PlaidInfoService) {
    super(plaidInfoService);
  }

  @Get()
  @ApiOperation({summary: 'Find all'})
  @ApiResponse({status: 200, description: 'Ok'})
  async findAll(params: PlaidInfo): Promise<PlaidInfo[] | Paginated<PlaidInfo>> {
    return super.findAll(params);
  }

  @Get('/company-bank')
  @ApiOperation({summary: 'Find company bank'})
  @ApiResponse({status: 200, description: 'Ok'})
  async findCompanyBank(@Query()params: SearchCompanyBankDto): Promise<CompanyBankResponseDto[] | Paginated<CompanyBankResponseDto>> {
    return this.plaidInfoService.findCompanyBank(params);
  }


  @Post()
  @ApiOperation({summary: 'Save entity'})
  @ApiResponse({
    status: 201,
    description: 'The record has been successfully created.',
  })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  async create(@Body() entity: PlaidInfo): Promise<PlaidInfo> {
    return super.create(entity);
  }
}
