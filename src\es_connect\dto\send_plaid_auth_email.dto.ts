import {IsNotEmpty} from "class-validator";
import {ApiProperty} from "@nestjs/swagger";

export class SendPlaidAuthEmailDto {

    // @ApiProperty({type: Number, description: 'plaid_id', example: 10})
    // @IsNotEmpty()
    // plaid_id: number;

    @ApiProperty({type: Number, description: 'bank id', example: 10})
    @IsNotEmpty()
    bank_id: number;

    @ApiProperty({type: String, description: 'Bank account', example: '001-36721-1983873'})
    @IsNotEmpty()
    bank_account: string;

    @ApiProperty({type: String, description: 'Bank name', example: 'BMO'})
    @IsNotEmpty()
    bank_name: string;


    @ApiProperty({type: String, description: 'Bank name', example: '1 CNY; 2 CAD; 3 USD'})
    @IsNotEmpty()
    currency: string;

    @ApiProperty({type: String, description: 'company code', example: '2000'})
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({type: String, description: 'company name', example: 'WADS'})
    @IsNotEmpty()
    company_name: string;

    @ApiProperty({type: String, description: 'company email', example: 'wads.gmail.com'})
    @IsNotEmpty()
    company_email: string;

    @ApiProperty({type: String, description: 'bank type', example: 'saving, cheque, credit'})
    @IsNotEmpty()
    bank_type: string;
}
