import {ApiProperty} from "@nestjs/swagger";
import {BaseDTO} from "../../common/dto/base.dto";

export class ReportDetailSummaryDto extends BaseDTO {

    @ApiProperty({description: 'invoice currency'})
    invoice_currency: string;

    @ApiProperty({description: 'total amount'})
    total_amount: number;

    @ApiProperty({description: 'reconcile amount'})
    reconcile_amount: number;

    @ApiProperty({description: 'balance: sum of to 0, 31, 61, 91'})
    balance: number;

    @ApiProperty({description: 'balance to 0'})
    balance_to_0: number;

    @ApiProperty({description: 'balance to 31'})
    balance_to_31: number;

    @ApiProperty({description: 'balance to 61'})
    balance_to_61: number;

    @ApiProperty({description: 'balance to 91'})
    balance_to_91: number;

}