export class EnumUtil {

    /**
     * get enum key from value
     * @param value
     * @param en
     */
    public static getEnumKey(value, en: Record<string, string | number>) {
        return Object.entries(en)
            .find(([key, val]) => val === value)?.[0];
    }

    /**
     * 判断传入的value是否存在
     * @param value
     * @param en
     */
    public static exists(value, en: any){
        return Object.values(en).includes(value as any);
    }

    /**
     * 判断传入的key是否存在
     * @param key
     * @param en
     */
    public static existsByKey(key, en: any){
        return Object.keys(en).includes(key as any);
    }
}