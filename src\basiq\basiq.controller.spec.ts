import { Test, TestingModule } from '@nestjs/testing';
import { BasiqController } from './basiq.controller';
import { BasiqService } from './basiq.service';

describe('BasiqController', () => {
  let controller: BasiqController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BasiqController],
      providers: [BasiqService],
    }).compile();

    controller = module.get<BasiqController>(BasiqController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
