import {Controller, Get, Post, Query} from '@nestjs/common';
import {ApiOperation, ApiResponse, ApiTags} from "@nestjs/swagger";
import {EsReconcileReverseService} from "./es_reconcile_reverse.service";
import {SelectReversesList} from "../dto/select_reverses_list.dto";
import {Paginated} from "../../common/utils/index.util";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {RequestReverseEsDto} from "../dto/request_reverse_es.dto";
import {RequestReverseInvoiceDto} from "../dto/request_reverse_invoice.dto";
import {RequestReverseTransactionDto} from "../dto/request_reverse_transaction.dto";
import {SelectReversesInvoiceList} from "../dto/select_reverses_invoice_list.dto";

@Controller('es-reconcile-reverse')
@ApiTags('Es-Reconcile-Reverse')
export class EsReconcileReverseController {

    constructor(private readonly service: EsReconcileReverseService) {
    }

    @Get("/e-statements")
    @ApiOperation({summary: 'Search reverse list of e-statements reconciliation'})
    @ApiResponse({status: 200, description: 'Ok'})
    async findReconcileList(@Query() params: SelectReversesList): Promise<Paginated<ReconciliationHistory>> {
        return await this.service.findByEs(params);
    }

    @Get("/invoices")
    @ApiOperation({summary: 'Search reverse list of invoices reconciliation'})
    @ApiResponse({status: 200, description: 'Ok'})
    async findReconcileListByInvoices(@Query() params: SelectReversesInvoiceList): Promise<Paginated<ReconciliationHistory>> {
        return await this.service.findByInvoices(params);
    }

    // @Post()
    // @ApiOperation({summary: 'Reverse reconciliation'})
    // @ApiResponse({status: 200, description: 'Ok'})
    // async reverse(@Query() params: RequestReverseDto) {
    //     return await this.service.reverseOneByOne(params);
    // }

    @Post('/es')
    @ApiOperation({summary: 'Reverse es'})
    @ApiResponse({status: 200, description: 'Ok'})
    async reverseEs(@Query() params: RequestReverseEsDto) {
        return await this.service.reverseEs(params.statement_id, params.creator);
    }

    @Post('/transaction')
    @ApiOperation({summary: 'Reverse reconcile transaction'})
    @ApiResponse({status: 200, description: 'Ok'})
    async reverseTransaction(@Query() params: RequestReverseTransactionDto) {
        return await this.service.reverseTransaction(params);
    }

    @Post('/transaction/integration')
    @ApiOperation({summary: 'Reverse reconcile transaction'})
    @ApiResponse({status: 200, description: 'Ok'})
    async reverseTransactionIntegration(@Query() params: RequestReverseTransactionDto) {
        return await this.service.reverseTransactionIntegration(params);
    }

    @Post('/invoice')
    @ApiOperation({summary: 'Reverse invoice'})
    @ApiResponse({status: 200, description: 'Ok'})
    async reverseInvoice(@Query() params: RequestReverseInvoiceDto) {
        return await this.service.reverseInvoice(params.br_id, params.br_type, params.creator);
    }

}
