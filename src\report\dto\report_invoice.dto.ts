import {ApiProperty} from "@nestjs/swagger";
import {BaseDTO} from "../../common/dto/base.dto";
import {IsNotEmpty} from "class-validator";
import {Column} from "typeorm";
import {Ar} from "../../ar/entities/ar.entity";
import {Ap} from "../../ap/entities/ap.entity";

export class ReportInvoiceDto extends BaseDTO {

    @ApiProperty({ description: 'id' })
    id: number;

    @ApiProperty({ description: 'company code' })
    company_code: string;

    @ApiProperty({ description: 'company name' })
    company_name: string;

    @ApiProperty({description: 'posting date'})
    posting_date: Date;

    @ApiProperty({description: 'engine doc no'})
    engine_doc_no: string;

    @ApiProperty({description: 'sap document id'})
    sap_document_id: string;

    @ApiProperty({description: 'bp no'})
    bp_no: string;

    @ApiProperty({description: 'bp name'})
    bp_name: string;

    @ApiProperty({description: 'invoice currency'})
    invoice_currency: string;

    @ApiProperty({description: 'invoice reference'})
    invoice_reference: string;

    @ApiProperty({description: 'invoice no'})
    invoice_no: string;

    @ApiProperty({description: 'br type'})
    br_type: string;

    @ApiProperty({description: 'net amount'})
    net_amount: number;

    @ApiProperty({description: 'discount'})
    discount: number;

    @ApiProperty({description: 'mx_isr'})
    mx_isr: number;

    @ApiProperty({description: 'mx_iva'})
    mx_iva: number;

    @ApiProperty({description: 'tax content'})
    tax_content: JSON;

    @ApiProperty({description: 'total tax'})
    total_tax: number;

    @ApiProperty({description: 'total fee'})
    total_fee: number;

    @ApiProperty({description: 'balance'})
    balance: number;

    @ApiProperty({description: 'reconcile_posting_date'})
    reconcile_posting_date: Date;

    @ApiProperty({description: 'reconcile_amount'})
    reconcile_amount: number;

    public convertToDtoFromAr(ar: any) {
        this.id = ar.id;
        this.company_code = ar.company_code;
        this.company_name = ar.company_name;
        this.posting_date = ar.posting_date;
        this.bp_no = ar.bill_to_customer_id;
        this.bp_name = ar.bill_to_company;
        this.invoice_currency = ar.invoice_currency;
        this.invoice_reference = ar.reference_no;
        this.invoice_no = ar.invoice_no;
        this.engine_doc_no = ar.engine_document_id;
        this.sap_document_id = ar.sap_document_id;
        this.br_type = ar.br_type;
        this.net_amount = ar.net_amount;
        this.discount = ar.discount;
        this.mx_isr = ar.mx_isr;
        this.mx_iva = ar.mx_iva;
        this.tax_content = ar.tax_content;
        this.total_tax = ar.total_tax;
        this.total_fee = ar.total_fee;
        this.balance = ar.after_balance;
        this.reconcile_posting_date = ar.reconcile_posting_date;
        this.reconcile_amount = ar.reconcile_amount;
        return this;
    }

    public convertToDtoFromAp(ap: any) {
        this.id = ap.id;
        this.company_code = ap.company_code;
        this.company_name = '';
        this.posting_date = ap.posting_date;
        this.bp_no = ap.issuer_id;
        this.bp_name = ap.issuer_name;
        this.invoice_currency = ap.invoice_currency;
        this.invoice_reference = ap.reference_no;
        this.invoice_no = ap.invoice_no;
        this.engine_doc_no = ap.engine_document_id;
        this.sap_document_id = ap.sap_document_id;
        this.br_type = ap.br_type;
        this.net_amount = ap.net_amount;
        this.discount = ap.discount;
        this.mx_isr = ap.mx_isr;
        this.mx_iva = ap.mx_iva;
        this.tax_content = ap.tax_content;
        this.total_tax = ap.total_tax;
        this.total_fee = ap.total_fee;
        this.balance = ap.after_balance;
        this.reconcile_posting_date = ap.reconcile_posting_date;
        this.reconcile_amount = ap.reconcile_amount;
        return this;
    }
}