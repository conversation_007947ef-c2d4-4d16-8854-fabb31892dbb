import { Module } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportController } from './report.controller';
import {ArModule} from "../ar/ar.module";
import {ApModule} from "../ap/ap.module";
import {TypeOrmModule} from "@nestjs/typeorm";
import {Ar} from "../ar/entities/ar.entity";
import {ArItem} from "../ar/entities/ar.item.entity";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {Ap} from "../ap/entities/ap.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Ar, Ap, ReconciliationHistory])],
  controllers: [ReportController],
  providers: [ReportService]
})
export class ReportModule {}
