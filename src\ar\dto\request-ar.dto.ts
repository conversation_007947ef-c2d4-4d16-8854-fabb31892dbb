import {ApiProperty} from "@nestjs/swagger";
import {BaseDTO} from "../../common/dto/base.dto";
import {IsDateString, IsEmail, IsEnum, IsNotEmpty} from "class-validator";

export class RequestArDto extends BaseDTO {

    @ApiProperty({ description: 'company_code' })
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({ description: 'posting_date[$bw]=[2022-06-01,2022-10-30]', required: false })
    'posting_date[$bw]': string;


}
