export enum BkTypeEnum {

    /**
     * AR: regular sales
     */
    RS = '0',
    /**
     * AP: regular purchase
     */
    RP = '1',
    /**
     * purchase refund
     */
    PR = '2',
    /**
     * sales refund
     */
    SR = '3',
    /**
     * funding trans
     * es对es， withdrawal找deposit相等的值， deposit找withdrawal相等的值
     */
    FT = '4',
    /**
     * payroll
     */
    PY = '5',
    /**
     * Es of different currency
     * es对es， 用于CAD 和 USD 流水间对账。这种对账不自动匹配，只有手动匹配，手动对账时只列出公司名下币种不同的所有流水即可。
     * 对账的时候也不强求金额相等。对账动作也不传SAP。
     */
    FX = '6',

    /**
     * regular Es
     */
    ES = '7',
    /**
     * Year End Document
     */
    YED = '8',

    /**
     * cash pay invoice
     * 创建cash pay invoice， 先发送给引擎，然后会自动对账
     */
    CP = '9',

    /**
     * EE, Bank Reconcile or GL Reconcile
     * 创建票的同时，直接对账
     */
    BC = '10',


    /**
     * stripe pay
     * 对账不平，强制对账不考虑账是否平
     */
    SP = '11',

    /**
     * Pre pay
     * 对账不平，强制对账不考虑账是否平
     */
    PP = '12',

    /**
     * Deposit And Return
     * 对账不平，强制对账不考虑账是否平
     */
    DR = '13'
}

