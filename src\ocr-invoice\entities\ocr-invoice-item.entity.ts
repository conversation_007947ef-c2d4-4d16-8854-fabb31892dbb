import {Column, Entity, Join<PERSON><PERSON>umn, ManyToOne} from 'typeorm';
import { BaseEntity } from '../../common/entity/base.entity';
import {OcrInvoice} from "./ocr-invoice.entity";


@Entity()
export class OcrInvoiceItem extends BaseEntity {

    @Column({name: 'item_no', nullable: true, comment: 'item no'})
    item_no: number;

    @Column({name: 'ocr_invoice_id', comment: 'invoice id'})
    ocr_invoice_id: number;

    @Column({name: 'model', nullable: true, comment: 'model'})
    model: string;

    @Column({name: 'description', nullable: true, comment: 'description'})
    description: string;

    @Column({name: 'qty', nullable: true, type: 'numeric', precision: 10, scale: 2, comment: 'qty'})
    qty: number;

    @Column({name: 'unit_price', nullable: true, type: 'numeric', precision: 10, scale: 2, comment: 'unit price'})
    unit_price: number;

    @Column({name: 'total', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total'})
    total: number;

    @Column({name: 'coa_code', nullable: true, comment: 'CoA code'})
    coa_code: string;

    @Column({name: 'bank_account', nullable: true, comment: 'bank account'})
    bank_account: string;

    @ManyToOne(() => OcrInvoice, ocrInvoice => ocrInvoice.items)
    @JoinColumn({name: 'ocr_invoice_id'})
    ocrInvoice: OcrInvoice;

}

