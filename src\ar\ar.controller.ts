import {Controller, Get, Post, Body, Param, Query, Patch, Headers} from '@nestjs/common';
import { ArService } from './ar.service';
import { CreateArDto } from './dto/create-ar.dto';
import {ApiTags} from "@nestjs/swagger";
import {BaseController} from "../common/controller/base.controller";
import {Ar} from "./entities/ar.entity";
import {RequestArDto} from "./dto/request-ar.dto";

@ApiTags('Sales')
@Controller('ar')
export class ArController extends BaseController<Ar> {
  constructor(private readonly arService: ArService) {
    super(arService);
  }

  @Post()
  createAr(@Body() createArDto: CreateArDto) {
    return this.arService.createAr(createArDto);
  }

  @Get('coa/top')
  topCoA(@Query() params: any) {
    return this.arService.topCoA(params);
  }

  @Get()
  findAll(@Query() queryData: RequestArDto) {
    return this.arService.findAllAr(queryData);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.arService.findOne(+id);
  }

  @Post(':id/email')
  emailToCustomer(@Param('id') id: string) {
    return this.arService.emailToCustomer(+id);
  }

  @Patch(`:id/reverse`)
  reverse(@Param('id') id: string, @Body() param: any) {
    return this.arService.reverse(+id, param);
  }

  @Patch(`:id/cash`)
  convertToCash(@Param('id') id: string, @Body() param: any) {
    return this.arService.convertToCash(+id, param);
  }

  @Post('pdf/batch')
  generatePdfBatch(@Body() param: any) {
    return this.arService.generatePdfBatch(param);
  }

  @Patch('/bill-to-email')
  updateBillToEmail(@Body() param: any) {
    return this.arService.updateBillToEmail(param);
  }

}
