import {Es} from "../../es/entities/es.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";
import {BankCodeStrategyService} from "../bank_code_strategy.service";

export class NOVACode extends BankCodeStrategyService {
    /**
     * Nova Scotia
     * parseCsvData()
     * 标准CSV文件格式，父类方法处理
     * @param file
     */


    /**
     * Nova Scotia
     * detectCsvHeaderAndData()
     * 标准导入格式，父类方法处理
     * @param csvData
     * @param keywords
     */


    /**
     * Nova Scotia
     * buildEsDtoList()
     * 通用数据格式，父类方法处理
     * @param csvHeaderData
     */
}