import {BadRequestException, HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import { CreatePaymentFlowDto } from './dto/create-payment_flow.dto';
import { UpdatePaymentFlowDto } from './dto/update-payment_flow.dto';
import {BaseService} from "../common/service/base.service";
import {PaymentApprovalFlow} from "./entities/payment_flow.entity";
import {v4 as uuidv4} from 'uuid';
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, Repository} from "typeorm";
import {Ap} from "../ap/entities/ap.entity";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {BkFlowLevelEnum} from "../common/enum/bk.flow.level.enum";
import {Paginated} from "../common/utils/index.util";

@Injectable()
export class PaymentFlowService extends BaseService<PaymentApprovalFlow> {

  private readonly logger = new Logger(PaymentFlowService.name);

  constructor(
      @InjectRepository(PaymentApprovalFlow) private readonly paymentFlowRepository: Repository<PaymentApprovalFlow>,
      @InjectRepository(Ap) private readonly apRepository: Repository<Ap>,
      private configService: ConfigService,
      private httpService: HttpService,
      private dataSource: DataSource,
  ) {
    super(paymentFlowRepository, 'paymentFlow');
  }

  async createPaymentFlow(createPaymentFlowDto: CreatePaymentFlowDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let flow: PaymentApprovalFlow = new PaymentApprovalFlow();
    flow = Object.assign(flow, createPaymentFlowDto);

    try {
      if (!flow.invoice_id) {
        throw new HttpException('Invoice id id is required.', 400);
      }
      if (!flow.level) {
        throw new HttpException('Company code id is required.', 400);
      }
      if (flow.level != BkFlowLevelEnum.LEVEL_1 && flow.level != BkFlowLevelEnum.LEVEL_2 && flow.level != BkFlowLevelEnum.LEVEL_3
          && flow.level != BkFlowLevelEnum.LEVEL_4 && flow.level != BkFlowLevelEnum.LEVEL_5 && flow.level != BkFlowLevelEnum.LEVEL_6
          && flow.level != BkFlowLevelEnum.LEVEL_7 && flow.level != BkFlowLevelEnum.LEVEL_8
      ) {
        throw new HttpException('Level is not correct.', 400);
      }
      if (!flow.email_list) {
        throw new HttpException('Email list is required.', 400);
      }
      // add flow
      await queryRunner.manager.save<PaymentApprovalFlow>(flow);
      // commit transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(error, error.status);
    } finally {
      await queryRunner.release();
    }
    return flow;
  }

  async createPaymentFlowBatch(createPaymentFlowDtoList: CreatePaymentFlowDto[]) {
    if (!createPaymentFlowDtoList || !createPaymentFlowDtoList.length || createPaymentFlowDtoList.length == 0)
      throw new BadRequestException('the company flow data is required.');
    const paymentFlowList = [];
    // set uuid
    const uuid = this.generateUUID();
    for (let i=0; i<createPaymentFlowDtoList.length; i++) {
      try {
        const flow = createPaymentFlowDtoList[i];
        flow.uuid = uuid;
        const newFlow = await this.createPaymentFlow(flow);
        paymentFlowList.push(newFlow)
      }catch (e) {
        throw new HttpException(e.message, e.status);
      }
    }
    return paymentFlowList;
  }

  async findAllPaymentFlow(params?: any) {
    try {
      const {where, order} = this.parseParams.parseQuery(params);
      if (params.page_index) {
        const {page_index, page_size = 10} = params;
        const [result, total] = await this.paymentFlowRepository.findAndCount({
          where: where,
          order: order,
          take: page_size,
          skip: (page_index - 1) * page_size
        });
        return new Paginated(total, page_size, page_index, result)
      }
      return await this.paymentFlowRepository.find({
        where: where,
        order: order
      });
    } catch (e) {
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  findOnePaymentFlow(id: number) {
    return `This action returns a #${id} paymentFlow`;
  }

  updatePaymentFlow(id: number, updatePaymentFlowDto: UpdatePaymentFlowDto) {
    return `This action updates a #${id} paymentFlow`;
  }

  remove(id: number) {
    return `This action removes a #${id} paymentFlow`;
  }

  public generateUUID() {
    return uuidv4();
  }
}
