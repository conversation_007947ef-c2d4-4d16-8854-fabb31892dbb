import {HttpException, HttpStatus, Injectable, Logger} from '@nestjs/common';
import {SelectReversesList} from "../dto/select_reverses_list.dto";
import {Brackets, DataSource, Not, ObjectLiteral, Raw} from "typeorm";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {Es} from "../../es/entities/es.entity";
import {ResponseEsReverseListDto} from "../dto/response_es_reverse_list.dto";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {Ap} from "../../ap/entities/ap.entity";
import {Ar} from "../../ar/entities/ar.entity";
import {Paginated} from "../../common/utils/index.util";
import {Decimal} from "decimal.js";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {es} from "date-fns/locale";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {BrStatusEnum} from "../../common/enum/br.status.enum";
import {EnumUtil} from "../../common/enum/enum.util";
import {BkTypeFactory} from "../bk.type.factory";
import {RequestReverseTransactionDto} from "../dto/request_reverse_transaction.dto";
import {BkEntityTypeEnum} from "../../common/enum/bk.entity_type.enum";
import {SelectReversesInvoiceList} from "../dto/select_reverses_invoice_list.dto";
import {LogService} from "../../log/log.service";

@Injectable()
export class EsReconcileReverseService {

    private readonly logger = new Logger(EsReconcileReverseService.name);

    // brTypeStrategyMap: Map<string, BkTypeStrategyService> = BkTypeFactory.getStrategyMap(this.dataSource, this.httpService, this.configService);
    bkTypeFactory: BkTypeFactory = new BkTypeFactory(this.dataSource, this.httpService, this.configService);

    constructor(private dataSource: DataSource, private configService: ConfigService, private httpService: HttpService,  private logService: LogService) {
    }

    async findByEs(params: SelectReversesList): Promise<Paginated<ReconciliationHistory>> {
        const {total, esList} = await this.findEsHistoryList(params);
        const resList: ResponseEsReverseListDto[] = [];

        for (let es of esList) {
            const dto = new ResponseEsReverseListDto();
            Object.assign(dto, es);
            const invoiceList: ReconciliationHistory[] = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
                .where("rh.transaction_id = :transaction_id", {transaction_id: es.transaction_id})
                .andWhere("rh.br_id != :br_id", {br_id: es.br_id})
                .getMany();

            for (let invoice of invoiceList) {
                invoice.entity = invoice.br_entity_type.toString() === BkEntityTypeEnum.AP ?
                    await this.findInvoice(Ap, 'ap', invoice.br_id) :
                    await this.findInvoice(Ar, 'ar', invoice.br_id);
            }

            dto.invoice_list = invoiceList;
            resList.push(dto)
        }
        return new Paginated(total, params.page_size, params.page_index, resList);
    }

    private async findEsHistoryList(params: SelectReversesList) {
        let builder = this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .innerJoinAndMapOne('rh.entity', Es, 'es', params.desc
                ? `es.id = rh.br_id and es.bank_account like '%${params.bank_account}%' and LOWER(es.description) like '%${params.desc.toLowerCase()}%'`
                : `es.id = rh.br_id and es.bank_account like '%${params.bank_account}%'`)
            .where("rh.br_entity_type = 7 and rh.target = :target", {target: 0})
            .andWhere("rh.company_code = :company_code", {company_code: params.company_code,});

        if (params.br_id) {
            await builder.andWhere("rh.br_id = :br_id", {br_id: params.br_id})
        }
        if (params.start_date && params.end_date) {
            await builder.andWhere("DATE_FORMAT(rh.posting_date,'%Y-%m-%d') between :start_date and :end_date", {
                start_date: params.start_date,
                end_date: params.end_date
            })
        }
        if (params.minTotalFee && params.maxTotalFee) {

            await builder.andWhere(
                new Brackets((qb) => {
                    qb.where("(`es`.`withdrawal` = 0 && es.deposit between :minTotalFee and :maxTotalFee)", {
                        minTotalFee: params.minTotalFee,
                        maxTotalFee: params.maxTotalFee
                    }).orWhere("(`es`.`deposit` = 0 && es.withdrawal between :minTotalFee and :maxTotalFee)", {
                        minTotalFee: params.minTotalFee,
                        maxTotalFee: params.maxTotalFee
                    })
                })
            )

            // await builder.andWhere("es.deposit between :minTotalFee and :maxTotalFee", {
            //     minTotalFee: params.minTotalFee,
            //     maxTotalFee: params.maxTotalFee
            // })
        }
        if (params.minTotalFee && !params.maxTotalFee) {
            await builder.andWhere(
                new Brackets((qb) => {
                    qb.where("(`es`.`withdrawal` = 0 && es.deposit >= :minTotalFee)", {
                        minTotalFee: params.minTotalFee,
                    }).orWhere("(`es`.`deposit` = 0 && es.withdrawal >= :minTotalFee)", {
                        minTotalFee: params.minTotalFee,
                    })
                })
            )
            // await builder.andWhere("rh.reconcile_amount >= :minTotalFee", {
            //     minTotalFee: params.minTotalFee,
            // })
        }
        if (!params.minTotalFee && params.maxTotalFee) {
            await builder.andWhere(
                new Brackets((qb) => {
                    qb.where("(`es`.`withdrawal` = 0 && es.deposit <= :maxTotalFee)", {
                        maxTotalFee: params.maxTotalFee,
                    }).orWhere("(`es`.`deposit` = 0 &&  es.withdrawal <= :maxTotalFee)", {
                        maxTotalFee: params.maxTotalFee,
                    })
                })
            )
            // await builder.andWhere("rh.reconcile_amount <= :maxTotalFee", {
            //     maxTotalFee: params.maxTotalFee,
            // })
        }

        if (params.sort) {
            await builder.orderBy(params.sort)
        }

        builder.skip((params.page_index - 1) * params.page_size).take(params.page_size)
        const [esList, total] = await builder.getManyAndCount();
        // if (params.payer_payee || params.desc ) {
        //     let filteredEsList = esList;
        //     // if (params.bank_account) {
        //     //     filteredEsList = filteredEsList.filter(es => {
        //     //         return (es.entity as Es).bank_account === params.bank_account
        //     //     });
        //     // }
        //     if (params.payer_payee) {
        //         filteredEsList = filteredEsList.filter(es => {
        //             return (es.entity as Es).payer_payee && (es.entity as Es).payer_payee.search(params.payer_payee) != -1
        //         });
        //     }
        //     if (params.desc) {
        //         filteredEsList = filteredEsList.filter(es => {
        //             return (es.entity as Es).description && (es.entity as Es).description.search(params.desc) != -1
        //         });
        //     }
        //     return {total: total, esList: filteredEsList};
        // }
        return {total, esList: esList};
    }

    private findInvoice(entity, alias, id, queryRunner?: QueryRunner): Promise<Ar | Ap | ObjectLiteral> {
        if (!queryRunner) {
            return this.dataSource.createQueryBuilder(entity, alias)
                .where(`${alias}.id = :id`, {id: id})
                .getOne();
        }
        return queryRunner.manager.findOne(entity, {
            where: {
                id: id
            }
        })
    }

    async findByInvoices(params: SelectReversesInvoiceList): Promise<Paginated<ReconciliationHistory>> {
        let builder = this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .where("rh.target = :target", {target: 1})
            .andWhere("rh.company_code = :company_code", {company_code: params.company_code});

        if (params.br_id) {
            await builder.andWhere("rh.br_id = :br_id", {br_id: params.br_id})
        }
        if (params.start_date && params.end_date) {
            await builder.andWhere("DATE_FORMAT(rh.create_time,'%Y-%m-%d') between :start_date and :end_date", {
                start_date: params.start_date,
                end_date: params.end_date
            })
        }
        if (params.sort) {
            await builder.orderBy(params.sort)
        }
        builder.skip((params.page_index - 1) * params.page_size).take(params.page_size)
        const [invoiceList, total] = await builder.getManyAndCount();

        const resList: ResponseEsReverseListDto[] = [];

        for (let item of invoiceList) {

            item.entity = item.br_entity_type.toString() === BkEntityTypeEnum.AP ?
                await this.findInvoice(Ap, 'ap', item.br_id) :
                await this.findInvoice(Ar, 'ar', item.br_id);

            const dto = new ResponseEsReverseListDto();
            Object.assign(dto, item);
            dto.invoice_list = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
                .leftJoinAndMapOne('rh.entity', Es, 'es', 'es.id = rh.br_id')
                .where("rh.transaction_id = :transaction_id", {transaction_id: item.transaction_id})
                .andWhere("rh.br_entity_type = :br_entity_type", {br_entity_type: 7})
                .getMany();
            resList.push(dto)
        }
        return new Paginated(total, params.page_size, params.page_index, resList);
    }

    /**
     * reverse es without db transaction
     * @param statement_id
     * @param creator
     * @param queryRunner
     */
    async reverseEsWithoutDBTransaction(statement_id: number, creator: number, queryRunner: QueryRunner): Promise<void> {

        // find es history with entity
        const esHistoryList: ReconciliationHistory[] = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .leftJoinAndMapOne('rh.entity', Es, 'es', 'es.id = rh.br_id')
            .where("rh.br_entity_type = 7 and rh.target = 0 and reverse_number < max_reverse_number and rh.br_id = :br_id", {br_id: statement_id})
            .getMany();

        if (!esHistoryList || esHistoryList.length === 0) throw new HttpException(`[es-reconcile-reverse] Es (${[statement_id]}) can not find!`, HttpStatus.BAD_REQUEST);

        for (let esHistory of esHistoryList) {
            //find invoice history
            const invoiceHistoryList = await queryRunner.manager.find(ReconciliationHistory, {
                where: {
                    // br_entity_type: In([0, 1]),
                    reverse_number: Raw((alias) => `${alias} < ReconciliationHistory.max_reverse_number`),
                    transaction_id: esHistory.transaction_id,
                    br_id: Not(esHistory.br_id)
                }
            })
            if (invoiceHistoryList.length === 0) throw new HttpException(`[es-reconcile-reverse] invoice histories with transaction_id: (${[esHistory.transaction_id]}) can not find!`, HttpStatus.BAD_REQUEST);

            await this.reverse(esHistory, invoiceHistoryList, queryRunner, creator);
        }
    }

    public async sendToEngine(data, queryRunner: QueryRunner, uuid: string, url: string): Promise<any> {
        this.logger.log(`[es_reconcile_reverse] Url: ${url}, Data: ${JSON.stringify(data)}`);
        const engineRes = await lastValueFrom(this.httpService.post(url, data)
            .pipe(
                tap(response => {
                    this.logger.log(JSON.stringify(response.data));
                    this.logService.sendLogDataToServer(url, JSON.stringify(data), response.status.toString(), response.data);
                }),
                map(async (response) => {
                    if (response.data.data.success) {
                        await queryRunner.manager.update(ReconciliationHistory, {transaction_id: uuid}, {
                            reverse_document_no: response.data.data.document_no,
                        });
                        return response;
                    }
                    throw new HttpException(`${JSON.stringify(response.data)}`, response.status);

                }),
                catchError(e => {
                    this.logger.log('[es_reconcile_reverse] Engine response error:', e.response.data);
                    this.logService.sendLogDataToServer(url, JSON.stringify(data), e.response.status, JSON.stringify(e.response.data));
                    throw new HttpException(`[es_reconcile_reverse] Engine response error: ${JSON.stringify(e.response.data)}`, e.response.status);
                })
            ));
        this.logger.log('[es_reconcile_reverse] Engine response successfully: ', JSON.stringify(engineRes.data));
        return engineRes;
    }

    async reverseEs(statement_id: number, creator: number): Promise<void> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            await this.reverseEsWithoutDBTransaction(statement_id, creator, queryRunner)
            await queryRunner.commitTransaction()
        } catch (e) {
            await queryRunner.rollbackTransaction()
            throw new HttpException(`[es-reconcile-reverse-es] reverse Es failed. Error: ${e}`, HttpStatus.INTERNAL_SERVER_ERROR)
        } finally {
            await queryRunner.release()
        }
    }

    /**
     * reverse invoice, cascade reverse es related this invoice
     * @param invoice_id
     * @param br_entity_type
     * @param creator
     */
    async reverseInvoice(invoice_id: number, br_entity_type: number, creator: number): Promise<string[]> {
        this.logger.log(`[es-reconcile-reverse-invoice] input params: invoice_id: ${invoice_id}, creator: ${creator}`);
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            // Find all history related to this invoice
            const historyRelatedInvoice = await queryRunner.manager.find(ReconciliationHistory, {
                where: {
                    br_id: invoice_id,
                    reverse_number: Raw((alias) => `${alias} < ReconciliationHistory.max_reverse_number`),
                    br_entity_type: br_entity_type
                }
            })

            if (!historyRelatedInvoice || historyRelatedInvoice.length === 0) {
                throw new HttpException(`[es-reconcile-reverse-invoice] Invoice (${[invoice_id]}) with br_entity_type (${br_entity_type}) can not find in history!`, HttpStatus.BAD_REQUEST);
            }
            const res = [];
            for (let invoiceHistory of historyRelatedInvoice) {
                res.push(await this.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(invoiceHistory.br_type.toString(), BkTypeEnum))
                    .reverseForInvoice(invoiceHistory, creator, queryRunner, this));
            }
            await queryRunner.commitTransaction()
            return res;
        } catch (e) {
            await queryRunner.rollbackTransaction()
            throw new HttpException(`[es-reconcile-reverse-invoice] reverse Es failed. Error: ${e}`, HttpStatus.INTERNAL_SERVER_ERROR)
        } finally {
            await queryRunner.release()
        }
    }

    private getBrFlag(entity, esNewBalance: Decimal, original: number) {
        if (esNewBalance.comparedTo(original) === 0) return BrStatusEnum.NOT_PAID;
        // add abs for negative value
        else if (esNewBalance.abs().comparedTo(new Decimal(original).abs()) < 0) return BrStatusEnum.PARTIAL_PAID;
        else throw new HttpException(`[es_reconcile_reverse] Balance (${esNewBalance}) of ${entity.constructor.name} (${entity.id}) can not be greater than the original value (${original}) after reverse!`, HttpStatus.INTERNAL_SERVER_ERROR)
    }

    /**
     * reverse by reconcile transaction id
     * @param params
     */
    async reverseTransaction(params: RequestReverseTransactionDto) {

        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        // find es history with entity
        const esHistoryList: ReconciliationHistory[] = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .leftJoinAndMapOne('rh.entity', Es, 'es', 'es.id = rh.br_id')
            // .where("rh.br_entity_type = 7 and reverse_number < max_reverse_number and rh.br_id = :br_id", {br_id: params.statement_id})
            .where("rh.target = 0 and reverse_number < max_reverse_number and rh.br_id = :br_id", {br_id: params.statement_id})
            .andWhere("rh.transaction_id = :transaction_id", {transaction_id: params.transaction_id})
            .getMany();

        if (!esHistoryList || esHistoryList.length === 0)
            throw new HttpException(`[es-reconcile-reverse] Es (${params.statement_id}) with transaction_id (${params.transaction_id}) can not find!`, HttpStatus.BAD_REQUEST);
        try {
            for (let esHistory of esHistoryList) {
                //查询transaction中的invoices
                const invoiceHistoryList = await queryRunner.manager.find(ReconciliationHistory, {
                    where: {
                        reverse_number: Raw((alias) => `${alias} < ReconciliationHistory.max_reverse_number`),
                        transaction_id: params.transaction_id,
                        br_id: Not(params.statement_id)
                    }
                })
                // if (invoiceHistoryList.length === 0) throw new HttpException(`[es-reconcile-reverse] invoice histories with transaction_id: (${[params.transaction_id]}) can not find!`, HttpStatus.BAD_REQUEST);
                //
                // this.bkTypeFactory.getBkTypeStrategy()

                //reverse
                await this.reverse(esHistory, invoiceHistoryList, queryRunner, params.creator);
            }
            await queryRunner.commitTransaction()
        } catch (e) {
            await queryRunner.rollbackTransaction()
            throw new HttpException(`[es-reconcile-reverse-es] reverse Es failed. Error: ${e}`, HttpStatus.INTERNAL_SERVER_ERROR)
        } finally {
            await queryRunner.release()
        }
    }

    /**
     * reverse by reconcile transaction id
     * @param params
     */
    async reverseTransactionIntegration(params: RequestReverseTransactionDto) {

        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        // find es history with entity
        const esHistoryList: ReconciliationHistory[] = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .leftJoinAndMapOne('rh.entity', Es, 'es', 'es.id = rh.br_id')
            // .where("rh.br_entity_type = 7 and reverse_number < max_reverse_number and rh.br_id = :br_id", {br_id: params.statement_id})
            .where("rh.target = 0 and reverse_number < max_reverse_number and rh.br_id = :br_id", {br_id: params.statement_id})
            .andWhere("rh.transaction_id = :transaction_id", {transaction_id: params.transaction_id})
            .getMany();

        if (!esHistoryList || esHistoryList.length === 0)
            throw new HttpException(`[es-reconcile-reverse] Es (${params.statement_id}) with transaction_id (${params.transaction_id}) can not find!`, HttpStatus.BAD_REQUEST);
        try {
            for (let esHistory of esHistoryList) {
                //查询transaction中的invoices
                const invoiceHistoryList = await queryRunner.manager.find(ReconciliationHistory, {
                    where: {
                        reverse_number: Raw((alias) => `${alias} < ReconciliationHistory.max_reverse_number`),
                        transaction_id: params.transaction_id,
                        br_id: Not(params.statement_id)
                    }
                })
                // if (invoiceHistoryList.length === 0) throw new HttpException(`[es-reconcile-reverse] invoice histories with transaction_id: (${[params.transaction_id]}) can not find!`, HttpStatus.BAD_REQUEST);
                //
                // this.bkTypeFactory.getBkTypeStrategy()

                //reverse
                await this.reverseIntegration(esHistory, invoiceHistoryList, queryRunner, params.creator);
            }
            await queryRunner.commitTransaction()
        } catch (e) {
            await queryRunner.rollbackTransaction()
            throw new HttpException(`[es-reconcile-reverse-es] reverse Es failed. Error: ${e}`, HttpStatus.INTERNAL_SERVER_ERROR)
        } finally {
            await queryRunner.release()
        }
    }

    async reverse(esHistory: ReconciliationHistory, invoiceHistoryList: ReconciliationHistory[], queryRunner: QueryRunner, creator: number) {

        //reverse invoices and calculate es new balance after reconciliation
        const strategy =
            this.bkTypeFactory.getBkTypeStrategy(esHistory.br_type.toString(),
                esHistory.br_type.toString() === '10' && invoiceHistoryList.length === 0 ? esHistory.document_no : null);

        const esNewBalance = await strategy.reverse(esHistory, invoiceHistoryList, queryRunner);
        //get es new brFlag after reconciliation
        const esNewBrFlag = new Decimal((esHistory.entity as Es).withdrawal).comparedTo(0) === 0 ?
            this.getBrFlag(esHistory.entity, esNewBalance, (esHistory.entity as Es).deposit) :
            this.getBrFlag(esHistory.entity, esNewBalance, (esHistory.entity as Es).withdrawal)

        //update es
        await queryRunner.manager.update(Es, esHistory.entity.id, {
            balance: esNewBalance.toNumber(),
            br_flag: esNewBrFlag
        });

        //send to engine
        await strategy.sendReverseEngine(esHistory, queryRunner, this, creator);
        //update es reverse history
        await queryRunner.manager.update(ReconciliationHistory, esHistory.id, {
                reverse_number: strategy.getReverseNumber(esHistory, invoiceHistoryList),
                after_reverse_balance: esNewBalance.toNumber(),
                reverse_time: new Date()});
    }

    async reverseIntegration(esHistory: ReconciliationHistory, invoiceHistoryList: ReconciliationHistory[], queryRunner: QueryRunner, creator: number) {

        //reverse invoices and calculate es new balance after reconciliation
        const strategy =
            this.bkTypeFactory.getBkTypeStrategy(esHistory.br_type.toString(),
                esHistory.br_type.toString() === '10' && invoiceHistoryList.length === 0 ? esHistory.document_no : null);

        const esNewBalance = await strategy.reverse(esHistory, invoiceHistoryList, queryRunner);
        //get es new brFlag after reconciliation
        const esNewBrFlag = new Decimal((esHistory.entity as Es).withdrawal).comparedTo(0) === 0 ?
            this.getBrFlag(esHistory.entity, esNewBalance, (esHistory.entity as Es).deposit) :
            this.getBrFlag(esHistory.entity, esNewBalance, (esHistory.entity as Es).withdrawal)

        //update es
        await queryRunner.manager.update(Es, esHistory.entity.id, {
            balance: esNewBalance.toNumber(),
            br_flag: esNewBrFlag
        });

        //send to engine
        // await strategy.sendReverseEngine(esHistory, queryRunner, this, creator);
        //update es reverse history
        await queryRunner.manager.update(ReconciliationHistory, esHistory.id, {
            reverse_number: strategy.getReverseNumber(esHistory, invoiceHistoryList),
            after_reverse_balance: esNewBalance.toNumber(),
            reverse_time: new Date()});
    }
}
