import {<PERSON><PERSON>our<PERSON>, EntityMana<PERSON>, Not, ObjectLiteral, Raw} from "typeorm";
import {Decimal} from "decimal.js";
import {Ar} from "../ar/entities/ar.entity";
import {Ap} from "../ap/entities/ap.entity";
import {MatchDto} from "./dto/match.dto";
import {Es} from "../es/entities/es.entity";
import {SelectEsReconcileDto} from "./dto/select_es_reconcile.dto";
import {HttpException, HttpStatus, Injectable, Logger} from "@nestjs/common";
import {ManualEsReconcileDto} from "./dto/manual_es_reconcile.dto";
import {BkEntityTypeEnum} from "../common/enum/bk.entity_type.enum";
import {ReconcileSubmitDto} from "./dto/reconcile_submit.dto";
import {ReconciliationHistory} from "./entities/reconciliation_history.entity";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {BrStatusEnum} from "../common/enum/br.status.enum";
import {format, parseISO} from "date-fns";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import {EsReconcileService} from "./es_reconcile.service";
import {HttpService} from "@nestjs/axios";
import {ConfigService} from "@nestjs/config";
import {EnumUtil} from "../common/enum/enum.util";
import {EsReconcileReverseService} from "./es_reconcile_reverse/es_reconcile_reverse.service";
import {RequestReverseTransactionDto} from "./dto/request_reverse_transaction.dto";

@Injectable()
export class BkTypeStrategyService {
    private readonly logger = new Logger(BkTypeStrategyService.name);

    constructor(protected dataSource: DataSource, protected httpService: HttpService, protected configService: ConfigService) {
    }

    public async find(params: SelectEsReconcileDto, es: Es, usedMap: Map<string, any>, threshold: number): Promise<MatchDto[]> {
        return null;
    }



    async findEsForBrMatch(params): Promise<Es[]> {
        let builder = this.dataSource.createQueryBuilder(Es, 'es')
            .where("es.br_flag IN (:...br_flag)", {br_flag: [0, 1]})
            .andWhere("es.company_code = :company_code", {company_code: params.company_code,})
            .andWhere("es.balance is not null AND es.balance <> :balance", {balance: 0});

        if (params.start_date && params.end_date) {
            await builder.andWhere("DATE_FORMAT(es.date,'%Y-%m-%d') between :start_date and :end_date", {
                start_date: params.start_date,
                end_date: params.end_date
            })
        }
        return await builder.orderBy({balance: "DESC"}).getMany();
    }

    protected async findArForBrMatch(params, isPositive, br_type): Promise<Ar[]> {
        let builder = this.dataSource.createQueryBuilder(Ar, 'ar')
            .where("ar.br_flag IN (:...br_flag)", {br_flag: [0, 1]})
            .andWhere("ar.company_code = :company_code", {company_code: params.company_code});

        if (!(params.ap_integration && params.ap_integration == 1)) {
            if (isPositive) {
                await builder.andWhere("ar.balance > :balance", {balance: 0});
            } else {
                await builder.andWhere("ar.balance < :balance", {balance: 0});
            }
        }

        if (br_type) {
            await builder.andWhere("ar.br_type = :br_type", {br_type: br_type});
        }

        if (params.bank_type && params.bank_type == 'Credit') {
            await builder.andWhere("ar.pay_method = :pay_method", {pay_method: '2'});
        }

        if (params.bank_type && params.bank_type == 'NoCredit') {
            await builder.andWhere("ar.pay_method IN (:...pay_method)", {pay_method: ['1', '3']});
        }

        if (params.start_date && params.end_date) {
            await builder.andWhere("DATE_FORMAT(ar.posting_date,'%Y-%m-%d') between :start_date and :end_date", {
                start_date: params.start_date,
                end_date: params.end_date
            })
        }
        // return await builder.orderBy({balance: "ASC"}).getMany();
        return await builder.orderBy({balance: "DESC"}).getMany();
    }

    public async findApForBrMatch(params, isPositive, br_type): Promise<Ap[]> {
        let builder = this.dataSource.createQueryBuilder(Ap, 'ap')
            // .where("ap.br_flag IN (:...br_flag)", {br_flag: [0, 1]})
            .where("ap.company_code = :company_code", {company_code: params.company_code})
        if (!(params.ap_integration && params.ap_integration == 1)) {
            if (isPositive) {
                await builder.andWhere("ap.balance > :balance", {balance: 0});
            } else {
                await builder.andWhere("ap.balance < :balance", {balance: 0});
            }
        }
        if (!br_type || br_type && br_type != 13) {
            await builder.andWhere("ap.br_flag IN (:...br_flag)", {br_flag: [0, 1]})
        }
        if (br_type) {
            await builder.andWhere("ap.br_type = :br_type", {br_type: br_type});
        }

        if (params.bank_type && params.bank_type == 'Credit') {
            await builder.andWhere("ap.pay_method = :pay_method", {pay_method: '2'});
        }

        if (params.bank_type && params.bank_type == 'NoCredit') {
            await builder.andWhere("ap.pay_method IN (:...pay_method)", {pay_method: ['1', '3']});
        }

        if (params.start_date && params.end_date) {
            await builder.andWhere("DATE_FORMAT(ap.posting_date,'%Y-%m-%d') between :start_date and :end_date", {
                start_date: params.start_date,
                end_date: params.end_date
            })
        }
        // return await builder.orderBy({balance: "ASC"}).getMany();
        return await builder.orderBy({balance: "DESC"}).getMany();
    }

    protected convertToMatchDto(entity: Ar | Ap): MatchDto {

        const matchDto = new MatchDto();
        matchDto.id = entity.id.toString();
        matchDto.invoice_id = entity.id.toString();
        matchDto.company_code = entity.company_code;
        if (entity instanceof Ar) {
            matchDto.company_name = entity.company_name;
            matchDto.br_type = entity.br_type;
            matchDto.bp_number = entity.bill_to_customer_id ? entity.bill_to_customer_id.toString() : '';
            matchDto.br_entity_type = BkEntityTypeEnum.AR;
            matchDto.payer_payee = entity.bill_to_company;
            matchDto.sap_document_id = entity.sap_document_id;
            matchDto.mx_isr = 0;
            matchDto.mx_iva = 0;
        } else {
            matchDto.company_name = entity.issuer_name;
            matchDto.br_type = entity.br_type;
            matchDto.bp_number = entity.issuer_id ? entity.issuer_id.toString() : '';
            matchDto.br_entity_type = BkEntityTypeEnum.AP;
            matchDto.payer_payee = entity.issuer_name;
            matchDto.sap_document_id = entity.sap_document_id;
            matchDto.mx_isr = entity.mx_isr;
            matchDto.mx_iva = entity.mx_iva;
        }
        matchDto.invoice_no = entity.invoice_no;
        matchDto.total_fee = entity.total_fee;
        matchDto.invoice_comments = entity.invoice_comments;
        matchDto.reference_no = entity.reference_no;
        matchDto.balance = entity.balance;
        matchDto.br_flag = entity.br_flag.toString();
        matchDto.invoice_currency = entity.invoice_currency;
        matchDto.invoice_create_date = entity.invoice_create_date.toString();
        matchDto.invoice_due_date = entity.invoice_due_date.toString();
        matchDto.posting_date = entity.posting_date.toString();
        matchDto.engine_document_id = entity.engine_document_id;
        return matchDto;
    }

    protected convertEsToMatchDto(entity: Es): MatchDto {
        const matchDto = new MatchDto();
        matchDto.id = entity.id.toString();
        matchDto.invoice_id = entity.id.toString();
        matchDto.company_code = entity.company_code;
        matchDto.bank_account = entity.bank_account;
        matchDto.total_fee = entity.withdrawal === 0 ? entity.deposit : entity.withdrawal;
        matchDto.deposit = entity.deposit;
        matchDto.withdrawal = entity.withdrawal;
        matchDto.currency_type = entity.currency.toString();
        matchDto.date = entity.date;
        matchDto.payer_payee = entity.payer_payee;
        matchDto.balance = entity.balance;
        matchDto.br_flag = entity.br_flag.toString();
        matchDto.reference_no = entity.reference
        matchDto.invoice_comments = entity.description;
        matchDto.br_type = entity.br_type.toString();
        matchDto.br_entity_type = BkEntityTypeEnum.ES;
        matchDto.reason_code = entity.reason_code;
        return matchDto;
    }

    private times: number = 0;

    public getReconciliationCombination(nums: MatchDto[], target, isPositive: boolean, thread: number, usedMap): MatchDto[] {
        // console.log("target:  " + target);
        // console.log("nums.length:  " + nums.length);
        // console.log("isPositive:  " + isPositive);
        // console.log("thread:  " + thread);
        // console.log("usedMap:  " + usedMap.size);
        if (nums.length <= 0) return [];
        const res = [];
        // this.backtrack(res, [], nums, target, new Decimal(0), 0, isPositive, 8, usedMap);
        // this.backtrack2(res, [], nums, new Decimal(target), 0, true, 8, usedMap);
        this.backtrack3(res, [], nums, new Decimal(target), 0, true, 10, usedMap);
        Logger.log(`recursionTime: ${target}--${this.times}`);
        this.times = 0;
        return res.length > 0 ? res[0] : res;
    }

    public getReconciliationCombinationIntegration(nums: MatchDto[], target, isPositive: boolean, thread: number, usedMap): MatchDto[] {
        // console.log("target:  " + target);
        // console.log("nums.length:  " + nums.length);
        // console.log("isPositive:  " + isPositive);
        // console.log("thread:  " + thread);
        // console.log("usedMap:  " + usedMap.size);
        if (nums.length <= 0) return [];

        // 按供应商分组
        const groupByCustomer = nums.reduce((groups, match) => {
            const key = match.bp_number;
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(match);
            return groups;
        }, {});

        let bestResult: MatchDto[] = [];
        let minDiff = Number.MAX_VALUE;

        // 对每个供应商组分别运行匹配算法
        for (const [customerId, customerNums] of Object.entries(groupByCustomer)) {
            const res = [];
            // 使用现有的 backtrack3 算法对每个供应商组进行匹配
            this.backtrack3(res, [], customerNums as MatchDto[], new Decimal(target), 0, true, thread, new Map());

            // 如果找到匹配结果
            if (res.length > 0) {
                // 计算当前匹配结果与目标金额的差额
                const currentSum = res[0].reduce((sum, match) => 
                    sum.add(new Decimal(match.balance)), new Decimal(0));
                const currentDiff = Math.abs(currentSum.sub(target).toNumber());

                // 如果找到更接近目标金额的组合，更新最佳结果
                if (currentDiff < minDiff) {
                    minDiff = currentDiff;
                    bestResult = res[0];

                    // 如果差额为0，说明找到完全匹配的组合，直接返回
                    if (currentDiff === 0) {
                        // 更新已使用的发票记录
                        bestResult.forEach(match => {
                            usedMap.set(`${match.id}_${match.br_type}`, true);
                        });
                        this.times = 0;
                        return bestResult;
                    }
                }
            }
        }

        // 如果找到最佳结果，更新已使用的发票记录
        if (bestResult.length > 0) {
            bestResult.forEach(match => {
                usedMap.set(`${match.id}_${match.br_type}`, true);
            });
        }
        this.times = 0;
        return bestResult;

        // // this.backtrack(res, [], nums, target, new Decimal(0), 0, isPositive, 8, usedMap);
        // // this.backtrack2(res, [], nums, new Decimal(target), 0, true, 8, usedMap);
        // this.backtrack3(res, [], nums, new Decimal(target), 0, true, 10, usedMap);
        // Logger.log(`recursionTime: ${target}--${this.times}`);
        // this.times = 0;
        // return res.length > 0 ? res[0] : res;
    }

    private backtrack(res: MatchDto[][], temp: MatchDto[], nums: MatchDto[], target, cusSum: Decimal, start: number, isPositive: boolean, thread: number, usedMap): void {
        // //filter combination greater than threshold
        // if (temp.length > thread) return;

        // //return when find one combination only
        // if (res.length > 0) return;

        // if (cusSum.comparedTo(target) === 0) {
        //     res.push(temp.map(e => e));
        //     temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
        //     return;
        // }

        // //filter combination greater than target
        // if (cusSum.comparedTo(target) > 0 || cusSum.comparedTo(0) < 0) return;

        // for (let i = start; i < nums.length; i++) {
        //     //Exclude data that has already been used
        //     if (usedMap.has(`${nums[i].id}_${nums[i].br_type}`)) continue;
        //     temp.push(nums[i]);
        //     this.backtrack(res, temp, nums, target, cusSum.add(isPositive ? new Decimal(nums[i].balance).abs() : new Decimal(nums[i].balance).mul(new Decimal(-1))), i + 1, isPositive, thread, usedMap);
        //     temp.pop();
        // }

        // this.times = this.times + 1;
        if (cusSum.comparedTo(target) === 0) {
            res.push(temp.map(e => e));
            temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
            return;
        }

        //filter combination greater than target
        if (cusSum.comparedTo(target) > 0 || cusSum.comparedTo(0) < 0) return;

        //filter combination greater than threshold
        if (temp.length >= thread) return;

        let min = cusSum.add(isPositive ? new Decimal(nums[nums.length - 1].balance).abs() : new Decimal(nums[nums.length - 1].balance).mul(new Decimal(-1)))
        if (min.comparedTo(target) > 0 || min.comparedTo(0) < 0) return;

        if (temp.length == thread - 2 && start < nums.length - 1) {
            //双指针

            let leftIndex = start;
            let rightIndex = nums.length - 1;

            while (usedMap.has(`${nums[leftIndex].id}_${nums[leftIndex].br_type}`) && leftIndex < rightIndex) {
                leftIndex++;
            }
            while (usedMap.has(`${nums[rightIndex].id}_${nums[rightIndex].br_type}`) && leftIndex < rightIndex) {
                rightIndex--;
            }
            if (leftIndex > rightIndex) return;
            let left = isPositive ? new Decimal(nums[leftIndex].balance).abs() : new Decimal(nums[leftIndex].balance).mul(new Decimal(-1));
            if (cusSum.add(left).comparedTo(target) === 0) {
                temp.push(nums[leftIndex]);
                res.push(temp.map(e => e));
                temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                return;
            }
            let right = isPositive ? new Decimal(nums[rightIndex].balance).abs() : new Decimal(nums[rightIndex].balance).mul(new Decimal(-1));
            if (cusSum.add(left).comparedTo(target) === 0) {
                temp.push(nums[rightIndex]);
                res.push(temp.map(e => e));
                temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                return;
            }
            while (leftIndex < rightIndex) {
                let currentSum = cusSum.add(left).add(right);
                let compare = currentSum.comparedTo(target);
                if (compare === 0) {
                    temp.push(nums[leftIndex]);
                    temp.push(nums[rightIndex]);
                    res.push(temp.map(e => e));
                    temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                    return;
                } else if (compare > 0) {
                    leftIndex++;
                    while (usedMap.has(`${nums[leftIndex].id}_${nums[leftIndex].br_type}`) && leftIndex < rightIndex) {
                        leftIndex++;
                    }
                    if (leftIndex > rightIndex) return;
                    left = isPositive ? new Decimal(nums[leftIndex].balance).abs() : new Decimal(nums[leftIndex].balance).mul(new Decimal(-1));
                    if (cusSum.add(left).comparedTo(target) === 0) {
                        temp.push(nums[leftIndex]);
                        res.push(temp.map(e => e));
                        temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                        return;
                    }
                } else {
                    rightIndex--;
                    while (usedMap.has(`${nums[rightIndex].id}_${nums[rightIndex].br_type}`) && leftIndex < rightIndex) {
                        rightIndex--;
                    }
                    if (leftIndex > rightIndex) return;
                    right = isPositive ? new Decimal(nums[rightIndex].balance).abs() : new Decimal(nums[rightIndex].balance).mul(new Decimal(-1));
                    if (cusSum.add(right).comparedTo(target) === 0) {
                        temp.push(nums[rightIndex]);
                        res.push(temp.map(e => e));
                        temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                        return;
                    }
                }
            }
        } else {
            for (let i = start; i < nums.length; i++) {
                //Exclude data that has already been used
                if (usedMap.has(`${nums[i].id}_${nums[i].br_type}`)) continue;
                temp.push(nums[i]);
                this.backtrack(res, temp, nums, target, cusSum.add(isPositive ? new Decimal(nums[i].balance).abs() : new Decimal(nums[i].balance).mul(new Decimal(-1))), i + 1, isPositive, thread, usedMap);
                temp.pop();
                //return when find one combination only
                if (res.length > 0) return;
            }
        }
    }

    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return Promise.resolve(undefined);
    }


    /**
     * reverse by history
     * @param esHistory
     * @param invoiceHistoryList
     * @param queryRunner
     */
    async reverse(esHistory: ReconciliationHistory, invoiceHistoryList: ReconciliationHistory[], queryRunner: QueryRunner) {
        //fix： 每次计算es的balance时，重新查询下es。解决es部分对账时，reverse票操作es不能完全恢复bug。
        const es = await queryRunner.manager.findOne(Es, {
            where: {
                id: esHistory.entity.id
            }
        })
        let esNewBalance = es.br_type == 1
            ? new Decimal(new Decimal(es.balance).add(es.charge_fee))
            : new Decimal(new Decimal(es.balance).sub(es.charge_fee));
        for (let invoiceHistory of invoiceHistoryList) {
            esNewBalance = esNewBalance.add(new Decimal(invoiceHistory.reconcile_amount).abs());
            await this.reverseInvoice(invoiceHistory, esHistory, queryRunner);
        }
        return esNewBalance;
    }

    /**
     * reverse invoice in history
     * @param invoiceHistory
     * @param esHistory
     * @param queryRunner
     */
    public async reverseInvoice(invoiceHistory: ReconciliationHistory, esHistory: ReconciliationHistory, queryRunner) {
        invoiceHistory.entity = invoiceHistory.br_entity_type.toString() === BkEntityTypeEnum.AP ?
            await this.findInvoice(Ap, 'ap', invoiceHistory.br_id, queryRunner) :
            await this.findInvoice(Ar, 'ar', invoiceHistory.br_id, queryRunner);

        const {invoiceNewBalance, invoiceNewBrFlag} = this.getInvoiceBalanceAndBrFlag(invoiceHistory);

        invoiceHistory.br_entity_type.toString() === BkEntityTypeEnum.AP ?
            await queryRunner.manager.update(Ap, invoiceHistory.br_id, {
                balance: invoiceNewBalance.toNumber(),
                br_flag: invoiceNewBrFlag
            }) :
            await queryRunner.manager.update(Ar, invoiceHistory.br_id, {
                balance: invoiceNewBalance.toNumber(),
                br_flag: invoiceNewBrFlag
            });

        //update history reverse number
        await queryRunner.manager.update(ReconciliationHistory, invoiceHistory.id, {reverse_number: invoiceHistory.reverse_number + 1, after_reverse_balance: invoiceNewBalance.toNumber()});
    }

    /**
     * reverse es in history
     * @param invoiceHistory
     * @param esHistory
     * @param queryRunner
     */
    async reverseEs(invoiceHistory: ReconciliationHistory, esHistory: ReconciliationHistory, queryRunner: QueryRunner): Promise<void> {

        invoiceHistory.entity = await this.findEsById(Es, 'es', invoiceHistory.br_id, queryRunner);
        const invoiceNewBalance = new Decimal(invoiceHistory.entity.balance).add(invoiceHistory.reconcile_amount);
        const invoiceNewBrFlag = this.getBrFlag(invoiceHistory.entity, invoiceNewBalance,
            new Decimal((invoiceHistory.entity as Es).deposit).comparedTo(0) === 0 ? (invoiceHistory.entity as Es).withdrawal : (invoiceHistory.entity as Es).deposit)

        await queryRunner.manager.update(Es, invoiceHistory.br_id, {
            balance: invoiceNewBalance.toNumber(),
            br_flag: invoiceNewBrFlag
        })

        //update history reverse number
        await queryRunner.manager.update(ReconciliationHistory, invoiceHistory.id, {reverse_number: invoiceHistory.reverse_number + 1, after_reverse_balance: invoiceNewBalance.toNumber()});
    }

    public findInvoice(entity, alias, id, queryRunner?: QueryRunner): Promise<Ar | Ap | ObjectLiteral> {
        if (!queryRunner) {
            return this.dataSource.createQueryBuilder(entity, alias)
                .where(`${alias}.id = :id`, {id: id})
                .getOne();
        }
        return queryRunner.manager.findOne(entity, {
            where: {
                id: id
            }
        })
    }

    public findEsById(entity, alias, id, queryRunner?: QueryRunner): Promise<Ar | Ap | Es | ObjectLiteral> {
        if (!queryRunner) {
            return this.dataSource.createQueryBuilder(entity, alias)
                .where(`${alias}.id = :id`, {id: id})
                .getOne();
        }
        return queryRunner.manager.findOne(entity, {
            where: {
                id: id
            }
        })
    }

    public getBrFlag(entity, esNewBalance: Decimal, original: number) {
        if (esNewBalance.comparedTo(original) === 0) return BrStatusEnum.NOT_PAID;
        else if (esNewBalance.abs().comparedTo(new Decimal(original).abs()) < 0) return BrStatusEnum.PARTIAL_PAID;
        else throw new HttpException(`[es_reconcile_reverse] Balance (${esNewBalance}) of ${entity.constructor.name} (${entity.id}) can not be greater than the original value (${original}) after reverse!`, HttpStatus.INTERNAL_SERVER_ERROR)
    }

    private getInvoiceBalanceAndBrFlag(invoiceHistory: ReconciliationHistory) {

        const invoiceNewBalance = new Decimal((invoiceHistory.entity as Ar | Ap).total_fee).comparedTo(0) > 0 ?
            new Decimal(invoiceHistory.entity.balance).add(invoiceHistory.reconcile_amount) :
            new Decimal(invoiceHistory.entity.balance).add(new Decimal(invoiceHistory.reconcile_amount).mul(-1))
        // const invoiceNewBalance = new Decimal(invoiceHistory.entity.balance).add(invoiceHistory.reconcile_amount);
        const invoiceNewBrFlag = this.getBrFlag(invoiceHistory.entity, invoiceNewBalance, (invoiceHistory.entity as Ap).total_fee);
        return {invoiceNewBalance, invoiceNewBrFlag};
    }


    /**
     * 创建发票： ap：cr,  ar: dr
     * 对账：    ap: cr, ar: dr
     * 负数：    pr/ap: dr, sr/ar: cr
     */

    public buildDataForEngine(submitDto: ReconcileSubmitDto, invoice_list_to_engine: any[], newEsBalance: Decimal, drCr) {

        return submitDto.charge_fee == 0.00 ? {
            "company_code": submitDto.company_code,
            "creator": submitDto.creator,
            "posting_date": format(parseISO(submitDto.posting_date), 'MM-dd-yyyy'),
            "post_check": true,
            "clearing_line_items": invoice_list_to_engine,
            "br_type": submitDto.br_type.toString(),
            "new_line_items": [
                {
                    "gl_account": submitDto.gl_account,
                    "amount_tc": new Decimal(submitDto.balance).sub(newEsBalance).toNumber(),
                    "dr_cr": drCr,
                    "neg_posting": false,
                }
            ]
        } : {
            "company_code": submitDto.company_code,
            "creator": submitDto.creator,
            "posting_date": format(parseISO(submitDto.posting_date), 'MM-dd-yyyy'),
            "post_check": true,
            "clearing_line_items": invoice_list_to_engine,
            "br_type": submitDto.br_type.toString(),
            "new_line_items": [
                {
                    "gl_account": submitDto.gl_account,
                    "amount_tc": submitDto.br_type == 1
                        ? new Decimal(submitDto.balance).sub(new Decimal(newEsBalance).sub(submitDto.charge_fee)).toNumber()
                        : new Decimal(submitDto.balance).sub(new Decimal(newEsBalance).add(submitDto.charge_fee)).toNumber(),
                    "dr_cr": drCr,
                    "neg_posting": false,
                },
                {
                    "gl_account": submitDto.charge_coa,
                    "amount_tc": submitDto.charge_fee,
                    "dr_cr": drCr,
                    "neg_posting": false,
                }
            ]
        }
    }

    calculateNewEsBalance(newEsBalance: Decimal, invoice: ReconcileSubmitDto.Invoice) {
        return newEsBalance.sub(new Decimal(invoice.reconcile_amount).abs());
    }

    async processInvoices(invoice: ReconcileSubmitDto.Invoice, transactionalEntityManager: EntityManager, service: EsReconcileService) {
        return BkEntityTypeEnum.AP === invoice.br_entity_type.toString() ?
            await service.updateInvoice(Ap, transactionalEntityManager, invoice) :
            await service.updateInvoice(Ar, transactionalEntityManager, invoice);
    }

    async buildInvoiceDataToEngin(es: Es, invoice_list_to_engine: any[], submitDto: ReconcileSubmitDto, invoice: ReconcileSubmitDto.Invoice) {
        invoice_list_to_engine.push({
            document_no: invoice.engine_document_id,
            line_no: "1",
            amount_tc: invoice.reconcile_amount
        });
    }

    async generateDataToEngine(submitDto: ReconcileSubmitDto, invoice_list_to_engine, newEsBalance: Decimal, service: EsReconcileService, drCr) {
        const enginePostInvoiceUrl = service.configService.get('ENGINE_BASE_URL') + '/post-payment';
        const data = this.buildDataForEngine(submitDto, invoice_list_to_engine, newEsBalance, drCr);
        return {url: enginePostInvoiceUrl, data}
    }

    async submit(submitDto: ReconcileSubmitDto, uuid, service: EsReconcileService) {
        const es = await service.findEs(submitDto);
        const historyList: ReconciliationHistory[] = [];
        const invoice_list_to_engine = [];

        await this.dataSource.transaction(async (transactionalEntityManager) => {
            // use to keep es balance after reconcile
            let newEsBalance: Decimal = submitDto.br_type == 1
                ? new Decimal(es.balance).sub(submitDto.charge_fee)
                : new Decimal(es.balance).add(submitDto.charge_fee);
            const bkTypeStrategy = service.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum));
            for (let invoice of submitDto.invoice_list) {
                newEsBalance = await bkTypeStrategy.calculateNewEsBalance(newEsBalance, invoice);
                const invoiceHis = await bkTypeStrategy.processInvoices(invoice, transactionalEntityManager, service);
                await bkTypeStrategy.buildInvoiceDataToEngin(es, invoice_list_to_engine, submitDto, invoice);
                // The invoice in each reconciliation can only be reversed once
                historyList.push(service.buildHistory(submitDto, invoiceHis.newBalance, invoiceHis.brFlag, uuid, invoice.reconcile_amount, invoice, invoice.invoice_id, invoice.br_entity_type, 1, 1))
            }
            //update es
            const esBrFlag = await service.updateEs(newEsBalance, es, transactionalEntityManager);
            // The number es can be reversed is equal to the number of invoices in this reconciliation
            historyList.push(service.buildHistory(submitDto, newEsBalance, esBrFlag, uuid, submitDto.br_type == 1 ? new Decimal(new Decimal(es.balance).sub(submitDto.charge_fee)).sub(newEsBalance).toNumber() : new Decimal(new Decimal(es.balance).add(submitDto.charge_fee)).sub(newEsBalance).toNumber(), es, es.id, submitDto.br_entity_type, submitDto.invoice_list.length, 0))
            // send to engine
            const drCr: string = await bkTypeStrategy.getDrCr(submitDto,es);
            const dataToEngine = await bkTypeStrategy.generateDataToEngine(submitDto, invoice_list_to_engine, newEsBalance, service, drCr);
            const response = await service.sendToEngine(dataToEngine.data, transactionalEntityManager, uuid, dataToEngine.url);

            //set document_no
            historyList.forEach(item => {
                item.document_no = response.document_no;
            })
            await service.saveHistories(historyList, transactionalEntityManager);

        });
        return historyList;
    }

    async submitIntegration(submitDto: ReconcileSubmitDto, uuid, service: EsReconcileService) {
        const es = await service.findEs(submitDto);
        const historyList: ReconciliationHistory[] = [];
        const invoice_list_to_engine = [];

        await this.dataSource.transaction(async (transactionalEntityManager) => {
            // use to keep es balance after reconcile
            let newEsBalance: Decimal = submitDto.br_type == 1
                ? new Decimal(es.balance).sub(submitDto.charge_fee)
                : new Decimal(es.balance).add(submitDto.charge_fee);
            const bkTypeStrategy = service.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum));
            for (let invoice of submitDto.invoice_list) {
                newEsBalance = await bkTypeStrategy.calculateNewEsBalance(newEsBalance, invoice);
                const invoiceHis = await bkTypeStrategy.processInvoices(invoice, transactionalEntityManager, service);
                await bkTypeStrategy.buildInvoiceDataToEngin(es, invoice_list_to_engine, submitDto, invoice);
                // The invoice in each reconciliation can only be reversed once
                historyList.push(service.buildHistory(submitDto, invoiceHis.newBalance, invoiceHis.brFlag, uuid, invoice.reconcile_amount, invoice, invoice.invoice_id, invoice.br_entity_type, 1, 1))
            }
            //update es
            const esBrFlag = await service.updateEs(newEsBalance, es, transactionalEntityManager);
            // The number es can be reversed is equal to the number of invoices in this reconciliation
            historyList.push(service.buildHistory(submitDto, newEsBalance, esBrFlag, uuid, submitDto.br_type == 1 ? new Decimal(new Decimal(es.balance).sub(submitDto.charge_fee)).sub(newEsBalance).toNumber() : new Decimal(new Decimal(es.balance).add(submitDto.charge_fee)).sub(newEsBalance).toNumber(), es, es.id, submitDto.br_entity_type, submitDto.invoice_list.length, 0))
            // send to engine
            // const drCr: string = await bkTypeStrategy.getDrCr(submitDto,es);
            // const dataToEngine = await bkTypeStrategy.generateDataToEngine(submitDto, invoice_list_to_engine, newEsBalance, service, drCr);
            // const response = await service.sendToEngine(dataToEngine.data, transactionalEntityManager, uuid, dataToEngine.url);

            //set document_no
            // historyList.forEach(item => {
            //     item.document_no = response.document_no;
            // })
            await service.saveHistories(historyList, transactionalEntityManager);

        });
        return historyList;
    }

    public async getDrCr(submitDto, es) {
        return null;
    }

    public async reverseForInvoice(invoiceHistory, creator: number, queryRunner: QueryRunner, service: EsReconcileReverseService) {
        //find es by transaction_id
        const esHistory = await this.dataSource.createQueryBuilder(ReconciliationHistory, 'rh')
            .leftJoinAndMapOne('rh.entity', Es, 'es', 'es.id = rh.br_id')
            .where("rh.target = 0 and reverse_number < max_reverse_number and rh.transaction_id = :transaction_id", {transaction_id: invoiceHistory.transaction_id})
            .getOne();
        //reverse es
        await service.reverseEsWithoutDBTransaction(esHistory.br_id, creator, queryRunner);
    }

    /**
     * reverse es and invoice reconcile
     * @param esHistory
     * @param queryRunner
     * @param service
     * @param creator
     */
    public async sendReverseEngine(esHistory: ReconciliationHistory, queryRunner: QueryRunner, service: EsReconcileReverseService, creator: number) {
        const enginePostInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/reverse-journal-entry';
        await service.sendToEngine(
            {
                creator: creator,
                document_no: esHistory.document_no,
                posting_date: (esHistory.entity as Es).date
            },
            queryRunner, esHistory.transaction_id, enginePostInvoiceUrl);
    }

    /**
     * reverse es and es reconcile
     * @param esHistory
     * @param queryRunner
     * @param service
     * @param creator
     */
    public async sendESReverseEngine(esHistory: ReconciliationHistory, queryRunner: QueryRunner, service: EsReconcileReverseService, creator: number) {
        const enginePostInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/reverse-journal-entry';
        await service.sendToEngine({
                creator: creator,
                document_no: esHistory.document_no,
                posting_date: (esHistory.entity as Es).date
            },
            queryRunner, esHistory.transaction_id, enginePostInvoiceUrl);
    }


    /**
     * 自动匹配算法v3
     */
    public backtrack3(res: MatchDto[][], temp: MatchDto[], nums: MatchDto[], target: Decimal, start: number, isPositive: boolean, thread: number, usedMap): void {

        this.times = this.times + 1;
        // 限制匹配次数的50w
        if(this.times > 100000) return;
        // console.log(this.times)
        if (target.comparedTo(0) === 0) {
            res.push(temp.map(e => e));
            temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
            return;
        }

        if (temp.length == thread - 2 && start < nums.length - 1) {
            let l = start;
            let r = nums.length - 1;

            while (usedMap.has(`${nums[l].id}_${nums[l].br_type}`) && l < r) {
                l++;
            }
            while (usedMap.has(`${nums[r].id}_${nums[r].br_type}`) && l < r) {
                r--;
            }

            //nums[l] 是要找的值
            if (target.comparedTo(nums[l].balance) == 0) {
                temp.push(nums[l]);
                res.push(temp.map(e => e));
                temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                temp.pop();
            }
            //nums[r] 是要找的值
            if (target.comparedTo(nums[r].balance) == 0) {
                temp.push(nums[r]);
                res.push(temp.map(e => e));
                temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                temp.pop();
            }

            if (l >= r) {
                return;
            }
            //nums[l] + nums[l+1] 是nums中最大两个数之和小于target，说明没有满足的， skip
            if (new Decimal(nums[l].balance).add(new Decimal(nums[l + 1].balance)).comparedTo(target) < 0) {
                return;
            }
            //nums[r] + nums[r-1] 是nums中最小两个数之和大于target，说明没有满足的， skip
            if (new Decimal(nums[r].balance).add(new Decimal(nums[r - 1].balance)).comparedTo(target) > 0) {
                return;
            }

            while (r > l) {
                let sum: Decimal = new Decimal(nums[l].balance).add(new Decimal(nums[r].balance));
                if (sum.comparedTo(target) > 0) {
                    l++;
                    while (usedMap.has(`${nums[l].id}_${nums[l].br_type}`) && l < r) {
                        l++;
                    }

                } else if (sum.comparedTo(target) < 0) {
                    r--;
                    while (usedMap.has(`${nums[r].id}_${nums[r].br_type}`) && l < r) {
                        r--;
                    }

                } else {
                    temp.push(nums[l]);
                    temp.push(nums[r]);
                    res.push(temp.map(e => e));
                    temp.forEach(e => usedMap.set(`${e.id}_${e.br_type}`, e));
                    temp.pop();
                    temp.pop();

                    // if nums[l] 和 nums[l+1]相等，nums[l]不满足说明nums[l+1]也不满足
                    while (l < r && (new Decimal(nums[l].balance).comparedTo(new Decimal(nums[l + 1].balance)) === 0 ||
                        usedMap.has(`${nums[l + 1].id}_${nums[l + 1].br_type}`))) {
                        l++;
                    }
                    while (l < r && (new Decimal(nums[r].balance).comparedTo(new Decimal(nums[r - 1].balance)) === 0 ||
                        usedMap.has(`${nums[r - 1].id}_${nums[r - 1].br_type}`))) {
                        r--;
                    }
                    l++;
                    r--;
                }
            }
            return;
        }

        for (let i = start; temp.length < thread && i < nums.length; i++) {
            // nums[i] 大于 target，不需要递归，skip
            if (target.comparedTo(nums[i].balance) < 0) continue;

            //Exclude data that has already been used
            if (usedMap.has(`${nums[i].id}_${nums[i].br_type}`)) continue;

            //nums[i]等于nums[i - 1]，如果nums[i - 1] 不满足， nums[i] 也不满足，
            if (i != start && (new Decimal(nums[i].balance).comparedTo(nums[i - 1].balance) === 0 && !usedMap.has(`${nums[i - 1].id}_${nums[i - 1].br_type}`)))
                continue;

            //已nums[i]为基础，预先计算temp中最大组合(thread个数的组合)的和， 如果小于target，说明此组合不满足要求，不进递归
            let sum = new Decimal(nums[i].balance);
            for (let j = 0, m = i + 1; m < nums.length && j < thread - temp.length - 1; j++, m++) {
                sum = sum.add(nums[m].balance);
            }
            if (sum.comparedTo(target) < 0) return;

            temp.push(nums[i]);
            this.backtrack3(res, temp, nums, target.sub(nums[i].balance), i + 1, true, thread, usedMap);
            temp.pop();
            //return when find one combination only
            if (res.length > 0) return;
        }
    }

    getReverseNumber(esHistory, invoiceHistoryList) {
        return esHistory.reverse_number + invoiceHistoryList.length;
    }
}
