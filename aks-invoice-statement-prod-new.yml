apiVersion: apps/v1
kind: Deployment
metadata:
  name: invoice-statement
  # namespace: ns-eo
spec:
  replicas: 2
  selector:
    matchLabels:
      app: invoice-statement
  template:
    metadata:
      labels:
        app: invoice-statement
    spec:
      containers:
        - name: invoice-statement
          image: eorepo.azurecr.io/eo-invoice-statement-service:latest
          imagePullPolicy: Always
          env:
            - name: NODE_ENV
              value: production
          ports:
            - containerPort: 3030
          volumeMounts:
            - name: files
              mountPath: /home/<USER>/work/upload
              subPath: bkp/upload
      volumes:
        - name: files
          persistentVolumeClaim:
            claimName: eo-files-prod-pvc

