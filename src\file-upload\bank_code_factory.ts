import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {DataSource} from "typeorm";
import {BankCodeStrategyService} from "./bank_code_strategy.service";
import { BMOCode } from "./bank_code/bmo.code";
import {BASEINETCode} from "./bank_code/baseinet.code";
import { CIBCCode } from "./bank_code/cibc.code";
import { RBCCode } from "./bank_code/rbc.code";
import {DEJARDINCode} from "./bank_code/dejardin.code";
import { TDCode } from "./bank_code/td.code";
import { JPCBCode } from "./bank_code/jpcb.code";
import {FBCode} from "./bank_code/fb.code";
import {NBCCode} from "./bank_code/nbc.code";
import {ASPIRECode} from "./bank_code/aspire.code";
import {NOVACode} from "./bank_code/nova.code";
import {AMEXCode} from "./bank_code/amex.code";
import {SEBCode} from "./bank_code/seb.code";
import {EWBCode} from "./bank_code/ewb.code";
import {CITICode} from "./bank_code/citi.code";
import {BOCCode} from "./bank_code/boc.code";
import {CMBCode} from "./bank_code/cmb.code";
import {ICBCCode} from "./bank_code/icbc.code";
import {BONJCode} from "./bank_code/bonj.code";
import {SANTANDERCode} from "./bank_code/santander.code";
import { MONEXCode } from "./bank_code/monex.code";
import { BBVACode } from "./bank_code/bbva.code";

/**
 * Bank code factory class
 */
export class BankCodeFactory {

    dataSource: DataSource;
    httpService: HttpService;
    configService: ConfigService;
    bankCodeStrategyMap: Map<string, BankCodeStrategyService>;

    constructor(dataSource, httpService, configService) {
        this.dataSource = dataSource;
        this.httpService = httpService;
        this.configService = configService;
        this.bankCodeStrategyMap = this.getStrategyMap();
    }

    getStrategyMap() {
        // @ts-ignore
        return new Map([
            ['BMO', new BMOCode(this.dataSource, this.httpService, this.configService)],
            ['CIBC', new CIBCCode(this.dataSource, this.httpService, this.configService)],
            ['RBC', new RBCCode(this.dataSource, this.httpService, this.configService)],
            ['Dejardin', new DEJARDINCode(this.dataSource, this.httpService, this.configService)],
            ['TD', new TDCode(this.dataSource, this.httpService, this.configService)],
            ['JPCB', new JPCBCode(this.dataSource, this.httpService, this.configService)],
            ['FB', new FBCode(this.dataSource, this.httpService, this.configService)],
            ['NBC', new NBCCode(this.dataSource, this.httpService, this.configService)],
            ['ASPIRE', new ASPIRECode(this.dataSource, this.httpService, this.configService)],
            ['NOVA', new NOVACode(this.dataSource, this.httpService, this.configService)],
            ['AMEX', new AMEXCode(this.dataSource, this.httpService, this.configService)],
            ['SEB', new SEBCode(this.dataSource, this.httpService, this.configService)],
            ['EWB', new EWBCode(this.dataSource, this.httpService, this.configService)],
            ['CITI', new CITICode(this.dataSource, this.httpService, this.configService)],
            ['BOC', new BOCCode(this.dataSource, this.httpService, this.configService)],
            ['CMB', new CMBCode(this.dataSource, this.httpService, this.configService)],
            ['ICBC', new ICBCCode(this.dataSource, this.httpService, this.configService)],
            ['BONJ', new BONJCode(this.dataSource, this.httpService, this.configService)],
            ['SANTANDER', new SANTANDERCode(this.dataSource, this.httpService, this.configService)],
            ['BASEINET', new BASEINETCode(this.dataSource, this.httpService, this.configService)],
            ['MONEX', new MONEXCode(this.dataSource, this.httpService, this.configService)],
            ['BBVA', new BBVACode(this.dataSource, this.httpService, this.configService)]
        ]);
    }

    getBankCodeStrategyByCode(code) {
        return this.bankCodeStrategyMap.get(code);
    }

}