import { Test, TestingModule } from '@nestjs/testing';
import { EsConnectController } from './es_connect.controller';
import { EsConnectService } from './es_connect.service';

describe('EsConnectController', () => {
  let controller: EsConnectController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EsConnectController],
      providers: [EsConnectService],
    }).compile();

    controller = module.get<EsConnectController>(EsConnectController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
