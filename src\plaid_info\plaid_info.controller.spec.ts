import { Test, TestingModule } from '@nestjs/testing';
import { PlaidInfoController } from './plaid_info.controller';
import { PlaidInfoService } from './plaid_info.service';

describe('PlaidInfoController', () => {
  let controller: PlaidInfoController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlaidInfoController],
      providers: [PlaidInfoService],
    }).compile();

    controller = module.get<PlaidInfoController>(PlaidInfoController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
