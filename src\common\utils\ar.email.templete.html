<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Salary Report</title>
    <style type="text/css">
        body{
            max-width: 1024px;
            margin: auto;
        }
        table{
            width: 100%;
            margin: auto;
            text-align: center;
        }
        .greenBlock{
            background-color:#CCE6CF;
            width:100%;
            /*font-weight: bold;*/
            padding: 15pt 0;
            margin: auto;
        }
        .myFont{
            font-family: Arial, sans-serif, serif, EmojiFont;
        }
        .dueDate{
            color: rgb(57, 58, 61);
            font-size: 12pt;
            font-weight: bold;
            margin: 0;
        }
        .totalFee{
            color: rgb(57, 58, 61);
            font-size: 36pt;
            padding: 12pt 0;
            margin: 0
        }
        .downloadBlock{
            width:150pt;
            padding: 9pt 0;
            background-color:#393A3D;
        }
        .download{
            color:white;
            text-decoration:none;
            font-size: 12pt;
            font-weight: bold;
        }
        .wrap{
            display: block;
            margin: auto;
        }


    </style>
</head>
<body>
<table border="0" cellspace="0" cellpadding="0" style="width: 100%">
    <tr>
        <td></td>
        <td style="width:576pt;padding:0;">
            <table style="padding-top: 10pt;">
                <tr>
                    <td class="myFont" style="font-size: 10pt; padding: 5pt 0">
                        <span style="color: rgb(107, 108, 114);">INVOICE</span>
                        <span>${ar.getInvoiceNo()}</span>
                    </td>
                </tr>
                <tr>
                    <td style="padding: 6pt 0">
                        <img src="${ar.getCompanyLogo()}">
                    </td>
                </tr>
            </table>
            <table class="greenBlock">
                <tr>
                    <td>
                        <p class="dueDate myFont">DUE ${ar.getInvoiceDueDate()}</p>
                        <p class="totalFee myFont">$ ${ar.getTotalFee()}</p>
                        <table>
                            <tr>
                                <td></td>
                                <td class="downloadBlock">
                                    <a class="download" href="${ar.getInvoiceUrl()}" download="invoice_${ar.getInvoiceNo()}">
                                        Print or save
                                    </a>
                                </td>
                                <td></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <div style="text-align: left">
                <p>Dear ${ar.getBillToCompany()},</p>
                <p>Thank you for choosing us, please find attached your invoice document for the payment.</p>
                <p>We recommend you download and keep this invoice document from the email. A notification for your payment accomplishment is preferred and appreciated.</p>
                <p>Please ignore if the payment has been already made.</p>
                <p>thank you for your business.</p>
                <p>${ar.getCompanyName()}</p>
                <p>${ar.getCompanyAddress()}</p>
                <p>${ar.getCompanyEmail()}</p>
            </div>
            <table style="border-top: 1px solid #cccccc;">
                <tr style="text-align: left">
                    <td>
                        <p class="myFont" style="color: rgb(107, 108, 114);">If you receive an email that seems fraudulent, please check with the business owner before paying.</p>
                        <p>&copy; Powered by INOSSEM portfolio, Essential Operation, All rights reserved.</p>
                    </td>
                </tr>
            </table>
        </td>
        <td></td>
    </tr>
</table>
</body>
</html>