import {Page} from "../../common/utils/index.util";
import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class SelectReversesList extends Page {

    @ApiProperty({description: 'company code', example: '3000'})
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({type: String, required: false, description: 'es/ar/ap id', example: '161'})
    br_id: string;

    @ApiProperty({type: String, required: false, description: 'start date', example: '2022-11-01'})
    start_date: string;

    @ApiProperty({type: String, required: false, description: 'end date', example: '2022-11-30'})
    end_date: string;

    @ApiProperty({type: String, required: false, description: 'min total fee', example: '100'})
    minTotalFee: string;

    @ApiProperty({type: String, required: false, description: 'max total fee', example: '150'})
    maxTotalFee: string;

    @ApiProperty({type: String, required: false,description: 'Bank account', example: '001-36721-1983873'})
    bank_account: string;

    @ApiProperty({type: String, required: false,description: 'payer payee', example: '1983873'})
    payer_payee: string;

    @ApiProperty({type: String, required: false, description: 'order by', example: 'sort[date]=desc'})
    sort: string;

    @ApiProperty({type: String, required: false,description: 'filter for description', example: 'SHOPIFY MSP/DIV'})
    desc: string;

}