import {BadRequestException, HttpException, HttpStatus, Injectable, <PERSON><PERSON>, <PERSON><PERSON>, Patch} from '@nestjs/common';
import { CreateArDto } from './dto/create-ar.dto';
import {InjectRepository} from "@nestjs/typeorm";
import {Between, DataSource, In, IsNull, Not, Raw, Repository} from "typeorm";
import {Ar} from "./entities/ar.entity";
import {ArItem} from "./entities/ar.item.entity";
import {Paginated} from "../common/utils/index.util";
import {generateLocalDateTimeByFormat} from "../common/utils/function.util";
import {BaseService} from "../common/service/base.service";
import {EngineInvoiceDto} from "../ap/dto/engine-invoice.dto";
import {EngineInvoiceItemDto} from "../ap/dto/engine-invoice-item.dto";
import {BrStatusEnum} from "../common/enum/br.status.enum";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {ConfigService} from "@nestjs/config";
import {HttpService} from "@nestjs/axios";
import {EsReconcileReverseService} from "../es_reconcile/es_reconcile_reverse/es_reconcile_reverse.service";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {BkEntityTypeEnum} from "../common/enum/bk.entity_type.enum";
import {FileUpload} from "../file-upload/entities/file-upload.entity";
import {FileStatusEnum} from "../common/enum/file.status.enum";
import {PayMethodEnum} from "../common/enum/pay.method.enum";
import {ReconcileSubmitDto} from "../es_reconcile/dto/reconcile_submit.dto";
import {EsReconcileService} from "../es_reconcile/es_reconcile.service";
import {Decimal} from "decimal.js";
import {LogService} from "../log/log.service";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";
import {CreateCheckDto} from "../check/dto/create-check.dto";
import {ReportInvoiceDto} from "../report/dto/report_invoice.dto";

@Injectable()
export class ArService extends BaseService<Ar> {

  private readonly logger = new Logger(ArService.name);

  constructor(
      @InjectRepository(Ar) private readonly arRepository: Repository<Ar>,
      @InjectRepository(ArItem) private readonly arItemRepository: Repository<ArItem>,
      @InjectRepository(CoaTopInit) private readonly coaTopInitRepository: Repository<CoaTopInit>,
      @InjectRepository(ReconciliationHistory) private readonly historyRepository: Repository<ReconciliationHistory>,
      private configService: ConfigService,
      private httpService: HttpService,
      private dataSource: DataSource,
      private esReconcileService: EsReconcileService,
      private esReconcileReverseService: EsReconcileReverseService,
      private logService: LogService,
  ) {
      super(arRepository, 'ar');
  }

  async createAr(createArDto: CreateArDto) {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      let ar: Ar = new Ar();
      ar = Object.assign(ar, createArDto);
      // set invoice no
      ar.invoice_no = generateLocalDateTimeByFormat(ar.company_code, new Date());
      // set balance
      ar.balance = ar.total_fee;
      // set br_flag
      ar.br_flag = BrStatusEnum.NOT_PAID;


      // send ar data to ar-invoice-pdf-service
      // const arInvoicePdfServiceUrl = this.configService.get('PDF_EMAIL_SERVICE_BASE_URL') + '/pdf-lib/ar';
      // if (ar.sap_document_id != null && ar.sap_document_id.length > 0) {
      //     // integration mode pdf generation
      //     const arIntegrationInvoicePdfServiceUrl = this.configService.get('AR_INTEGRATION_INVOICE_PDF_SERVICE_URL');
      //     this.logger.log('[AR][PDF_SERVICE][url]', arIntegrationInvoicePdfServiceUrl);
      //     this.logger.log('[AR][PDF_SERVICE][input]', JSON.stringify(ar));
      //     const pdfServiceRes = await lastValueFrom(this.httpService.post(arIntegrationInvoicePdfServiceUrl, ar)
      //         .pipe(
      //             tap(response => {
      //                 this.logger.log(JSON.stringify(response.data));
      //                 this.logService.sendLogDataToServer(arIntegrationInvoicePdfServiceUrl, JSON.stringify(ar), response.status.toString(), response.data);
      //             }),
      //             map((response) => {
      //                 this.logger.log('[AR][PDF_SERVICE][output]', response.data);
      //                 return response.data;
      //             }),
      //             catchError(e => {
      //                 this.logger.log('[AR][PDF_SERVICE][Error]', e.response.data);
      //                 this.logService.sendLogDataToServer(arIntegrationInvoicePdfServiceUrl, JSON.stringify(ar), e.response.status.toString(), e.response.data);
      //                 // throw new HttpException(`[Engine Error] ${JSON.stringify(e.response.data)}`, e.response.status);
      //                 return `[AR][PDF_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
      //             })
      //         ));
      //     this.logger.log('res', JSON.stringify(pdfServiceRes));
      //     if (pdfServiceRes.code == 200) {
      //         ar.invoice_file_name = pdfServiceRes.name;
      //         ar.invoice_file_b64 = pdfServiceRes.content;
      //     }else {
      //         throw new HttpException('[AR][PDF_SERVICE][Error]' + pdfServiceRes.message, pdfServiceRes.code);
      //     }
      // } else {
      if (ar.sap_document_id == null || ar.sap_document_id.length == 0) {
          // common pdf generation
          const arInvoicePdfServiceUrl = this.configService.get('AR_INVOICE_PDF_SERVICE_URL');
          this.logger.log('[AR][PDF_SERVICE][url]', arInvoicePdfServiceUrl);
          this.logger.log('[AR][PDF_SERVICE][input]', JSON.stringify(ar));
          const pdfServiceRes = await lastValueFrom(this.httpService.post(arInvoicePdfServiceUrl, ar)
              .pipe(
                  tap(response => {
                      this.logger.log(JSON.stringify(response.data));
                      this.logService.sendLogDataToServer(arInvoicePdfServiceUrl, JSON.stringify(ar), response.status.toString(), response.data);
                  }),
                  map((response) => {
                      this.logger.log('[AR][PDF_SERVICE][output]', response.data);
                      return response.data;
                  }),
                  catchError(e => {
                      this.logger.log('[AR][PDF_SERVICE][Error]', e.response.data);
                      this.logService.sendLogDataToServer(arInvoicePdfServiceUrl, JSON.stringify(ar), e.response.status.toString(), e.response.data);
                      // throw new HttpException(`[Engine Error] ${JSON.stringify(e.response.data)}`, e.response.status);
                      return `[AR][PDF_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
                  })
              ));
          this.logger.log('res', JSON.stringify(pdfServiceRes));
          if (pdfServiceRes.code == 200) {
              ar.invoice_url = pdfServiceRes.message;
          }else {
              throw new HttpException('[AR][PDF_SERVICE][Error]' + pdfServiceRes.message, pdfServiceRes.code);
          }
      }


      try {
        // check items length
        if (createArDto.items.length <= 0) throw new HttpException('items cannot be empty.', 400);
        // add ar data
        await queryRunner.manager.save<Ar>(ar);
        let lineItemTotalAmount = new Decimal(0);
        for (let i in createArDto.items) {
          let arItem: ArItem = new ArItem();
          arItem = Object.assign(arItem, createArDto.items[i]);
          arItem.ar = ar;
          lineItemTotalAmount = Decimal.add(lineItemTotalAmount, arItem.total);;
          // add ar item data
          await queryRunner.manager.save<ArItem>(arItem);
        }
        if (!lineItemTotalAmount.equals(ar.net_amount))
          throw new HttpException(`The net amount (${ar.net_amount}) does not match the line item total (${lineItemTotalAmount}).`, 400);

        // update status to upload-file table with file url
        if (ar.file_id)
            await queryRunner.manager.update<FileUpload>(FileUpload, ar.file_id, {invoice_created_status: FileStatusEnum.GENERATED});
        if (!ar.pay_method || ar.pay_method.length == 0)
            throw new HttpException('Pay method is required.', 400);
        if (ar.pay_method == PayMethodEnum.CASH_PAID && ar.br_type != BkTypeEnum.CP)
            throw new HttpException('Br_type is not correct.', 400);

        // send ar data to engine
        // if (ar.company_code != '8888' && ar.company_code != '65QW') {
        if (ar.sap_document_id == null || ar.sap_document_id.length == 0) {
            const enginePostInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/post-invoice';
            const engineInput = this.engineInvoiceBuilder(ar);
            this.logger.log('[AR][engine][url]', enginePostInvoiceUrl);
            this.logger.log('[AR][engine][input]', JSON.stringify(engineInput));
            const engineRes = await lastValueFrom(this.httpService.post(enginePostInvoiceUrl, engineInput)
                .pipe(
                    tap(response => {
                        this.logger.log(JSON.stringify(response.data));
                        this.logService.sendLogDataToServer(enginePostInvoiceUrl, JSON.stringify(engineInput), response.status.toString(), response.data);
                    }),
                    map((response) => {
                        return response.data;
                    }),
                    catchError(e => {
                        this.logger.log(e.response.data);
                        e.response.message = '[Engine Error] ' + JSON.stringify(e.response.data.errors);
                        this.logService.sendLogDataToServer(enginePostInvoiceUrl, JSON.stringify(engineInput), e.response.status.toString(), e.response.data);
                        throw new HttpException(e.response, e.response.status);
                    })
                ));
            this.logger.log('res', JSON.stringify(engineRes));
            if (!engineRes || !engineRes.data || !engineRes.data.success || engineRes.data.success == false)
                throw new HttpException(engineRes, 500);
            if (createArDto.pay_method != PayMethodEnum.CASH_PAID && (!engineRes.data.data.document_no || engineRes.data.data.document_no.length == 0))
                throw new HttpException('[Engine Error] No invoice no return from engine.', 500);
            if (createArDto.pay_method == PayMethodEnum.CASH_PAID && (!engineRes.data.invoice_no || engineRes.data.invoice_no.length == 0))
                throw new HttpException('[Engine Error] No invoice no return because of cash paid.', 500);
            if (createArDto.pay_method == PayMethodEnum.CASH_PAID && (!engineRes.data.payment_no || engineRes.data.invoice_no.length == 0))
                throw new HttpException('[Engine Error] No payment no return because of cash paid.', 500);

            ar.engine_document_id = createArDto.pay_method != PayMethodEnum.CASH_PAID ? engineRes.data.data.document_no : engineRes.data.invoice_no;
            ar.cash_engine_payment_no = createArDto.pay_method == PayMethodEnum.CASH_PAID ? engineRes.data.payment_no : null;

            await queryRunner.manager.update<Ar>(Ar, ar.id, {engine_document_id: ar.engine_document_id, cash_engine_payment_no: ar.cash_engine_payment_no, send_engine_status: 1});
        }
        // commit transaction
        await queryRunner.commitTransaction();

        // generate PDF file with special url if it is created by SAP
        if (ar.sap_document_id != null && ar.sap_document_id.length > 0) {
            // integration mode pdf generation
            const arIntegrationInvoicePdfServiceUrl = this.configService.get('AR_INTEGRATION_INVOICE_PDF_SERVICE_URL') + '/' + ar.id;
            this.logger.log('[AR][PDF_SERVICE][url]', arIntegrationInvoicePdfServiceUrl);
            this.logger.log('[AR][PDF_SERVICE][input]', JSON.stringify(ar));
            const pdfServiceRes = await lastValueFrom(this.httpService.post(arIntegrationInvoicePdfServiceUrl, ar)
                .pipe(
                    tap(response => {
                        this.logger.log(JSON.stringify(response.data));
                    }),
                    map((response) => {
                        this.logger.log('[AR][PDF_SERVICE][output]', response.data);
                        return response.data;
                    }),
                    catchError(e => {
                        this.logger.log('[AR][PDF_SERVICE][Error]', e.response.data);
                        this.logService.sendLogDataToServer(arIntegrationInvoicePdfServiceUrl, JSON.stringify(ar), e.response.status.toString(), e.response.data);
                        // throw new HttpException(`[Engine Error] ${JSON.stringify(e.response.data)}`, e.response.status);
                        return `[AR][PDF_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
                    })
                ));
        }
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw new HttpException(error, error.status);
      } finally {
        await queryRunner.release();
      }

      // if invoice is cash paid, then call BR API to reconcile
      if (createArDto.pay_method == PayMethodEnum.CASH_PAID)
          await this.cashPaidReconcile(ar);

      return ar;
  }

    async cashPaidReconcile(dto: Ar) {
        try {
            await this.esReconcileService.reconcileSubmit(this.cashPaidReconcileBuilder(dto));
        }catch (error) {
            throw new HttpException(error, 500);
        }
    }

    private cashPaidReconcileBuilder(dto: Ar): ReconcileSubmitDto {
        const cashPaidReconParam = {
            company_code: dto.company_code,
            statement_id: -1,
            br_type: parseInt(dto.br_type),
            br_entity_type: parseInt(BkTypeEnum.RS),
            posting_date: dto.posting_date.toString(),
            currency: parseInt(dto.invoice_currency),
            bank_account: '',
            balance: dto.total_fee,
            gl_account: '',
            creator: dto.creator,
            invoice_list: [{
                invoice_id: dto.id,
                balance: dto.total_fee,
                bp_number: dto.bill_to_customer_id,
                br_type: parseInt(dto.br_type),
                br_entity_type: parseInt(BkTypeEnum.RS),
                reconcile_amount: dto.total_fee,
                engine_document_id: dto.cash_engine_payment_no,
                gl_account: ''
            }]
        };
        const res = Object.assign(new ReconcileSubmitDto(), cashPaidReconParam);
        this.logger.log('cashPaidReconcileBuilder', JSON.stringify(res));
        return res;
    }

  async topCoA(params: any) {
    if (params.bill_to_customer_id.length === 0) throw new HttpException('bill_to_customer_id is required', HttpStatus.BAD_REQUEST);
    if (params.company_code.length === 0) throw new HttpException('company_code is required', HttpStatus.BAD_REQUEST);
    const bill_to_customer_id = params.bill_to_customer_id;
    const company_code = params.company_code;

    const qb = this.arItemRepository.createQueryBuilder('arItem');
    qb.select('arItem.debit_coa_id, arItem.debit_coa_code, COUNT(arItem.id) as count')
        .innerJoin(Ar, 'ar', 'ar.id = arItem.invoice_id AND ar.deleted_time IS NULL')
        .where('ar.company_code = :companyCode AND ar.bill_to_customer_id = :clientId', { companyCode: company_code, clientId: bill_to_customer_id })
        .andWhere('arItem.deleted_time IS NULL AND arItem.debit_coa_id IS NOT NULL')
        .groupBy('arItem.debit_coa_id, arItem.debit_coa_code')
        .orderBy('count', 'DESC');
    const result = await qb.getRawMany<{ debit_coa_id: number; debit_coa_code: string; count: string }>();
    // if
    if (result?.length > 0) {
        return result.map(result => ({ ...result, count: parseInt(result.count) }));
    }else {
        return await this.coaTopInitRepository
            .createQueryBuilder('coaTopInit')
            .select('coaTopInit.coa_id', 'debit_coa_id')
            .addSelect('coaTopInit.coa_code', 'debit_coa_code')
            .addSelect('coaTopInit.count', 'count')
            .where('coaTopInit.company_code = :companyCode', { companyCode: company_code })
            .andWhere('coaTopInit.contact_id = :clientId', { clientId: bill_to_customer_id })
            .andWhere('coaTopInit.contact_type = :contactType', { contactType: 'S' })
            .orderBy('coaTopInit.count', 'DESC')
            .getRawMany<{ debit_coa_id: number; debit_coa_code: string; count: number }>();
    }
}

  async findAllAr(params: any) {
      if (!params.company_code || params.company_code.length == 0) {
          throw new HttpException('company code is required', HttpStatus.BAD_REQUEST);
      }

      try {
          const {where, order} = this.parseParams.parseQuery(params);
          if (params.page_index) {
              const {page_index, page_size = 10} = params;
              const [result, total] = await this.arRepository.findAndCount({
                  where: where,
                  relations: {items: true},
                  order: order,
                  take: page_size,
                  skip: (page_index - 1) * page_size
              });
              return new Paginated(total, page_size, page_index, result)
          }
          return await this.arRepository.find({
              where: where,
              relations: {items: true},
              order: order
          });
      } catch (e) {
          throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
      }
  }

  async findOne(id: number) {
      return await this.arRepository.findOne({where: {id: id}, relations: {items: true} });
  }

  async reverse(id: number, param: any) {
      const invoiceDetail = await this.findOne(id);
      if (!invoiceDetail) {
          throw new BadRequestException('this invoice is not exist.')
      }
      if (invoiceDetail.br_flag == BrStatusEnum.REVERSED) {
          throw new BadRequestException('this invoice has been reversed.')
      }
      if (!param || !param.creator || param.creator.length == 0) {
          throw new BadRequestException('the param creator is required.')
      }
      if (!param.creator_name || param.creator_name.length == 0) {
          throw new BadRequestException('the param creator_name is required.')
      }

      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();
      try {
          // reverse ES and Invoice
          let reverseId = '';
          if (invoiceDetail.br_flag == BrStatusEnum.PARTIAL_PAID || invoiceDetail.br_flag == BrStatusEnum.PAID) {
              // call es reconcile reverse api
              const esBrRes = await this.esReconcileReverseService.reverseInvoice(id, parseInt(BkEntityTypeEnum.AR), param.creator);
              if (invoiceDetail.pay_method == PayMethodEnum.CASH_PAID) reverseId = esBrRes[0];
          }
          // send reverse data to engine
          const engineReverseInvoiceUrl = this.configService.get('ENGINE_BASE_URL') + '/reverse-journal-entry';
          const engineReverseInput = {document_no: invoiceDetail.engine_document_id, posting_date: param.posting_date || invoiceDetail.posting_date, creator: param.creator};
          this.logger.log('[AR][engine][url]', engineReverseInvoiceUrl);
          this.logger.log('[AR][engine][input]', JSON.stringify(engineReverseInput));
          const reverseEngineRes = await lastValueFrom(this.httpService.post(engineReverseInvoiceUrl, engineReverseInput)
              .pipe(
                  tap(response => {
                      this.logger.log(JSON.stringify(response.data));
                      this.logService.sendLogDataToServer(engineReverseInvoiceUrl, JSON.stringify(engineReverseInput), response.status.toString(), response.data);
                  }),
                  map((response) => {
                      return response.data;
                  }),
                  catchError(e => {
                      this.logger.log(e.response.data);
                      e.response.message = '[Engine Error] ' + JSON.stringify(e.response.data.errors);
                      this.logService.sendLogDataToServer(engineReverseInvoiceUrl, JSON.stringify(engineReverseInput), e.response.status.toString(), e.response.data);
                      throw new HttpException(e.response, e.response.status);
                  })
              ));
          if (!reverseEngineRes || !reverseEngineRes.data || !reverseEngineRes.data.success || reverseEngineRes.data.success == false) {
              throw new HttpException(reverseEngineRes, 500);
          }
          if (reverseEngineRes.data.success == true) {
              reverseId += reverseId && reverseId != '' ? ',' + reverseEngineRes.data.document_no : reverseEngineRes.data.document_no;
          }
          // update invoice br_flag to reversed status
          await queryRunner.manager.update<Ar>(Ar, id, {br_flag: BrStatusEnum.REVERSED, creator: param.creator, creator_name: param.creator_name, engine_reverse_document_id: reverseId});
          if (invoiceDetail.file_id) {
              // update file invoice_created_status to not created status
              await queryRunner.manager.update<FileUpload>(FileUpload, invoiceDetail.file_id, {invoice_created_status: FileStatusEnum.NOT_GENERATED});
          }
          // commit transaction
          return await queryRunner.commitTransaction();
      } catch (error) {
          await queryRunner.rollbackTransaction();
          throw new HttpException(error, error.status);
      } finally {
          await queryRunner.release();
      }
  }

  async convertToCash(id: number, param: any) {
      const invoiceDetail: Ar = await this.findOne(id);
      if (invoiceDetail.pay_method == PayMethodEnum.CASH_PAID)
          throw new BadRequestException('The invoice is cash paid and cannot convert to cash paid.');
      if (invoiceDetail.br_flag != BrStatusEnum.NOT_PAID)
          throw new BadRequestException('The invoice has wrong br_flag and cannot convert to cash paid.');

      try {
          // step1: reverse
          await this.reverse(id, param);
          // step2: create cash paid invoice
          let createArDto = Object.assign(new CreateArDto(), invoiceDetail);
          createArDto.pay_method = PayMethodEnum.CASH_PAID;
          createArDto.br_type = BkTypeEnum.CP;
          await this.createAr(createArDto);
      }catch (error) {
          throw new HttpException(error, error.status);
      }
  }

  async emailToCustomer(id: number) {
      let arDto: Ar = await this.findOne(id);
      if (!arDto) {
          throw new BadRequestException('Invoice ID is not exist');
      }

      // fill pdf url
      arDto.invoice_url = this.configService.get('UPLOAD_FILE_PATH') + arDto.invoice_url;
      const emailInput = {
          from: null,
          to: arDto.bill_to_email,
          subject: 'Invoice ' + arDto.invoice_no + ' from ' + arDto.company_name,
          data: arDto,
          template: 'ArEmail'
      };

      const arEmailServiceUrl = this.configService.get('PDF_EMAIL_SERVICE_BASE_URL') + '/email';
      this.logger.log('[AR][EMAIL_SERVICE][url]', arEmailServiceUrl);
      this.logger.log('[AR][EMAIL_SERVICE][input]', JSON.stringify(emailInput));
      const emailServiceRes = await lastValueFrom(this.httpService.post(arEmailServiceUrl, emailInput)
          .pipe(
              tap(response => {
                  this.logger.log(JSON.stringify(response.data));
                  this.logService.sendLogDataToServer(arEmailServiceUrl, JSON.stringify(emailInput), response.status.toString(), response.data);
              }),
              map((response) => {
                  this.logger.log('[AR][EMAIL_SERVICE][output]', response.data);
                  return response.data;
              }),
              catchError(e => {
                  this.logger.log('[AR][EMAIL_SERVICE][output]', e.response.data);
                  this.logService.sendLogDataToServer(arEmailServiceUrl, JSON.stringify(emailInput), e.response.status.toString(), e.response.data);
                  // throw new HttpException(`[Engine Error] ${JSON.stringify(e.response.data)}`, e.response.status);
                  return `[AR][EMAIL_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
              })
          ));
      this.logger.log('res', JSON.stringify(emailServiceRes));

      if (emailServiceRes.code == 200) {
          return emailServiceRes.message;
      }else {
          throw new HttpException(emailServiceRes.message, emailServiceRes.code);
      }
  }

  private engineInvoiceBuilder(dto: Ar): EngineInvoiceDto {
      let engineDto = new EngineInvoiceDto();
      engineDto.company_code = dto.company_code;
      engineDto.cashPaidFlag = dto.pay_method == PayMethodEnum.CASH_PAID ? 1 : 0
      engineDto.bp = dto.bill_to_customer_id;
      engineDto.customer_vendor = 'C';
      engineDto.posting_date = dto.posting_date.toString();
      engineDto.currency = dto.invoice_currency;
      engineDto.header_text = dto.invoice_comments;
      engineDto.invoice_no = dto.invoice_no;
      engineDto.due_date = dto.invoice_due_date.toString();
      // engineDto.HST = Math.abs(dto.gst);
      // engineDto.QST = Math.abs(dto.qst);
      // engineDto.GST = Math.abs(dto.gst);
      // engineDto.PST = Math.abs(dto.pst);
      engineDto.tax = dto.tax_content;
      engineDto.creator = dto.creator;
      let itemList: EngineInvoiceItemDto[] = [];
      for (let i in dto.items) {
          let dtoItem: ArItem = new ArItem();
          dtoItem = Object.assign(dtoItem, dto.items[i]);
          let engineDtoItem: EngineInvoiceItemDto = new EngineInvoiceItemDto();
          engineDtoItem.dr_cr = dtoItem.dr_cr;
          engineDtoItem.gl_account = dtoItem.debit_coa_code;
          engineDtoItem.business_partner = dtoItem.business_partner;
          engineDtoItem.amount_tc = Math.abs(dtoItem.total);
          // engineDtoItem.neg_posting = dtoItem.total > 0 ? false : true;
          itemList.push(engineDtoItem);
      }
      engineDto.line_items = itemList;
      return engineDto;
  }

  async generatePdfBatch(param: any) {
      // valid param
      if (!param.company_code)
          throw new BadRequestException('company_code is required.');
      if (!param.company_logo)
          throw new BadRequestException('company_logo is required.');
      const invoiceIds: number[] = param.invoice_ids;
      let arList: Ar[] = [];
      try {
          if (invoiceIds && invoiceIds.length > 0) {
              for (let invoiceId of invoiceIds) {
                  const invoiceDetail = await this.arRepository.findOne({where: {id: invoiceId, company_code: param.company_code}, relations: {items: true}});
                  if (invoiceDetail) {
                      arList.push(invoiceDetail);
                  }
              }
          }else {
              arList = await this.arRepository.find({
                  where: {
                      company_code: param.company_code,
                      br_flag: In([0, 1, 2]),
                      invoice_url: Raw((alias) => `${alias} IS NOT NULL AND ${alias} != ''`),
                  },
                  relations: {items: true}
              });
          }

          for (let item of arList) {
              item.company_logo = param.company_logo;
          }

          // send ar data to eo-pdf-email-service
          const arInvoicePdfServiceUrl = this.configService.get('AR_INVOICE_PDF_SERVICE_URL');
          this.logger.log('[AR][PDF_SERVICE][url]', arInvoicePdfServiceUrl);
          // this.logger.log('[AR][PDF_SERVICE][input]', JSON.stringify(arList));
          let errRes = [];
          this.logger.log('[AR][PDF_SERVICE][Generate Batch] Total: ', arList.length);
          for (let arDetail of arList) {
              const pdfServiceRes = await lastValueFrom(this.httpService.post(arInvoicePdfServiceUrl, arDetail)
                  .pipe(
                      tap(response => {
                          // this.logger.log(JSON.stringify(response.data));
                          // this.logService.sendLogDataToServer(arInvoicePdfServiceUrl, `${arList.length}`, response.status.toString(), response.data);
                      }),
                      map((response) => {
                          this.logger.log('[AR][PDF_SERVICE][output]', JSON.stringify(response.data));
                          return response.data;
                      }),
                      catchError(e => {
                          this.logger.log('[AR][PDF_SERVICE][Error]', JSON.stringify(e.response.data));
                          // this.logService.sendLogDataToServer(arInvoicePdfServiceUrl, `${arList.length}`, e.response.status.toString(), e.response.data);
                          // throw new HttpException(`[Engine Error] ${JSON.stringify(e.response.data)}`, e.response.status);
                          return `[AR][PDF_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
                      })
                  ));
              // this.logger.log('res', JSON.stringify(pdfServiceRes));
              if (pdfServiceRes.code != 200) {
                  errRes.push(pdfServiceRes);
              }
          }
          return errRes;
      }catch (e) {
          throw new HttpException('get report ar invoice list error', 500);
      }

  }

    async updateBillToEmail(param: any) {
        return await this.dataSource.createQueryBuilder()
            .update(Ar)
            .set({bill_to_email: param.bill_to_email})
            .where("bill_to_customer_id = :bill_to_customer_id", {bill_to_customer_id: param.bill_to_customer_id})
            .execute()

    }
}
