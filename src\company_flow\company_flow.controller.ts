import {Controller, Get, Post, Body, Patch, Param, Delete, Query} from '@nestjs/common';
import { CompanyFlowService } from './company_flow.service';
import { CreateCompanyFlowDto } from './dto/create-company_flow.dto';
import { UpdateCompanyFlowDto } from './dto/update-company_flow.dto';
import {ApiTags} from "@nestjs/swagger";
import {BaseController} from "../common/controller/base.controller";
import {Ap} from "../ap/entities/ap.entity";
import {CompanyFlow} from "./entities/company_flow.entity";
import {CreateApDto} from "../ap/dto/create-ap.dto";

@ApiTags('Company-flow')
@Controller('company-flow')
export class CompanyFlowController extends BaseController<CompanyFlow> {
  constructor(private readonly companyFlowService: CompanyFlowService) {
    super(companyFlowService);
  }

  @Post()
  createCompanyFlow(@Body() createCompanyFlowDto: CreateCompanyFlowDto) {
    return this.companyFlowService.createCompanyFlow(createCompanyFlowDto);
  }

  @Post('batch')
  createCompanyFlowBatch(@Body() param: any) {
    return this.companyFlowService.createCompanyFlowBatch(param);
  }

  @Get()
  findAllCompanyFlow(@Query() queryData: any) {
    return this.companyFlowService.findAllCompanyFlow(queryData);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.companyFlowService.findOne(+id);
  }

  @Patch(':id')
  updateCompanyFlow(@Param('id') id: string, @Body() updateCompanyFlowDto: UpdateCompanyFlowDto) {
    return this.companyFlowService.updateCompanyFlow(+id, updateCompanyFlowDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.companyFlowService.remove(+id);
  }
}
