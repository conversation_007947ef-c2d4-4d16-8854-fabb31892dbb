apiVersion: apps/v1
kind: Deployment
metadata:
  name: invoice-statement
  # namespace: ns-eo
spec:
  replicas: 1
  selector:
    matchLabels:
      app: invoice-statement
  template:
    metadata:
      labels:
        app: invoice-statement
      annotations:
        gke-gcsfuse/volumes: "true" # required
    spec:
      serviceAccountName: nt-eo-gcp-cs
      containers:
        - name: invoice-statement
          image: northamerica-northeast2-docker.pkg.dev/nt-eo-gb/nt-eo-repo/eo-invoice-statement-service:test-latest
          imagePullPolicy: Always
          env:
            - name: NODE_ENV
              value: development
          ports:
            - containerPort: 3000
              name: port
          volumeMounts:
            - name: files
              mountPath: /home/<USER>/work/upload
              # subPath: bkp/upload
          livenessProbe:
            httpGet:
              path: /api/v1/ap?page_index=1&page_size=10&company_code=3002
              port: port
            failureThreshold: 1
            timeoutSeconds: 90
            periodSeconds: 600
          startupProbe:
            httpGet:
              path: /api/v1/ap?page_index=1&page_size=10&company_code=3002
              port: port
            initialDelaySeconds: 10
            failureThreshold: 15
            periodSeconds: 10
      volumes:
      - name: files
        persistentVolumeClaim:
          claimName: eo-files-test-pvc-bkp
