import {forwardRef, Module} from '@nestjs/common';
import { ApService } from './ap.service';
import { ApController } from './ap.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {ApItem} from "./entities/ap.item.entity";
import {Ap} from "./entities/ap.entity";
import {HttpModule} from "@nestjs/axios";
import {ConfigModule} from "@nestjs/config";
import {FileUploadModule} from "../file-upload/file-upload.module";
import {EsReconcileReverseService} from "../es_reconcile/es_reconcile_reverse/es_reconcile_reverse.service";
import {EsReconcileModule} from "../es_reconcile/es_reconcile.module";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {LogModule} from "../log/log.module";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";


@Module({
  imports: [TypeOrmModule.forFeature([Ap, ApItem, CoaTopInit, ReconciliationHistory]), LogModule, HttpModule, ConfigModule, FileUploadModule, forwardRef(() => EsReconcileModule) ],
  controllers: [ApController],
  providers: [ApService],
  exports:[ApService]
})
export class ApModule {}
