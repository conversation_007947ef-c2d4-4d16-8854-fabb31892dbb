import {Column, Entity} from "typeorm";
import {BaseEntity} from "../../common/entity/base.entity";

@Entity("plaid_fetch_history")
export class PlaidFetchHistory extends BaseEntity{

    // @Column({name: 'company_id', type: 'int', comment: 'company id'})
    // company_id: number;

    @Column({name: 'company_code', type: 'varchar', comment: 'company code'})
    company_code: string;

    @Column({name: 'br_type', nullable: true, type: 'tinyint', comment: '1 AR ; 2 AP; 3 ES'})
    br_type: string;

    @Column({name: 'name', nullable: true, type: 'varchar', length: 50, comment: 'fetch name'})
    name: string;

    @Column({name: 'fetch_message', nullable: true, type:'text',  comment: 'fetch message'})
    fetch_message: string;

    @Column({name: 'bank_account', nullable: true, type: 'varchar', length: 50,  comment: 'Bank account'})
    bank_account: string;

    @Column({name: 'currency', nullable: true, type: 'varchar', length: 10,  comment: 'currency: 1 - CNY; 2 - CAD; 3 - USD'})
    currency: string;
}
