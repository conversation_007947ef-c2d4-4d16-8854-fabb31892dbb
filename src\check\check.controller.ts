import {Body, Controller, Post} from '@nestjs/common';
import { CheckService } from './check.service';
import {BaseController} from "../common/controller/base.controller";
import {Check} from "./entities/check.entity";
import {ApiTags} from "@nestjs/swagger";
import {CreateCheckDto} from "./dto/create-check.dto";

@ApiTags('Check')
@Controller('check')
export class CheckController extends BaseController<Check> {
  constructor(private readonly checkService: CheckService) {
    super(checkService);
  }

  @Post('batch')
  createCheckBatch(@Body() createArDtoList: CreateCheckDto[]) {
    return this.checkService.createCheckBatch(createArDtoList);
  }
}
