import {Body, Controller, Post} from '@nestjs/common';
import {EsConnectService} from './es_connect.service';
import {PlaidAccessTokenDto} from "../plaid_info/dto/plaid_access_token.dto";
import {ApiOperation, ApiTags} from "@nestjs/swagger";
import {SendPlaidAuthEmailDto} from "./dto/send_plaid_auth_email.dto";
import {FetchPlaidTransactionDto} from "./dto/fetch_plaid_transaction.dto";

@ApiTags('Es-Connect')
@Controller('es-connect')
export class EsConnectController {
    constructor(private readonly esConnectService: EsConnectService) {
    }

    @Post("/plaid-access-token")
    @ApiOperation({summary: 'Save plaid access token'})
    plaidAccessToken(@Body() plaidAccessTokenDto: PlaidAccessTokenDto) {
        return this.esConnectService.plaidAccessToken(plaidAccessTokenDto);
    }

    @Post("/sync")
    @ApiOperation({summary: 'Fetch transaction data from bank'})
    sync(@Body() fetchPlaidTransactionDto: FetchPlaidTransactionDto) {
        return this.esConnectService.sync(fetchPlaidTransactionDto);
    }

    @Post("/email")
    @ApiOperation({summary: 'Send verification email to customer'})
    sendBkBankEmail(@Body() sendPlaidAuthEmailDto: SendPlaidAuthEmailDto) {
        return this.esConnectService.sendBkBankEmail(sendPlaidAuthEmailDto);
    }
}
