import {Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne} from 'typeorm';
import { BaseEntity } from '../../common/entity/base.entity';
import {Gl} from "./gl.entity";

@Entity()
export class GlItem extends BaseEntity {

    @Column({name: 'item_no', comment: 'item no'})
    item_no: number;

    @Column({name: 'gl_id', comment: 'gl id'})
    gl_id: number;

    @Column({name: 'description', comment: 'description'})
    description: string;

    @Column({name: 'debit', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'debit'})
    debit: number;

    @Column({name: 'credit', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'credit'})
    credit: number;

    @Column({name: 'coa_id', comment: 'CoA id'})
    coa_id: number;

    @Column({name: 'coa_code', comment: 'coa code'})
    coa_code: string;

    @ManyToOne(() => Gl, gl => gl.items)
    @JoinColumn({name: 'gl_id'})
    gl: Gl;

}

