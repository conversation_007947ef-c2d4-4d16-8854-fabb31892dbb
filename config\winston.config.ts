import * as winston from 'winston';
import 'winston-daily-rotate-file';

const customLevels = {
    emerg: 0,
    alert: 1,
    crit: 2,
    error: 3,
    warning: 4,
    notice: 5,
    info: 6,
    debug: 7,
};

const transport = [
    new winston.transports.DailyRotateFile({
        filename: './logs/application-%DATE%.log',
        // dirname: './logs',
        datePattern: 'YYYY-MM-DD',
        zippedArchive: false,
        maxSize: '5m',
        maxFiles: 5,
    }),
    new winston.transports.Console()
];

const moduleName = 'Nest';

const alignColorsAndTime = winston.format.combine(
    winston.format.timestamp({
        format: 'YY-MM-DD HH:MM:SS',
    }),
    winston.format.printf(
        ({level, message, context, timestamp, stack, trace}) => {
            if(level === 'ERROR'){
                return ` [${moduleName}] [${timestamp}]     ${level.toUpperCase()} [${stack[0]}] : [ ${message.trim()} ]`;
            }
            return ` [${moduleName}] [${timestamp}]     ${level.toUpperCase()} [${context}] : [ ${message.trim()} ]`;
        },
    ),
);

const winstonConfig = {
    format: winston.format.combine(
        winston.format((info) => ({...info, level: info.level.toUpperCase()}))(),
        winston.format.align(),
        winston.format.errors({stack: true}),
        winston.format.prettyPrint(),
        winston.format.simple(),
        winston.format.splat(),
        alignColorsAndTime,
    ),
    transports: transport,
    levels: customLevels,
    level: 'info',
};

export default winstonConfig;
