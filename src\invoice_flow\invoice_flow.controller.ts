import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
  HttpException
} from '@nestjs/common';
import { InvoiceFlowService } from './invoice_flow.service';
import { CreateInvoiceFlowDto } from './dto/create-invoice_flow.dto';
import { UpdateInvoiceFlowDto } from './dto/update-invoice_flow.dto';
import {BaseController} from "../common/controller/base.controller";
import {CompanyFlow} from "../company_flow/entities/company_flow.entity";
import {ApprovalFlow} from "./entities/invoice_flow.entity";
import {ApiTags} from "@nestjs/swagger";
import {CreateCompanyFlowDto} from "../company_flow/dto/create-company_flow.dto";

@ApiTags('approval-flow')
@Controller('approval-flow')
export class InvoiceFlowController extends BaseController<ApprovalFlow> {
  constructor(private readonly invoiceFlowService: InvoiceFlowService) {
    super(invoiceFlowService);
  }

  @Post()
  createInvoiceFlow(@Body() createInvoiceFlowDto: CreateInvoiceFlowDto) {
    return this.invoiceFlowService.createInvoiceFlow(createInvoiceFlowDto);
  }

  @Post('batch')
  createInvoiceFlowBatch(@Body() param: any) {
    return this.invoiceFlowService.createApprovalFlowBatch(param);
  }

  @Get()
  findAllInvoiceFlow(@Query() queryData: any) {
    return this.invoiceFlowService.findAllInvoiceFlow(queryData);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.invoiceFlowService.findOne(+id);
  }

  @Patch(':id')
  updateInvoiceFlow(@Param('id') id: string, @Body() updateInvoiceFlowDto: UpdateInvoiceFlowDto) {
    return this.invoiceFlowService.update(+id, updateInvoiceFlowDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.invoiceFlowService.remove(+id);
  }

}
