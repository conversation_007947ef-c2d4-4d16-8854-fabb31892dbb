import {Between, In, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Like, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Not, Raw} from "typeorm";

export class ParseParams {

    /**
     * {{url_l}}/es?page=1&page_size=10&company_id=34
     * &company_id[$in]=[1]
     * &sort[date]=desc&sort[bank_account]=asc
     * &create_time[$bw]=[2022-11-01,2022-11-30]
     * &update_time[$gt]=2022-11-01
     * &company_id=[$like]=3000
     * &company_id=[$not]=3000
     * &withDeleted=true      //included soft deleted data
     */
    operators = {
        $raw: (value) => Raw(value),
        $isNull: () => IsNull(),
        $notNull: () => Not(IsNull()),
        $not: Not,
        $gte: MoreThanOrEqual,
        $gt: MoreThan,
        $lte: LessThanOrEqual,
        $lt: LessThan,
        $in: (value) => {
            return In(this.parseArray(value))
        },
        $bw: (value) => {
            const arr = this.parseArray(value)
            return Between(arr[0], arr[1]);
        },
        $like: (value) => Like(`%${value}%`)
    }

    filter = [
        'page_index',
        'page_size',
        'sort',
        'withDeleted'
    ]

    parseQuery(params: Object) {
        const paginate = (params) => ({
            'page_index': params.page_index,
            'page_size': params.page_size !== 'undefined' ? params.page_size : params.page_size = 10
        });

        const filters = (params) => {
            const result = {};
            Object.keys(params).forEach((key) => {
                if (this.filter.indexOf(key) === -1) {
                    result[key] = params[key];
                    if (params[key] instanceof Object) {
                        this.parseObjectParams(params, key, result);
                    }
                }
            })
            return result;
        };
        const sort = (params) => {
            return params.sort ? params.sort : {}
        }

        const withDeleted = (params) => {
            return !!params.withDeleted
        }
        return {
            where: filters(params),
            paginate: paginate(params),
            order: sort(params),
            withDeleted: withDeleted(params)
        };
    }

    parseArray(value: string): string[] {
        return value.substring(1, value.length - 1).split(',')
    }

    parseObjectParams(params, key: string, result: {}) {
        for (const prop in params[key]) {
            result[key] = this.operators[prop](params[key][prop]);
        }
    }
}