import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";

export class PYType extends BkTypeStrategyService{
    /**
     * Payroll类型流水， 对AP的票, 只找值相等的，取第一个记录
     * @param params
     * @param es
     * @param usedMap
     */
    public async find(params, es, usedMap): Promise<MatchDto[]> {
        const rest: MatchDto[] = (await this.findApForBrMatch(params, true, es.br_type))
            .filter(ap => (new Decimal(ap.balance).comparedTo(es.balance) === 0)
                && ap.invoice_currency === es.currency
                && !usedMap.has(ap.id)
            )
            .map(ap => this.convertToMatchDto(ap))
            .slice(0, 1);
        if(rest.length >0 )  rest.forEach(e => usedMap.set(e.id, e));
        return rest;
    }

    /**
     * 手动对账, Payroll类型流水，对AP的票, 找大于0的值
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return (await this.findApForBrMatch(params, true, es.br_type))
            // .filter(ap => (new Decimal(ap.balance).comparedTo(es.balance) === 0) && ap.invoice_currency === es.currency.toString())
            .filter(ap => (new Decimal(ap.balance).comparedTo(0) > 0) && ap.invoice_currency === es.currency)
            .map(ap => this.convertToMatchDto(ap))
    }


    public async getDrCr(submitDto) {
        if(BkTypeEnum.RP === submitDto.br_type.toString() || BkTypeEnum.PY === submitDto.br_type.toString()){
            return 'cr'
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of PY type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }
}