import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { OcrEsService } from './ocr-es.service';
import { CreateOcrEDto } from './dto/create-ocr-e.dto';
import { UpdateOcrEDto } from './dto/update-ocr-e.dto';
import {BaseController} from "../common/controller/base.controller";
import {OcrEs} from "./entities/ocr-es.entity";
import {ApiTags} from "@nestjs/swagger";

@ApiTags('Ocr-Es')
@Controller('ocr-es')
export class OcrEsController extends BaseController<OcrEs>{
  constructor(private readonly ocrEsService: OcrEsService) {
    super(ocrEsService);
  }

}
