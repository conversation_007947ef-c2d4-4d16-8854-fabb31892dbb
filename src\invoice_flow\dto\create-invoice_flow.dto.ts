import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class CreateInvoiceFlowDto {
    @ApiProperty({type: String, description: 'uuid', example: 'cc9e4798-6c09-4973-8ec3-b7978e142374'})
    @IsNotEmpty()
    uuid: string;

    @ApiProperty({ description: 'invoice_id' })
    @IsNotEmpty()
    invoice_id: number;

    @ApiProperty({ description: 'type' })
    @IsNotEmpty()
    type: string;

    @ApiProperty({ description: 'level' })
    @IsNotEmpty()
    level: number;

    @ApiProperty({ description: 'sort' })
    @IsNotEmpty()
    sort: number;

    @ApiProperty({ description: 'email_list' })
    @IsNotEmpty()
    email_list: string;

    @ApiProperty({ description: 'low_code' })
    low_code: string;

    @ApiProperty({ description: 'approver' })
    @IsNotEmpty()
    approver: string;

    @ApiProperty({ description: 'status' })
    @IsNotEmpty()
    status: number;
}
