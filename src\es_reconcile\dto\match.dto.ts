export class MatchDto {

    // Id
    id: string;

    //invoice ID
    invoice_id: string;

    // 公司Id
    company_id: string;

    // 公司代码
    company_code: string;

    // 公司名称
    company_name: string;

    // Invoice编码
    invoice_no: string;

    // 对账类型: 0 AR/RS; 1 AP/RP; 2 PR; 3 SR; 4 FT; 5 PY; 6 FX； 7 ES; 8 YED; 9 CP; 10 BC； 11 SP
    br_type: string;

    // entity类型: 0 AR ; 1 AP; 7 ES
    br_entity_type: string;

    // Invoice货币类型
    invoice_currency: string;

    // Invoice开票日期
    invoice_create_date: string;

    // Invoice到期日期
    invoice_due_date: string;

    // 总费用(税后)
    total_fee: number;

    // 预付款
    deposit: number;

    // withholding ISR
    mx_isr: number;

    // withholding IVA
    mx_iva: number;

    // 待付金额
    balance: number;

    // 票据识别名称
    invoice_comments: string;

    reference_no: string;

    // 对账标识 0 未对账 1 部分对账 2 完成对账
    br_flag: string;

    // sap document id
    sap_document_id: string;

    // posting date
    posting_date: string;

    // bpNumber - customerId or supplierId
    bp_number: string;

    // 银行账户
    bank_account: string;

    // 支出
    withdrawal: number;

    // Invoice类型
    expense_account: string;

    // 货币类型
    currency_type: string;

    // 银行流水的 posting_date
    date: string;


    // 引擎返回invoice的 document_no
    engine_document_id: string;

    // 供应商
    payer_payee: string;

    // reason code
    reason_code: string;


}