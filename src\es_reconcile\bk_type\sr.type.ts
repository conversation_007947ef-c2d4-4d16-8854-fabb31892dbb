import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {ReconcileSubmitDto} from "../dto/reconcile_submit.dto";
import {EntityManager} from "typeorm";
import {EsReconcileService} from "../es_reconcile.service";
import {Ar} from "../../ar/entities/ar.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";

export class SRType extends BkTypeStrategyService{
    /**
     * 自动对账， 查找负数AR的票, 只找值相等的，取第一个记录
     * @param params
     * @param es
     * @param usedMap
     */
    public async find(params, es, usedMap): Promise<MatchDto[]> {
        const rest: MatchDto[] = (await this.findArForBrMatch(params, false, es.br_type))
            .filter(ar => (new Decimal(ar.balance).abs().comparedTo(es.balance) === 0)
                && ar.invoice_currency === es.currency
                && !usedMap.has(ar.id)
            )
            .map(ap => this.convertToMatchDto(ap))
            .slice(0, 1);
        if(rest.length >0 )  rest.forEach(e => usedMap.set(e.id, e));
        return rest;
    }

    /**
     * 手动对账，查找全部负数AR的票
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return (await this.findArForBrMatch(params, false, es.br_type))
            // .filter(ar => (new Decimal(ar.balance).abs().comparedTo(es.balance) === 0) && ar.invoice_currency === es.currency.toString())
            .filter(ar => ar.invoice_currency === es.currency)
            .map(ap => this.convertToMatchDto(ap))
    }

    /**
     * process Invoices for submit
     * @param invoice
     * @param transactionalEntityManager
     * @param service
     */
    async processInvoices(invoice: ReconcileSubmitDto.Invoice, transactionalEntityManager: EntityManager, service: EsReconcileService) {
        const findOne = await service.findInvoice(Ar, transactionalEntityManager, invoice);
        // sr's balance of invoice must be negative,  reconcile_amount must be positive
        const newBalance = new Decimal(findOne.balance).add(new Decimal(invoice.reconcile_amount));
        const brFlag = service.getBrFlag(newBalance, findOne);
        // update invoice
        await transactionalEntityManager.update(Ar, {id: findOne.id}, {
            br_flag: brFlag,
            balance: newBalance.toNumber()
        });
        return {newBalance, brFlag};
    }


    public async getDrCr(submitDto) {
        if (BkTypeEnum.SR === submitDto.br_type.toString()) {
            return 'cr'
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of SR type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }

}