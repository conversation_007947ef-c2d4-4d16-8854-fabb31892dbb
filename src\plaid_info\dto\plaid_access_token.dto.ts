import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";

export class PlaidAccessTokenDto {

    @ApiProperty({description: 'company code', example: '3000'})
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({type: String, description: 'access Token', example: "access-development-e1adcsd7-f7e8-4a3c-92f6-a0f83bca2XXX"})
    @IsNotEmpty()
    accessToken: string;

    @ApiProperty({type: String, description: 'uuid', example: '2dccc9ee7a454c3a854ddeaff2066fa9'})
    @IsNotEmpty()
    uuid: string;

    @ApiProperty({type: Number, description: 'plaid Item', example: "Myaa5wABw4tkO84B3OmKFobqKXmgnXuMQPrMx"})
    plaidItem: string;
}