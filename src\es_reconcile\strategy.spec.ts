import {BkTypeEnum} from "../common/enum/bk.type.enum";

describe('EsConnectService', () => {


    it('enum_test', function () {
        let entries = Object.entries(BkTypeEnum);
        let res = entries.find(([key, val]) => val === '1')?.[0];
        console.log(res)
    });

    it('test_strategy', function () {
        const plumageMap = plumages(birds);
        const speedsMap = speeds(birds);
    });

    const birds = [{name: 'European', type: 'EuropeanSwallow'},
        {name: 'African', type: 'AfricanSwallow'},
        {name: 'Norwegian', type: 'NorwegianBlueParrot'},
    ]

    function plumages(birds) {
        // @ts-ignore
        // return new Map(birds.map(b => [b.name, plumage(b)]));
        return new Map(
            birds.map(b => createBird(b))
                .map(bird => [bird.name, bird.plumage])
        )
    }

    function speeds(birds) {
        // @ts-ignore
        // return new Map(birds.map(b => [b.name, airSpeedVelocity(b)]));
        return new Map(
            birds.map(b => createBird(b))
                .map(bird => [bird.name, bird.airSpeedVelocity])
        )
    }

    // function plumage(bird) {
    //     return new Bird(bird).plumage;
    // }
    //
    // function airSpeedVelocity(bird) {
    //     return new Bird(bird).airSpeedVelocity;
    // }
});


class Bird {
    constructor(birdObject) {
        Object.assign(this, birdObject);
    }

    get plumage() {
        return "unknown";
    }

    get airSpeedVelocity() {
        return null;
    }
}

function createBird(bird) {
    switch (bird.type) {
        case 'EuropeanSwallow':
            return new EuropeanSwallow(bird);
        case 'AfricanSwallow':
            return new AfricanSwallow(bird);
        case 'NorweigianBlueParrot':
            return new NorwegianBlueParrot(bird);
        default:
            return new Bird(bird);
    }
}


class EuropeanSwallow extends Bird {
    get plumage() {
        return "average";
    }

    get airSpeedVelocity() {
        return 35;
    }
}

class AfricanSwallow extends Bird {
    get plumage() {
        // @ts-ignore
        return (this.numberOfCoconuts > 2) ? "tired" : "average";
    }

    get airSpeedVelocity() {
        // @ts-ignore
        return 40 - 2 * this.numberOfCoconuts;
    }
}

class NorwegianBlueParrot extends Bird {
    get plumage() {
        // @ts-ignore
        return (this.voltage > 100) ? "scorched" : "beautiful";
    }

    get airSpeedVelocity() {
        // @ts-ignore
        return (this.isNailed) ? 0 : 10 + this.voltage / 10;
    }
}