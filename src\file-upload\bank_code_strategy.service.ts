import {FileUpload} from "./entities/file-upload.entity";

const papa = require('papaparse')
import {DataSource, EntityManager, Not, ObjectLiteral, Raw} from "typeorm";
import {Es} from "../es/entities/es.entity";
import {BadRequestException, HttpException, HttpStatus, Injectable, Logger} from "@nestjs/common";
import {HttpService} from "@nestjs/axios";
import {ConfigService} from "@nestjs/config";
import {CsvHeaderDataDetector} from "./dto/csv-header-data-detector";
import {Readable} from "stream";
import {Express} from "express";
import {EnumUtil} from "../common/enum/enum.util";
import {BankTypeEnum} from "../common/enum/bank_type.enum";
import {EsCsvDesKeyForRefund, EsCsvDesKeyForTransfer} from "../common/utils/const";
import {BkTypeEnum} from "../common/enum/bk.type.enum";

@Injectable()
export class BankCodeStrategyService {
    protected readonly logger = new Logger(BankCodeStrategyService.name);
    private commonKeys = ['transaction type','account type', 'first bank card','account #','account number','accountid', 'date posted','date', '\"date\"','transaction date','tansaction date', 'description','description 1', 'merchant name', 'withdraw','withdraw amount','withdrawl','withdrawal', 'credit', 'deposit','deposit amount','cad$','transaction amount', 'amount', 'debitcredit', 'debit', 'reference', 'transaction', 'transaction reference', 'member name', 'payerpayee', 'description 2'];

    constructor(protected dataSource: DataSource, protected httpService: HttpService, protected configService: ConfigService) {
    }

    /**
     * 解析UTF-8标准CSV流水文件
     */
    public async parseCsvData(file: Express.Multer.File): Promise<Array<Array<any>>> {
        // prepare file read stream from buffer
        const readableInstanceStream = new Readable({
            read() {
                this.push(file.buffer);
                this.push(null);
            }
        });
        this.logger.log('stream', JSON.stringify(readableInstanceStream));

        // parse file buffer to csv data
        const csvData: Array<Array<any>> = await this.papaParseFileBuffer(readableInstanceStream);
        console.log('csvData', csvData);
        return csvData;
    }

    /**
     * 提取CSV流水文件的header line和data line
     */
    public detectCsvHeaderAndData(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
        if (keywords.length == 0) keywords = this.commonKeys;
        for (let i = 0; i < csvData.length; i++) {
            const row = csvData[i];
            if (row.length < minColumns) continue;

            const score = row.filter(cell =>
                keywords.some(keyword => cell.toLowerCase().includes(keyword)),
            ).length;

            if (score >= 5) {
                let csvHeaderDataRes = new CsvHeaderDataDetector();
                csvHeaderDataRes.header = row;
                csvHeaderDataRes.data = csvData.slice(i + 1);
                return csvHeaderDataRes;
            }
        }
        throw new Error('No CSV header or data found.');
    }

    /**
     * 构建CSV流水文件的ES对象数组
     */
    public buildEsDtoList(csvHeaderData: CsvHeaderDataDetector, fileUploadDto: FileUpload, bankType: String): Es[] {
        const headerLine = csvHeaderData.header;
        const dataLine = csvHeaderData.data;
        const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
        let createEsDtoList: Es[] = [];
        for (let i = 0; i < dataLine.length; i++) {
            const esItem = dataLine[i];
            let createEsDto: Es = new Es();
            createEsDto.company_code = fileUploadDto.company_code;
            createEsDto.currency = fileUploadDto.file_currency;
            createEsDto.bank_account = fileUploadDto.bank_account;
            createEsDto.creator = fileUploadDto.creator;
            createEsDto.file_id = fileUploadDto.id;
            for (let j = 0; j < headerLine.length; j++) {
                const headerStr = headerLine[j];
                // account type
                if (['transaction type','account type'].includes(headerStr.trim().toLowerCase())) {
                    if (!EnumUtil.exists(esItem[j].toUpperCase(), BankTypeEnum)) throw new BadRequestException(`The account type is invalid at line [${i + 2}]. It should be like CHEQUE, SAVING or CREDIT.`)
                    createEsDto.statement_type = BankTypeEnum[esItem[j].toUpperCase()];
                }
                // account number
                if (['first bank card','account #','account number','accountid'].includes(headerStr.trim().toLowerCase())) {
                    if (createEsDto.bank_account != esItem[j]) throw new Error('The bank account number on CSV file does not match the one selected.')
                    createEsDto.bank_account = esItem[j];
                }
                // date
                if (['date posted','date', '\"date\"','transaction date','tansaction date'].includes(headerStr.trim().toLowerCase())) {
                    let dateStrFromCsv: string = esItem[j];
                    let dateStrToDatabase: string = null;
                    if (dateStrFromCsv.indexOf('/') != -1) {
                        const dateStrArray: string[] = dateStrFromCsv.split('/');
                        dateStrToDatabase = dateStrArray[2] + '-' + dateStrArray[0] + '-' + dateStrArray[1];
                    } else {
                        dateStrToDatabase = dateStrFromCsv;
                    }
                    createEsDto.date = dateStrToDatabase;
                }
                // description
                if (['description','description 1', 'merchant name'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.description = esItem[j];
                }
                // deposit
                if (['deposit','deposit amount','cad$','transaction amount', 'amount', 'debitcredit', 'debit'].includes(headerStr.trim().toLowerCase())) {
                    const numStr = esItem[j].trim() ? esItem[j] : '0';
                    const num = parseFloat(numStr);
                    if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Deposit is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
                    createEsDto.deposit = num;
                    if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
                }
                // withdrawal
                if (['withdraw','withdraw amount','withdrawl','withdrawal', 'credit'].includes(headerStr.trim().toLowerCase())) {
                    const numStr = esItem[j].trim() ? esItem[j] : '0';
                    const num = parseFloat(numStr);
                    if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
                    createEsDto.withdrawal = num;
                    if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
                }
                // reference
                if (['reference', 'transaction', 'transaction reference', 'member name'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.reference = esItem[j];
                }
                // payer payee
                if (['payerpayee'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.payer_payee = esItem[j];
                }
                // description 2
                if (['description 2'].includes(headerStr.trim().toLowerCase())) {
                    createEsDto.description = createEsDto.description.length === 0 ?
                        esItem[j] : createEsDto.description + ' && ' + esItem[j];
                }
            }
            try {
                // valid account type
                if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
                // valid account number
                if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
                // valid transaction date
                if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
                // valid description
                if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

                // handle negative value
                if (createEsDto.deposit < 0) {
                    createEsDto.withdrawal = Math.abs(createEsDto.deposit);
                    createEsDto.deposit = 0.00;
                }else if (createEsDto.withdrawal < 0) {
                    createEsDto.deposit = Math.abs(createEsDto.withdrawal);
                    createEsDto.withdrawal = 0.00;
                }
                // valid withdrawal and deposit
                if (createEsDto.deposit == null)
                    throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == null)
                    throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
                if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
                    continue;
                    // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
                if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
                    throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
                // set balance and br_type
                let isRefund, isTransfer = false;
                for (let key of EsCsvDesKeyForRefund) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isRefund = true;
                        break;
                    }
                }
                for (let key of EsCsvDesKeyForTransfer) {
                    if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
                        isTransfer = true;
                        break;
                    }
                }
                // set br_type
                if (isTransfer) {
                    createEsDto.br_type = parseInt(BkTypeEnum.FT);
                }else if (isRefund) {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
                }else {
                    createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
                }
                createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
                // set balance
                createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

                createEsDtoList.push(createEsDto);
            } catch (e) {
                this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
                throw new HttpException(e.response, e.status);
            }
        }
        return createEsDtoList;
    }

    /**
     * parse file buffer with papa library
     */
    private async papaParseFileBuffer(readableInstanceStream: Readable): Promise<Array<Array<any>>> {
        return await new Promise<Array<Array<any>>>((function (resolve) {
            papa.parse(readableInstanceStream, {
                header: false,
                worker: true,
                delimiter: ",",
                complete: (results) => {
                    resolve(results.data);
                }
            });
        }));
    }

}
