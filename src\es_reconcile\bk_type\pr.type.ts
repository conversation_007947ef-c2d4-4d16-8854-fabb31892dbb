import {MatchDto} from "../dto/match.dto";
import {Decimal} from "decimal.js";
import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {ManualEsReconcileDto} from "../dto/manual_es_reconcile.dto";
import {ReconcileSubmitDto} from "../dto/reconcile_submit.dto";
import {EntityManager} from "typeorm";
import {EsReconcileService} from "../es_reconcile.service";
import {Ap} from "../../ap/entities/ap.entity";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";

export class PRType extends BkTypeStrategyService{
    /**
     * 自动对账，查找负数AP的票, 只找值相等的，取第一个记录
     * @param params
     * @param es
     * @param usedMap
     */
    public async find(params, es, usedMap): Promise<MatchDto[]> {
        const rest: MatchDto[] = (await this.findApForBrMatch(params, false, es.br_type))
            .filter(ap => (new Decimal(ap.balance).abs().comparedTo(es.balance) === 0)
                && ap.invoice_currency === es.currency
                && !usedMap.has(ap.id)
            )
            .map(ap => this.convertToMatchDto(ap))
            .slice(0, 1);  // return first
        if(rest.length >0 )  rest.forEach(e => usedMap.set(e.id, e));
        return rest;
    }

    /**
     * 手动对账, 查找全部负数AP的票
     * @param params
     * @param es
     */
    async findForManual(params: ManualEsReconcileDto, es: any): Promise<MatchDto[]> {
        return (await this.findApForBrMatch(params, false, es.br_type))
            // .filter(ap => (new Decimal(ap.balance).abs().comparedTo(es.balance) === 0) && ap.invoice_currency === es.currency.toString())
            .filter(ap => ap.invoice_currency === es.currency)
            .map(ap => this.convertToMatchDto(ap))
    }


    async processInvoices(invoice: ReconcileSubmitDto.Invoice, transactionalEntityManager: EntityManager, service: EsReconcileService) {

        const findOne = await service.findInvoice(Ap, transactionalEntityManager, invoice);
        // pr's balance of invoice must be negative, but reconcile_amount must be positive
        const newBalance = new Decimal(findOne.balance).add(new Decimal(invoice.reconcile_amount));
        // const newBalance = new Decimal(findOne.balance).add(new Decimal(invoice.reconcile_amount));
        const brFlag = service.getBrFlag(newBalance, findOne);
        // update invoice
        await transactionalEntityManager.update(Ap, {id: findOne.id}, {
            br_flag: brFlag,
            balance: newBalance.toNumber()
        });
        return {newBalance, brFlag};
    }

    public async getDrCr(submitDto) {
        if (BkTypeEnum.PR === submitDto.br_type.toString()){
            return 'dr'
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of PR type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }
}