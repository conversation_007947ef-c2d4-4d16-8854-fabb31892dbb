import {Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne} from 'typeorm';
import { BaseEntity } from '../../common/entity/base.entity';
import {Ap} from "./ap.entity";


@Entity()
export class ApItem extends BaseEntity {

    @Column({name: 'item_no', nullable: true, comment: 'item no'})
    item_no: number;

    @Column({name: 'invoice_id', comment: 'invoice id'})
    invoice_id: number;

    @Column({name: 'model', nullable: true, comment: 'model'})
    model: string;

    @Column({name: 'description', nullable: true, comment: 'description'})
    description: string;

    @Column({name: 'qty', nullable: true, type: 'numeric', precision: 10, scale: 3, comment: 'qty'})
    qty: number;

    @Column({name: 'uom', nullable: true, comment: 'uom'})
    uom: string;

    @Column({name: 'unit_price', nullable: true, type: 'numeric', precision: 10, scale: 3, comment: 'unit price'})
    unit_price: number;

    @Column({name: 'total', type: 'numeric', precision: 10, scale: 2, default: 0.00, comment: 'total'})
    total: number;

    @Column({name: 'debit_coa_id', nullable: true, comment: 'debit CoA id'})
    debit_coa_id: number;

    @Column({name: 'debit_coa_code', nullable: true, comment: 'debit Coa code'})
    debit_coa_code: string;

    @Column({name: 'debit_coa_name', nullable: true, comment: 'debit Coa name'})
    debit_coa_name: string;

    @Column({name: 'credit_coa_id', nullable: true, comment: 'credit CoA id'})
    credit_coa_id: number;

    @Column({name: 'credit_coa_code', nullable: true, comment: 'credit Coa code'})
    credit_coa_code: string;

    @Column({name: 'credit_coa_name', nullable: true, comment: 'credit Coa name'})
    credit_coa_name: string;

    @Column({name: 'bank_account', nullable: true, comment: 'bank account'})
    bank_account: string;

    @Column({name: 'dr_cr', nullable: true, comment: 'Debit or credit: dr - Debit; cr - credit'})
    dr_cr: string;

    @Column({name: 'business_partner', nullable: true, comment: 'business partner related to gl_account'})
    business_partner: string;

    @Column({name: 'sap_gl_account', nullable: true, comment: 'sap gl account'})
    sap_gl_account: string;

    @Column({name: 'sap_wbs', nullable: true, comment: 'sap wbs'})
    sap_wbs: string;

    @Column({name: 'sap_cost_center', nullable: true, comment: 'sap cost center'})
    sap_cost_center: string;

    @Column({name: 'sap_internal_order', nullable: true, comment: 'sap internal order'})
    sap_internal_order: string;

    @Column({name: 'sap_profit_center', nullable: true, comment: 'sap profit center'})
    sap_profit_center: string;

    @Column({name: 'fiscal_year', nullable: true, comment: 'fiscal year'})
    fiscal_year: string;

    @Column({name: 'goods_receipt_item_number', nullable: true, comment: 'goods receipt item number'})
    goods_receipt_item_number: string;

    @Column({name: 'goods_receipt_number', nullable: true, comment: 'goods receipt number'})
    goods_receipt_number: string;

    @Column({name: 'material', nullable: true, comment: 'material'})
    material: string;

    @Column({name: 'po_item_number', nullable: true, comment: 'po item number'})
    po_item_number: string;

    @Column({name: 'po_number', nullable: true, comment: 'po number'})
    po_number: string;

    @Column({name: 'tax_code', nullable: true, comment: 'tax code'})
    tax_code: string;

    @ManyToOne(() => Ap, ap => ap.items)
    @JoinColumn({name: 'invoice_id'})
    ap: Ap;

}

