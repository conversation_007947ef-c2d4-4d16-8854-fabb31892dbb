import {ApiProperty, ApiPropertyOptional} from "@nestjs/swagger";
import {ApItem} from "../../ap/entities/ap.item.entity";
import {IsDateString, IsEmail, IsEnum, IsJSON, IsNotEmpty} from "class-validator";

export class CreateTaskDto {

    @ApiProperty({description: 'name'})
    name: string;

    @ApiProperty({ description: 'company code' })
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({description: 'company name'})
    company_name: string;

    @ApiProperty({description: 'period start'})
    period_start: Date;

    @ApiProperty({description: 'period end'})
    period_end: Date;

    @ApiProperty({description: 'due date'})
    @IsDateString()
    due_date: Date;

    @ApiProperty({description: 'estimated hour'})
    estimated_hour: number;

    @ApiProperty({description: 'actual hour'})
    actual_hour: number;

    @ApiProperty({description: 'status'})
    status: string;

    @ApiProperty({description: 'assign to'})
    assign_to: string;

    @ApiProperty({description: 'email'})
    email: string;

    @ApiProperty({description: 'priority'})
    priority: number;

    @ApiProperty({description: 'tag'})
    tag: string;

    @ApiProperty({description: 'del_flag: 0-not deleted; 1-deleted'})
    del_flag: string;

}
