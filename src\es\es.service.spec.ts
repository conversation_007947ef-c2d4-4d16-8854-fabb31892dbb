import { Test, TestingModule } from '@nestjs/testing';
import { EsService } from './es.service';
import {EsModule} from "./es.module";
import {TestDatabaseModule} from "../../test/dateModule.test";
import {HttpService} from "@nestjs/axios";
import {of} from "rxjs";

describe('EsService', () => {
  let service: EsService;
  let httpService: HttpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [EsModule, TestDatabaseModule],
      providers: [{
          provide: HttpService,
          useValue: {
            post: jest.fn().mockImplementation(() => of({data: {}})),
          },
      }],
    }).compile();
    httpService = module.get<HttpService>(HttpService);

    service = module.get<EsService>(EsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
