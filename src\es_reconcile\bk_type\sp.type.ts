import {BkTypeStrategyService} from "../bk_type_strategy.service";
import {Decimal} from "decimal.js";
import {ReconciliationHistory} from "../entities/reconciliation_history.entity";
import {ReconcileSubmitDto} from "../dto/reconcile_submit.dto";
import {EsReconcileService} from "../es_reconcile.service";
import {QueryRunner} from "typeorm/query-runner/QueryRunner";
import {EnumUtil} from "../../common/enum/enum.util";
import {BkTypeEnum} from "../../common/enum/bk.type.enum";
import {HttpException, HttpStatus} from "@nestjs/common";

/**
 * stripe pay
 */
export class SPType extends BkTypeStrategyService {

    /**
     * sp 对账不平，强制对账不考虑账是否平。 通常票的balance总和会大于流水
     * @param submitDto
     * @param uuid
     * @param service
     */
    async submit(submitDto: ReconcileSubmitDto, uuid, service: EsReconcileService) {
        const es = await service.findEs(submitDto);
        const historyList: ReconciliationHistory[] = [];
        const invoice_list_to_engine = [];

        //find all ars for this stripe pay and organize invoice list


        await this.dataSource.transaction(async (transactionalEntityManager) => {
            // es must be full reconcile
            let newEsBalance = new Decimal(0);
            const bkTypeStrategy = service.bkTypeFactory.getBkTypeStrategyByType(EnumUtil.getEnumKey(es.br_type.toString(), BkTypeEnum));
            for (let invoice of submitDto.invoice_list) {
                const invoiceHis = await bkTypeStrategy.processInvoices(invoice, transactionalEntityManager, service);
                await bkTypeStrategy.buildInvoiceDataToEngin(es, invoice_list_to_engine, submitDto, invoice);
                historyList.push(service.buildHistory(submitDto, invoiceHis.newBalance, invoiceHis.brFlag, uuid, invoice.reconcile_amount, invoice, invoice.invoice_id, invoice.br_entity_type, 1, 1))
            }
            //update es
            const esBrFlag = await service.updateEs(newEsBalance, es, transactionalEntityManager);

            // The number es can be reversed is equal to the number of invoices in this reconciliation
            historyList.push(service.buildHistory(submitDto, newEsBalance, esBrFlag, uuid, new Decimal(es.balance).sub(newEsBalance).toNumber(), es, es.id, submitDto.br_entity_type, submitDto.invoice_list.length, 0))
            // send to engine
            const drCr: string = await bkTypeStrategy.getDrCr(submitDto, es);
            const dataToEngine = await bkTypeStrategy.generateDataToEngine(submitDto, invoice_list_to_engine, newEsBalance, service, drCr);
            const response = await service.sendToEngine(dataToEngine.data, transactionalEntityManager, uuid, dataToEngine.url);

            //set document_no
            historyList.forEach(item => {
                item.document_no = response.document_no;
            })
            await service.saveHistories(historyList, transactionalEntityManager);
        });
        return historyList;
    }

    public async getDrCr(submitDto, es) {
        if (BkTypeEnum.SP === submitDto.br_type.toString()){
            if(es.deposit > 0) {
                return 'dr'
            }else {
                return 'cr'
            }
        }
        throw new HttpException('[es_reconcile_submit] get br_cr of SP type failed!', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * SP reverse, 账不平，强制es等于对账金额
     * @param esHistory
     * @param invoiceHistoryList
     * @param queryRunner
     */
    async reverse(esHistory: ReconciliationHistory, invoiceHistoryList: ReconciliationHistory[], queryRunner: QueryRunner) {
        let esNewBalance = new Decimal(esHistory.reconcile_amount);
        for (let invoiceHistory of invoiceHistoryList) {
            // esNewBalance = esNewBalance.add(new Decimal(invoiceHistory.reconcile_amount).abs());
            await this.reverseInvoice(invoiceHistory, esHistory, queryRunner);
        }
        return esNewBalance;
    }
}