import { Injectable } from '@nestjs/common';
import { CreateEsHistoryDto } from './dto/create-es_history.dto';
import { UpdateEsHistoryDto } from './dto/update-es_history.dto';

@Injectable()
export class EsHistoryService {
  create(createEsHistoryDto: CreateEsHistoryDto) {
    return 'This action adds a new esHistory';
  }

  findAll() {
    return `This action returns all esHistory`;
  }

  findOne(id: number) {
    return `This action returns a #${id} esHistory`;
  }

  update(id: number, updateEsHistoryDto: UpdateEsHistoryDto) {
    return `This action updates a #${id} esHistory`;
  }

  remove(id: number) {
    return `This action removes a #${id} esHistory`;
  }
}
