import {BadRequestException, HttpException, Injectable, Logger} from '@nestjs/common';
import {InjectRepository} from "@nestjs/typeorm";
import {DataSource, In, Repository} from "typeorm";
import {CreateFileUploadDto} from './dto/create-file-upload.dto';
import {FileUpload} from './entities/file-upload.entity';
import {createWriteStream} from "fs";
import {join} from "path";
import {BkTypeEnum} from "../common/enum/bk.type.enum";
import {CsvParser} from "nest-csv-parser";
import {BaseService} from "../common/service/base.service";
import {createHash} from 'crypto';
import {EsService} from "../es/es.service";
import {Es} from "../es/entities/es.entity";
import {ConfigService} from "@nestjs/config";
import {Readable} from "stream";
import {EsCsvDesKeyForRefund, EsCsvDesKeyForTransfer} from "../common/utils/const";
import {OcrUtilsService} from "../ocr-utils/ocr-utils.service";
import {Express} from "express";
import {OcrInvoice} from "../ocr-invoice/entities/ocr-invoice.entity";
import {OcrInvoiceService} from "../ocr-invoice/ocr-invoice.service";
import {FileStatusEnum} from "../common/enum/file.status.enum";
import {FileTypeEnum} from "../common/enum/file.type.enum";
import {BankTypeEnum} from "../common/enum/bank_type.enum";
import {EnumUtil} from "../common/enum/enum.util";
import {Ap} from "../ap/entities/ap.entity";
import {BrStatusEnum} from "../common/enum/br.status.enum";
import {lastValueFrom} from "rxjs";
import {catchError, map, tap} from "rxjs/operators";
import {HttpService} from "@nestjs/axios";
import {Ar} from "../ar/entities/ar.entity";
import {HttpServiceUtil} from "../common/utils/httpService.util";
import { CsvHeaderDataDetector } from './dto/csv-header-data-detector';
import {BkTypeFactory} from "../es_reconcile/bk.type.factory";
import {BankCodeFactory} from "./bank_code_factory";
import {BankCodeEnum} from "../common/enum/bank_code.enum";

const papa = require('papaparse')

@Injectable()
export class FileUploadService extends BaseService<FileUpload> {
  
  private logger = new Logger(FileUploadService.name);
  private apPatch = '/bookkeeping/ap';
  private arPatch = '/bookkeeping/ar';
  private esPatch = '/bookkeeping/es';
  private yedPatch = '/bookkeeping/yed';

  private headerAccountType: string[] = ['transaction type','account type'];  // saving, cheque, credit
  private headerAccountNumber: string[] = ['first bank card','account #','account number','accountid'];
  private headerDate: string[] = ['date posted','date', '\"date\"','transaction date','tansaction date'];
  private headerDescription: string[] = ['description','description 1', 'merchant name'];
  private headerWithdrawal: string[] = ['withdraw','withdraw amount','withdrawl','withdrawal', 'credit'];
  private headerDeposit: string[] = ['deposit','deposit amount','cad$','transaction amount', 'amount', 'debitcredit', 'debit'];
  private headerReference: string[] = ['reference', 'transaction', 'transaction reference', 'member name'];
  private headerPayerPayee: string[] = ['payerpayee'];
  private headerDescription2: string[] = ['description 2'];
  bankCodeFactory: BankCodeFactory = new BankCodeFactory(this.dataSource, this.httpService, this.configService);

  constructor(
    @InjectRepository(FileUpload) private readonly fileUploadRepository: Repository<FileUpload>,
    @InjectRepository(Ap) private readonly apRepository: Repository<Ap>,
    private readonly ocrUtilsService: OcrUtilsService,
    private readonly esService: EsService,
    private readonly ocrInvoiceService: OcrInvoiceService,
    private httpService: HttpService,
    private readonly csvParser: CsvParser,
    private configService: ConfigService,
    private dataSource: DataSource,
  ) {
    super(fileUploadRepository, 'file_upload');
  }

  create(createFileUploadDto: CreateFileUploadDto) {
    return this.fileUploadRepository.save(createFileUploadDto);
  }

  findAll(params?: any): Promise<FileUpload[]> {
    const {where, order} = this.parseParams.parseQuery(params);
    return this.fileUploadRepository.find({
      where: where,
      order: order
    });
  }

  async remove(id:number) {
    const uploadFile = await this.findOne(id);
    if (uploadFile.invoice_created_status != FileStatusEnum.NOT_GENERATED) {
      return 'this file can not be delete!';
    }
    await this.update(id, {invoice_created_status: FileStatusEnum.ABANDONED});
    return'this file delete successful!';
  }
  
  async upload(jsonStr: String, files: Express.Multer.File[]) {
    // format jsonStr
    const jsonStrFormat = JSON.parse(JSON.stringify(jsonStr)).jsonStr;
    // convert to object array
    const jsonArray = JSON.parse(jsonStrFormat);
    this.logger.log('list', jsonArray);

    // validate
    if (jsonArray.length == 0 || files.length == 0)
      throw new BadRequestException('Files or jsonStr not found');
    if (files.length > 8)
      throw new BadRequestException('No more than 8 files');
    if (files.length != jsonArray.length)
      throw new BadRequestException( 'The uploaded files are inconsistent with the number of contents filled in');

    // error list for return
    let errorList: string[] = [];
    let fileDto;

    for (let i = 0; i < files.length; i++) {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const file = files[i];
        const str = jsonArray[i];
        let createDto: FileUpload = new FileUpload();
        createDto = Object.assign(createDto, str);

        // validate jsonStr
        if (!createDto.company_code || !createDto.file_type || !createDto.file_name || !createDto.creator)
          throw new Error('company_code or file_type or file_name or creator is required');

        if (!Object.values<string>(FileTypeEnum).includes(createDto.file_type))
          throw new Error('file_type is not correct');

        if (createDto.file_type == FileTypeEnum.E_STATEMENT && !createDto.file_currency)
          throw new Error('file_currency is required');

        // generate md5 signature
        const readStream = file.buffer.toString();
        // checksum
        const md5sum = createHash('md5')
        md5sum.update(readStream);
        createDto.md5 = md5sum.digest('hex');
        this.logger.log('---md5 string---', createDto.md5);

        // validate md5 signature if it is unique in database
        let fileListFromDatabase: FileUpload[];
        if (createDto.operation_type && createDto.operation_type == '1') {
          // validation for upload automatically
          fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
        }else {
          // validation for upload manually
          // fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5, invoice_created_status: 0});
          fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
        }
        if (fileListFromDatabase.length > 0)
          throw new Error(fileListFromDatabase[0].id + ' --- it has already been exist, please do not upload it again.')

        // upload file to server
        const fileName = `${createDto.company_code}_${Date.now()}_${createDto.md5}${file.originalname.substring(file.originalname.lastIndexOf('.'))}`;
        const filePath = createDto.file_type == FileTypeEnum.E_STATEMENT ? this.esPatch :
            createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT ? this.yedPatch :
            createDto.file_type == FileTypeEnum.SALES_NOT_PAID || createDto.file_type == FileTypeEnum.SALES_CASH ? this.arPatch : this.apPatch;
        createDto.file_url = `${filePath}/${fileName}`;
        this.logger.log('upload file url: ', join(process.env.FILE_PATH, filePath, fileName));
        let writeStream = createWriteStream(join(process.env.FILE_PATH, filePath, fileName));
        writeStream.write(file.buffer);
        writeStream.close();

        // upload ocr data to abbyy if it is AR/AP
        // if (createDto.file_type == FileTypeEnum.SALES_NOT_PAID ||
        //     createDto.file_type == FileTypeEnum.SALES_CASH ||
        //     createDto.file_type == FileTypeEnum.PURCHASE_NOT_PAID ||
        //     createDto.file_type == FileTypeEnum.PURCHASE_CASH) {
        //   const ocrUploadRes = await this.ocrUpload(file);
        //   if (!ocrUploadRes) throw new BadRequestException( 'The file ocr upload error.');
        //   createDto.ocr_transaction_id = ocrUploadRes.transactionId;
        // }

        // set invoice_created_status if YED type
        if (createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT) createDto.invoice_created_status = FileStatusEnum.GENERATED;

        // save file data to database
        fileDto = await queryRunner.manager.save<FileUpload>(createDto);

        // if it is csv file, parse to es list
        if (createDto.file_type == FileTypeEnum.E_STATEMENT) {
          // prepare file read stream from buffer
          const readableInstanceStream = new Readable({
            read() {
              this.push(file.buffer);
              this.push(null);
            }
          });
          let dataLine = [];
          let headerLine = [];
          let esList: Es[] = [];
          this.logger.log('stream', JSON.stringify(readableInstanceStream));

          //调用洪博API，如果返回400，则继续本地的ES文件解析；如果返回200，则得到ES数组数据，直接保存
          const payrollAuthenticationUrl = this.configService.get('PAYROLL_AUTHENTICATION_URL');
          const authenticationParams = {
            "account": this.configService.get('PAYROLL_USER'),
            "password": this.configService.get('PAYROLL_PASSWORD'),
            "strategy": "local"
          }
          const authentication = await new HttpServiceUtil(this.httpService).post(payrollAuthenticationUrl, authenticationParams, null, '[ES_FILE_PARSE_PDF] Get payroll access token error');

          const esFileUploadUrl = this.configService.get('ES_FILE_PARSE_URL');
          this.logger.log('[ES][FILE_PARSE_SERVICE][url]', esFileUploadUrl);
          const formData = new FormData();
          formData.append('bank_code', str['bank_code']);
          formData.append('bank_type', str['bank_type'],);
          formData.append('file_type', str['file_suffix']);
          formData.append('language', str['language']);
          formData.append('country', str['country']);
          formData.append('company_code', str['company_code']);
          formData.append('bank_account', str['bank_account']);
          formData.append('statement_type', str['statement_type']);
          formData.append('file', file);

          // const payload = {
          //   'bank_code': str['bank_code'],
          //   'bank_type': str['bank_type'],
          //   'file_type': str['file_suffix'],
          //   'language': str['language'],
          //   'country': str['country'],
          //   'file': file,
          //   'company_code': str['company_code'],
          //   'bank_account': str['bank_account'],
          //   'statement_type': str['statement_type']
          // }
          this.logger.log('[ES][FILE_PARSE_SERVICE][input]', JSON.stringify(formData));
          const pdfServiceRes = await lastValueFrom(this.httpService.post(esFileUploadUrl, formData,
            {headers: { 'Authorization': 'Bearer ' + authentication.accessToken }})
              .pipe(
                  tap(response => {
                    this.logger.log(JSON.stringify(response.data));
                  }),
                  map((response) => {
                    this.logger.log('[ES][FILE_PARSE_SERVICE][output]', response.data);
                    return response.data;
                  }),
                  catchError(e => {
                    this.logger.log('[ES][FILE_PARSE_SERVICE][Error]', e.response.data);
                    // throw new HttpException(`[Engine Error] ${JSON.stringify(e.response.data)}`, e.response.status);
                    return `[ES][FILE_PARSE_SERVICE][Error] ${JSON.stringify(e.response.data)}`;
                  })
              ));
          this.logger.log('res', JSON.stringify(pdfServiceRes));
          if (pdfServiceRes.code == 200) {
            esList = Object.assign(esList, pdfServiceRes.data);
          }else {
            // parse file buffer to csv data
            const csvData: Array<Array<any>> = await this.papaParseFileBuffer(readableInstanceStream);
            console.log('csvData', csvData);

            if (str['bank_code'] && str['bank_code'] == 'AMEX') {
              // find headerList and dataList
              for (let i=0; i<csvData.length; i++) {
                let itemList: Array<string> = csvData[i];
                if (headerLine.length == 0 && i == 0) {
                  headerLine = itemList;
                }else if (headerLine.length > 0) {
                  dataLine.push(itemList);
                }
              }
              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankAMEX(headerLine, dataLine, fileDto, str['bank_type']);
            } else if (str['bank_code'] && str['bank_code'] == 'EWB') {
              // find headerList and dataList
              for (let i=0; i<csvData.length; i++) {
                let itemList: Array<string> = csvData[i];
                if (headerLine.length == 0 && i == 0) {
                  headerLine = itemList;
                }else if (headerLine.length > 0) {
                  dataLine.push(itemList);
                }
              }
              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankEWB(headerLine, dataLine, fileDto, str['bank_type']);
            } else if (str['bank_code'] && str['bank_code'] == 'CITI') {
              // find headerList and dataList
              for (let i=0; i<csvData.length; i++) {
                let itemList: Array<string> = csvData[i];
                if (headerLine.length == 0 && i == 0) {
                  headerLine = itemList;
                }else if (headerLine.length > 0) {
                  dataLine.push(itemList);
                }
              }
              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankCITI(headerLine, dataLine, fileDto, str['bank_type'], str['bank_account']);
            } else if (str['bank_code'] && str['bank_code'] == 'SANTANDER') {
              // find headerList and dataList
              // for (let i=0; i<csvData.length; i++) {
              //   let itemList: Array<string> = csvData[i];
              //   if (headerLine.length == 0 && i == 0) {
              //     headerLine = itemList;
              //   }else if (headerLine.length > 0) {
              //     dataLine.push(itemList);
              //   }
              // }
              // 检查文件类型特征
              const isCheckingAccount = this.isCheckingAccountFormat(csvData);
              const isCreditCardAccount = this.isCreditCardFormat(csvData);

              if (!isCheckingAccount && !isCreditCardAccount) {
                throw new BadRequestException('Unsupported Santander bank statement format');
              }
              if ((isCheckingAccount && str['bank_type'] !== 'Cheque') || (isCreditCardAccount && str['bank_type'] !== 'Credit')) {
                throw new BadRequestException('The statement type does not match the bank type');
              }
              let csvHeaderDataRes;
              if (isCheckingAccount) {
                csvHeaderDataRes = this.processCheckingAccount(csvData);
              } else {
                csvHeaderDataRes = this.processCreditCardAccount(csvData);
              }
              headerLine = csvHeaderDataRes.header;
              dataLine = csvHeaderDataRes.data;
              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankSANTANDER(headerLine, dataLine, fileDto, str['bank_type'], str['bank_account']);
            } else if (str['bank_code'] && str['bank_code'] == 'ICBC') {
              // find headerList and dataList
              for (let i=6; i<csvData.length-1; i++) {
                let itemList: Array<string> = csvData[i];
                if (headerLine.length == 0 && i == 6) {
                  headerLine = itemList;
                }else if (headerLine.length > 0) {
                  dataLine.push(itemList);
                }
              }
              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankICBC(headerLine, dataLine, fileDto, str['bank_type'], str['bank_account']);
            } else if (str['bank_code'] && str['bank_code'] == 'BASEINET') {
              // find headerList and dataList
              const keywords = ['folio', 'fecha', 'operación (concepto)', 'detalle', 'destinatario', 'cargo', 'abono', 'saldo'];
              const csvHeaderData = await this.detectCsvHeaderAndDataForBaseinet(csvData, keywords);
              headerLine = csvHeaderData.header;
              dataLine = csvHeaderData.data;

              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankBASEINET(headerLine, dataLine, fileDto, str['bank_type'], str['bank_account']);
            } else if (str['bank_code'] && str['bank_code'] == 'MONEX') {
              // find headerList and dataList
              const keywords = ['divisa', 'fecha operación', 'fecha de liquidación', 'descripción', 'emisora serie', 'referencia', 'cantidad plazo', 'tasa rend prima unitaria', 'precio strike', 'importe'];
              const csvHeaderData = await this.detectCsvHeaderAndDataForMonex(csvData, keywords);
              headerLine = csvHeaderData.header;
              dataLine = csvHeaderData.data;

              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankMONEX(headerLine, dataLine, fileDto, str['bank_type'], str['bank_account']);
            } else if (str['bank_code'] && str['bank_code'] == 'BBVA') {
              // find headerList and dataList
              const keywords = ['operation date', 'value date', 'code', 'observations', 'concept', 'transaction number', 'amount (cop)', 'balance (cop)'];
              const csvHeaderData = await this.detectCsvHeaderAndDataForBbva(csvData, keywords);
              headerLine = csvHeaderData.header;
              dataLine = csvHeaderData.data;

              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              esList = this.generateCreateEsDtoByBankBBVA(headerLine, dataLine, fileDto, str['bank_type'], str['bank_account']);
            } else {
              // other common bank data parse
              // find headerList and dataList
              for (let i=0; i<csvData.length; i++) {
                let itemList: Array<string> = csvData[i];
                if (headerLine.length == 0 && this.isHeaderLine(itemList)) {
                  headerLine = itemList;
                }else if (headerLine.length > 0 && this.isCommonLine(itemList)) {
                  dataLine.push(itemList);
                }
              }
              console.log("headerLine:", headerLine);
              console.log("dataLine:", dataLine);
              if (!headerLine || headerLine.length == 0) throw new Error('headerLine not found');
              if (!dataLine || dataLine.length == 0) throw new Error('dataLine not found');

              // validate headers
              const errorHeaders: string[] = this.validHeadLine(headerLine);
              if (errorHeaders.length > 0) {
                throw new Error('Wrong headers: ' + errorHeaders + ' at line 1');
              }

              // generate esDtoList
              esList = this.generateCreateEsDto(headerLine, dataLine, fileDto);
            }
            if (!esList || esList.length == 0) throw new Error('No ES line item imported.');
            for (let i = 0; i < esList.length; i++) {
              // valid data if to be duplicated
              const createEsDto: Es = esList[i];
              const duplicatedOcrEsList: Es[] = await this.esService.findAll({
                company_code: createEsDto.company_code,
                date: createEsDto.date,
                description: createEsDto.description,
                bank_account: createEsDto.bank_account,
                currency: createEsDto.currency,
                withdrawal: createEsDto.withdrawal,
                deposit: createEsDto.deposit,
                reference: createEsDto.reference
              })
              if (duplicatedOcrEsList?.length > 0) continue;
              await queryRunner.manager.save<Es>(createEsDto);
            }
          }
        }
        // commit transaction
        await queryRunner.commitTransaction();
      }
      catch (e) {
        errorList.push(e.message ? '[' + files[i].originalname + ': ' + e.message + ']' : '[' + files[i].originalname + ': ' + ' it is failed to upload]');
        await queryRunner.rollbackTransaction();
        continue;
      } finally {
        await queryRunner.release();
      }
    }
    // handle error
    if (errorList.length > 0) {
      throw new BadRequestException(errorList.toString());
    }
    return fileDto;
  }

  async uploadEsFile(jsonStr: String, files: Express.Multer.File[]) {
    // format jsonStr
    const jsonStrFormat = JSON.parse(JSON.stringify(jsonStr)).jsonStr;
    // convert to object array
    const jsonArray = JSON.parse(jsonStrFormat);
    this.logger.log('list', jsonArray);

    // validate
    if (jsonArray.length == 0 || files.length == 0)
      throw new BadRequestException('Files or jsonStr not found');
    if (files.length > 8)
      throw new BadRequestException('No more than 8 files');
    if (files.length != jsonArray.length)
      throw new BadRequestException( 'The uploaded files are inconsistent with the number of contents filled in');

    // error list for return
    let errorList: string[] = [];
    let fileDto;

    for (let i = 0; i < files.length; i++) {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const file = files[i];
        const str = jsonArray[i];
        let createDto: FileUpload = new FileUpload();
        createDto = Object.assign(createDto, str);

        // validate jsonStr
        if (!createDto.company_code || !createDto.file_type || !createDto.file_name || !createDto.creator)
          throw new Error('company_code or file_type or file_name or creator is required');

        if (!Object.values<string>(FileTypeEnum).includes(createDto.file_type))
          throw new Error('file_type is not correct');

        if (createDto.file_type == FileTypeEnum.E_STATEMENT && !createDto.file_currency)
          throw new Error('file_currency is required');

        if (str['bank_code'] == null || str['bank_code'] == '')
          throw new Error('bank_code is invalid');

        // generate md5 signature
        const readStream = file.buffer.toString();
        // checksum
        const md5sum = createHash('md5')
        md5sum.update(readStream);
        createDto.md5 = md5sum.digest('hex');
        this.logger.log('---md5 string---', createDto.md5);

        // validate md5 signature if it is unique in database
        let fileListFromDatabase: FileUpload[];
        if (createDto.operation_type && createDto.operation_type == '1') {
          // validation for upload automatically
          fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
        } else {
          // validation for upload manually
          // fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5, invoice_created_status: 0});
          fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
        }
        if (fileListFromDatabase.length > 0)
          throw new Error(fileListFromDatabase[0].id + ' --- it has already been exist, please do not upload it again.')

        // upload file to server
        const fileName = `${createDto.company_code}_${Date.now()}_${createDto.md5}${file.originalname.substring(file.originalname.lastIndexOf('.'))}`;
        const filePath = createDto.file_type == FileTypeEnum.E_STATEMENT ? this.esPatch :
            createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT ? this.yedPatch :
                createDto.file_type == FileTypeEnum.SALES_NOT_PAID || createDto.file_type == FileTypeEnum.SALES_CASH ? this.arPatch : this.apPatch;
        createDto.file_url = `${filePath}/${fileName}`;
        this.logger.log('upload file url: ', join(process.env.FILE_PATH, filePath, fileName));
        let writeStream = createWriteStream(join(process.env.FILE_PATH, filePath, fileName));
        writeStream.write(file.buffer);
        writeStream.close();

        // set invoice_created_status if YED type
        if (createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT) createDto.invoice_created_status = FileStatusEnum.GENERATED;

        // save file data to database
        fileDto = await queryRunner.manager.save<FileUpload>(createDto);
        // parse file
        const csvData: Array<Array<any>> = await this.bankCodeFactory.getBankCodeStrategyByCode(EnumUtil.getEnumKey(str['bank_code'], BankCodeEnum)).parseCsvData(file)
        console.log('--------csvData = ', csvData)
        // detect header data
        const esHeaderAndData: CsvHeaderDataDetector = this.bankCodeFactory.getBankCodeStrategyByCode(EnumUtil.getEnumKey(str['bank_code'], BankCodeEnum))
            .detectCsvHeaderAndData(csvData, []);
        // build ES[]
        const esList: Es[] = this.bankCodeFactory.getBankCodeStrategyByCode(EnumUtil.getEnumKey(str['bank_code'], BankCodeEnum))
            .buildEsDtoList(esHeaderAndData, fileDto, str['bank_type']);

        if (!esList || esList.length == 0) throw new Error('No ES line item imported.');
        for (let i = 0; i < esList.length; i++) {
          // valid data if to be duplicated
          const createEsDto: Es = esList[i];
          const duplicatedOcrEsList: Es[] = await this.esService.findAll({
            company_code: createEsDto.company_code,
            date: createEsDto.date,
            description: createEsDto.description,
            bank_account: createEsDto.bank_account,
            currency: createEsDto.currency,
            withdrawal: createEsDto.withdrawal,
            deposit: createEsDto.deposit,
            reference: createEsDto.reference
          })
          if (duplicatedOcrEsList?.length > 0) continue;
          await queryRunner.manager.save<Es>(createEsDto);
        }

        // commit transaction
        await queryRunner.commitTransaction();
      } catch (e) {
        errorList.push(e.message ? '[' + files[i].originalname + ': ' + e.message + ']' : '[' + files[i].originalname + ': ' + ' it is failed to upload]');
        await queryRunner.rollbackTransaction();
        continue;
      } finally {
        await queryRunner.release();
      }
    }
  }

  async uploadFileAndXmlNew(jsonStr: String, files: Express.Multer.File[]) {
    // format jsonStr
    const jsonStrFormat = JSON.parse(JSON.stringify(jsonStr)).jsonStr;
    // convert to object array
    const jsonArray = JSON.parse(jsonStrFormat);
    this.logger.log('list', jsonArray);
    this.logger.log('jsonArray.length', jsonArray.length);

    // validate
    if (jsonArray.length == 0 || files.length == 0)
      throw new BadRequestException('Files or jsonStr not found');
    if (files.length > 8)
      throw new BadRequestException('No more than 8 files');

    // Separate PDF and XML files
    const pdfFiles = files.filter(file =>
        file.mimetype === 'application/pdf' ||
        file.originalname.toLowerCase().endsWith('.pdf') ||
        file.mimetype.startsWith('image/')
    );
    const xmlFiles = files.filter(file => this.checkXmlFileType(file));
    // const otherFiles = files.filter(file =>
    //     !this.checkXmlFileType(file) &&
    //     !(file.mimetype === 'application/pdf' || file.originalname.toLowerCase().endsWith('.pdf'))
    // );

    // Validate file matching: PDF files with XML files by name
    const pdfFileNames = pdfFiles.map(file => this.getFileNameWithoutExtension(file.originalname));
    const xmlFileNames = xmlFiles.map(file => this.getFileNameWithoutExtension(file.originalname));

    // Check if all XML files have matching PDF files
    const unmatchedXmlFiles = xmlFileNames.filter(xmlName => !pdfFileNames.includes(xmlName));
    if (unmatchedXmlFiles.length > 0) {
      throw new BadRequestException(`XML files without matching PDF files: ${unmatchedXmlFiles.join(', ')}`);
    }

    // Create file matching map
    const fileMatches = new Map<string, { pdf?: Express.Multer.File, xml?: Express.Multer.File }>();

    // Add PDF files to the map
    pdfFiles.forEach(file => {
      const fileName = this.getFileNameWithoutExtension(file.originalname);
      if (!fileMatches.has(fileName)) {
        fileMatches.set(fileName, {});
      }
      fileMatches.get(fileName)!.pdf = file;
    });

    // Add XML files to the map
    xmlFiles.forEach(file => {
      const fileName = this.getFileNameWithoutExtension(file.originalname);
      if (!fileMatches.has(fileName)) {
        fileMatches.set(fileName, {});
      }
      fileMatches.get(fileName)!.xml = file;
    });

    // error list for return
    let errorList: string[] = [];
    let fileDtoList: FileUpload[] = [];

    let recordId;

    // 0-init transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const [key, value] of fileMatches) {
        const pdfFile = value.pdf;
        const xmlFile = value.xml;

        // 1-generate createDto and valid params
        const str = jsonArray[0];
        let createDto: FileUpload = new FileUpload();
        createDto = Object.assign(createDto, str);
        createDto.file_name = '';
        // validate jsonStr
        if (!createDto.company_code || !createDto.file_type || !createDto.creator)
          throw new Error('company_code or file_type or file_name or creator is required');

        if (!Object.values<string>(FileTypeEnum).includes(createDto.file_type))
          throw new Error('file_type is not correct');

        // set invoice_created_status if YED type
        if (createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT) createDto.invoice_created_status = FileStatusEnum.GENERATED;
        // set xml_status to PENDING
        createDto.xml_status = 'PENDING';

        // 2-upload file and set url
        if (pdfFile) {
          // set file name
          createDto.file_name = pdfFile.originalname;
          // generate md5 signature
          const readStream = pdfFile.buffer.toString();
          // checksum
          const md5sum = createHash('md5')
          md5sum.update(readStream);
          createDto.md5 = md5sum.digest('hex');
          this.logger.log('---md5 string---', createDto.md5);

          // validate md5 signature if it is unique in database
          let fileListFromDatabase: FileUpload[];
          if (createDto.operation_type && createDto.operation_type == '1') {
            // validation for upload automatically
            fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
          } else {
            // validation for upload manually
            fileListFromDatabase = await this.findAll({
              company_code: createDto.company_code,
              md5: createDto.md5,
              // invoice_created_status: 0
            });
          }
          if (fileListFromDatabase.length > 0)
            throw new Error(fileListFromDatabase[0].id + ' --- it has already been exist, please do not upload it again.')
        }

        // upload file to server
        const filePath = createDto.file_type == FileTypeEnum.E_STATEMENT ? this.esPatch :
            createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT ? this.yedPatch :
                createDto.file_type == FileTypeEnum.SALES_NOT_PAID || createDto.file_type == FileTypeEnum.SALES_CASH ? this.arPatch : this.apPatch;

        if (pdfFile) {
          const pdfFileName = `${createDto.company_code}_${Date.now()}_${createDto.md5}${pdfFile.originalname.substring(pdfFile.originalname.lastIndexOf('.'))}`;
          createDto.file_url = `${filePath}/${pdfFileName}`;
          let pdfStream = createWriteStream(join(process.env.FILE_PATH, filePath, pdfFileName));
          pdfStream.on('finish', () => console.log('PDF 写入完成'));
          pdfStream.on('error', err => console.error('PDF 写入失败', err));
          pdfStream.end(pdfFile.buffer); // 一步完成写入并关闭
        }
        if (xmlFile) {
          const xmlFileName = `${createDto.company_code}_${Date.now()}_${xmlFile.originalname}`;
          createDto.xml_url = `${filePath}/${xmlFileName}`;
          let xmlStream = createWriteStream(join(process.env.FILE_PATH, filePath, xmlFileName));
          xmlStream.on('finish', () => console.log('XML 写入完成'));
          xmlStream.on('error', err => console.error('XML 写入失败', err));
          xmlStream.end(xmlFile.buffer); // 一步完成写入并关闭
        }
        // save file data to database
        const fileDto = await queryRunner.manager.save<FileUpload>(createDto);
        fileDtoList.push(fileDto);
        // send xml validation request
        if (pdfFile && xmlFile) {
          const validXmlRes = await this.sendXmlValidationRequest(fileDto);
          if (validXmlRes.code != 200) {
            // await this.updateXmlStatus(fileDto.id, {xml_status: 'FAILURE'});
            await queryRunner.manager.update<FileUpload>(FileUpload, fileDto.id, {xml_status: 'FAILURE'});
          }
        }
      }
      // commit transaction
      await queryRunner.commitTransaction();
    }
    catch (e) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(e.message);
    } finally {
      await queryRunner.release();
    }

    return fileDtoList;
  }

  async uploadFileAndXml(jsonStr: String, files: Express.Multer.File[]) {
    // format jsonStr
    const jsonStrFormat = JSON.parse(JSON.stringify(jsonStr)).jsonStr;
    // convert to object array
    const jsonArray = JSON.parse(jsonStrFormat);
    this.logger.log('list', jsonArray);
    this.logger.log('jsonArray.length', jsonArray.length);

    // validate
    if (jsonArray.length == 0 || files.length == 0)
      throw new BadRequestException('Files or jsonStr not found');
    if (files.length != 2)
      throw new BadRequestException('Should be 2 files');
    if (jsonArray.length != 1)
      throw new BadRequestException( 'The number of contents filled in should be only one');

    // Check if XML file is included
    const hasXmlFile = files.some(file =>
        file.mimetype === 'text/xml' ||
        file.mimetype === 'application/xml' ||
        file.originalname.toLowerCase().endsWith('.xml')
    );
    if (!hasXmlFile)
      throw new BadRequestException('At least one of the files must be an XML file.');

    // error list for return
    let errorList: string[] = [];
    let fileDto;

    let recordId;

    // 0-init transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1-generate createDto and valid params
      const str = jsonArray[0];
      let createDto: FileUpload = new FileUpload();
      createDto = Object.assign(createDto, str);
      // validate jsonStr
      if (!createDto.company_code || !createDto.file_type || !createDto.file_name || !createDto.creator)
        throw new Error('company_code or file_type or file_name or creator is required');

      if (!Object.values<string>(FileTypeEnum).includes(createDto.file_type))
        throw new Error('file_type is not correct');

      if (createDto.file_type == FileTypeEnum.E_STATEMENT && !createDto.file_currency)
        throw new Error('file_currency is required');

      // set invoice_created_status if YED type
      if (createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT) createDto.invoice_created_status = FileStatusEnum.GENERATED;
      // set xml_status to PENDING
      createDto.xml_status = 'PENDING';

      // 2-upload file and set url
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (!this.checkXmlFileType(file)) {
          // generate md5 signature
          const readStream = file.buffer.toString();
          // checksum
          const md5sum = createHash('md5')
          md5sum.update(readStream);
          createDto.md5 = md5sum.digest('hex');
          this.logger.log('---md5 string---', createDto.md5);

          // validate md5 signature if it is unique in database
          let fileListFromDatabase: FileUpload[];
          if (createDto.operation_type && createDto.operation_type == '1') {
            // validation for upload automatically
            fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
          } else {
            // validation for upload manually
            fileListFromDatabase = await this.findAll({
              company_code: createDto.company_code,
              md5: createDto.md5,
              // invoice_created_status: 0
            });
          }
          if (fileListFromDatabase.length > 0)
            throw new Error(fileListFromDatabase[0].id + ' --- it has already been exist, please do not upload it again.')
        }

        // upload file to server
        const filePath = createDto.file_type == FileTypeEnum.E_STATEMENT ? this.esPatch :
            createDto.file_type == FileTypeEnum.YEAR_END_DOCUMENT ? this.yedPatch :
                createDto.file_type == FileTypeEnum.SALES_NOT_PAID || createDto.file_type == FileTypeEnum.SALES_CASH ? this.arPatch : this.apPatch;
        let fileName;
        if (this.checkXmlFileType(file)) {
          fileName = `${createDto.company_code}_${Date.now()}_${file.originalname}`;
          createDto.xml_url = `${filePath}/${fileName}`;
        } else {
          fileName = `${createDto.company_code}_${Date.now()}_${createDto.md5}${file.originalname.substring(file.originalname.lastIndexOf('.'))}`;
          createDto.file_url = `${filePath}/${fileName}`;
        }

        this.logger.log('upload file url: ', join(process.env.FILE_PATH, filePath, fileName));
        let writeStream = createWriteStream(join(process.env.FILE_PATH, filePath, fileName));
        writeStream.write(file.buffer);
        writeStream.close();
      }
      // save file data to database
      fileDto = await queryRunner.manager.save<FileUpload>(createDto);
      // commit transaction
      await queryRunner.commitTransaction();
    }
    catch (e) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(e.message);
    } finally {
      await queryRunner.release();
    }
    // send xml validation request
    const validXmlRes = await this.sendXmlValidationRequest(fileDto);
    if (validXmlRes.code != 200) {
      await this.updateXmlStatus(fileDto.id, {xml_status: 'FAILURE'});
      // await queryRunner.manager.update<FileUpload>(FileUpload, fileDto.id, {xml_status: 'FAILURE'});
    }
    return fileDto;
  }

  async sendXmlValidationRequest(fileDto: FileUpload) {
    const payrollAuthenticationUrl = this.configService.get('PAYROLL_AUTHENTICATION_URL');
    const authenticationParams = {
      "account": this.configService.get('PAYROLL_USER'),
      "password": this.configService.get('PAYROLL_PASSWORD'),
      "strategy": "local"
    }
    const authentication = await new HttpServiceUtil(this.httpService).post(payrollAuthenticationUrl, authenticationParams, null, '[file_upload_MX] Get payroll access token error');
    const xmlValidationUrl = this.configService.get('PAYROLL_BASE_URL') + `/prefectClient/xml_validate`;
    const validationXmlBody = {
      "xml_file_id": fileDto.id,
      "xml_url": `${this.configService.get('UPLOAD_FILE_PATH')}${fileDto.xml_url}`
    };
    return await new HttpServiceUtil(this.httpService).post(xmlValidationUrl, validationXmlBody, authentication.accessToken, '[file_upload_MX] XML validation error');
  }

  checkXmlFileType(file: Express.Multer.File): boolean {
    return (file.mimetype === 'text/xml' ||
        file.mimetype === 'application/xml' ||
        file.originalname.toLowerCase().endsWith('.xml'));
  }

  /**
   * Get filename without extension
   * @param filename - original filename with extension
   * @returns filename without extension
   */
  private getFileNameWithoutExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex === -1 ? filename : filename.substring(0, lastDotIndex);
  }

  async papaParseFileBuffer(readableInstanceStream: Readable): Promise<Array<Array<any>>> {
    return await new Promise<Array<Array<any>>>((function (resolve) {
      papa.parse(readableInstanceStream, {
        header: false,
        worker: true,
        delimiter: ",",
        complete: (results) => {
          resolve(results.data);
        }
      });
    }));
  }

  isHeaderLine(line: string[]): boolean {
    const headerWithdrawal: string[] = ['withdraw','withdraw amount','withdrawl','withdrawal'];
    const headerDeposit: string[] = ['deposit','deposit amount','cad$','transaction amount'];
    const headerLine = headerWithdrawal.concat(headerDeposit);
    let lineStr = '';
    for (let item of line) {
      lineStr += item.trim().toLowerCase() + ' ';
    }
    let res = false;
    for (let header of headerLine) {
      if (lineStr.includes(header.toLowerCase())) {
        res = true;
        break;
      }
    }
    return res;
  }

  isCommonLine(line: string[]): boolean {
    let lineStr = '';
    for (let item of line) {
      lineStr += item.trim().toLowerCase() + ' ';
    }
    let res = false;
    if ('' != lineStr.trim()) {
      res = true;
    }
    return res;
  }

  validHeadLine(headerLine: string[]): string[] {
    let errorHeads: string[] = [];
    let headerTemplateList: string[] = [];
    headerTemplateList.push(...this.headerAccountType, ...this.headerAccountNumber, ...this.headerDate, ...this.headerDescription,
        ...this.headerWithdrawal, ...this.headerDeposit, ...this.headerReference, ...this.headerPayerPayee, ...this.headerDescription2);

    for (let j = 0; j < headerLine.length; j++) {
      const headerStr = headerLine[j];
      if (!headerTemplateList.includes(headerStr.trim().toLowerCase())) {
        errorHeads.push(headerStr);
      }
    }
    return errorHeads;
  }

  generateCreateEsDto(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      for (let j = 0; j < headerLine.length; j++) {
        const headerStr = headerLine[j];
        // account type
        if (this.headerAccountType.includes(headerStr.trim().toLowerCase())) {
          if (!EnumUtil.exists(esItem[j].toUpperCase(), BankTypeEnum)) throw new BadRequestException(`The account type is invalid at line [${i + 2}]. It should be like CHEQUE, SAVING or CREDIT.`)
          createEsDto.statement_type = BankTypeEnum[esItem[j].toUpperCase()];
        }
        // account number
        if (this.headerAccountNumber.includes(headerStr.trim().toLowerCase())) {
          if (createEsDto.bank_account != esItem[j]) throw new Error('The bank account number on CSV file does not match the one selected.')
          createEsDto.bank_account = esItem[j];
        }
        // date
        if (this.headerDate.includes(headerStr.trim().toLowerCase())) {
          let dateStrFromCsv: string = esItem[j];
          let dateStrToDatabase: string = null;
          if (dateStrFromCsv.indexOf('/') != -1) {
            const dateStrArray: string[] = dateStrFromCsv.split('/');
            dateStrToDatabase = dateStrArray[2] + '-' + dateStrArray[0] + '-' + dateStrArray[1];
          } else {
            dateStrToDatabase = dateStrFromCsv;
          }
          createEsDto.date = dateStrToDatabase;
        }
        // description
        if (this.headerDescription.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = esItem[j];
        }
        // deposit
        if (this.headerDeposit.includes(headerStr.trim().toLowerCase())) {
          const numStr = esItem[j].trim() ? esItem[j] : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Deposit is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.deposit = num;
          if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
        }
        // withdrawal
        if (this.headerWithdrawal.includes(headerStr.trim().toLowerCase())) {
          const numStr = esItem[j].trim() ? esItem[j] : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.withdrawal = num;
          if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
        }
        // reference
        if (this.headerReference.includes(headerStr.trim().toLowerCase())) {
          createEsDto.reference = esItem[j];
        }
        // payer payee
        if (this.headerPayerPayee.includes(headerStr.trim().toLowerCase())) {
          createEsDto.payer_payee = esItem[j];
        }
        // description 2
        if (this.headerDescription2.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = createEsDto.description.length === 0 ?
              esItem[j] : createEsDto.description + ' && ' + esItem[j];
        }
      }
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }
        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        for (let key of EsCsvDesKeyForTransfer) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isTransfer = true;
            break;
          }
        }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
            // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankAMEX(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      for (let j = 0; j < headerLine.length; j++) {
        const headerStr = headerLine[j];
        // account type
        createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
        // account number
        if (this.headerAccountNumber.includes(headerStr.trim().toLowerCase())) {
          // if (createEsDto.bank_account != esItem[j]) throw new Error('The bank account number on CSV file does not match the one selected.')
          createEsDto.bank_account = esItem[j];
        }
        // date
        if (this.headerDate.includes(headerStr.trim().toLowerCase())) {
          let dateStrFromCsv: string = esItem[j];
          let dateStrToDatabase: string = null;
          if (dateStrFromCsv.indexOf('/') != -1) {
            const dateStrArray: string[] = dateStrFromCsv.split('/');
            dateStrToDatabase = dateStrArray[2] + '-' + dateStrArray[0] + '-' + dateStrArray[1];
          } else {
            dateStrToDatabase = dateStrFromCsv;
          }
          createEsDto.date = dateStrToDatabase;
        }
        // description
        if (this.headerDescription.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = esItem[j];
        }
        // deposit
        if (this.headerDeposit.includes(headerStr.trim().toLowerCase())) {
          const numStr = esItem[j].trim() ? esItem[j] : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Deposit is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          if (bankType == 'Credit') {
            if (num >= 0) {
              createEsDto.withdrawal = num;
              createEsDto.deposit = 0.00;
            } else {
              createEsDto.deposit = -num;
              createEsDto.withdrawal = 0.00;
            }
          } else {
            if (num >= 0) {
              createEsDto.deposit = num;
              createEsDto.withdrawal = 0.00;
            } else {
              createEsDto.withdrawal = -num;
              createEsDto.deposit = 0.00;
            }
          }
        }
        // reference
        if (this.headerReference.includes(headerStr.trim().toLowerCase())) {
          createEsDto.reference = esItem[j];
        }
        // payer payee
        if (this.headerPayerPayee.includes(headerStr.trim().toLowerCase())) {
          createEsDto.payer_payee = esItem[j];
        }
        // description 2
        if (this.headerDescription2.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = createEsDto.description.length === 0 ?
              esItem[j] : createEsDto.description + ' && ' + esItem[j];
        }
      }
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        for (let key of EsCsvDesKeyForTransfer) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isTransfer = true;
            break;
          }
        }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankEWB(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      for (let j = 0; j < headerLine.length; j++) {
        const headerStr = headerLine[j];
        // account type
        createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
        // account number
        if (this.headerAccountNumber.includes(headerStr.trim().toLowerCase())) {
          // if (createEsDto.bank_account != esItem[j]) throw new Error('The bank account number on CSV file does not match the one selected.')
          createEsDto.bank_account = esItem[j];
        }
        // date
        if (this.headerDate.includes(headerStr.trim().toLowerCase())) {
          let dateStrFromCsv: string = esItem[j];
          let dateStrToDatabase: string = null;
          if (dateStrFromCsv.indexOf('/') != -1) {
            const dateStrArray: string[] = dateStrFromCsv.split('/');
            dateStrToDatabase = dateStrArray[2] + '-' + dateStrArray[0] + '-' + dateStrArray[1];
          } else {
            dateStrToDatabase = dateStrFromCsv;
          }
          createEsDto.date = dateStrToDatabase;
        }
        // description
        if (this.headerDescription.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = esItem[j];
        }
        // deposit
        if (this.headerDeposit.includes(headerStr.trim().toLowerCase())) {
          const numStr = esItem[j].trim() ? esItem[j] : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Deposit is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          if (bankType == 'Credit') {
            if (num >= 0) {
              createEsDto.withdrawal = num;
              createEsDto.deposit = 0.00;
            } else {
              createEsDto.deposit = -num;
              createEsDto.withdrawal = 0.00;
            }
          } else {
            if (num >= 0) {
              createEsDto.deposit = num;
              createEsDto.withdrawal = 0.00;
            } else {
              createEsDto.withdrawal = -num;
              createEsDto.deposit = 0.00;
            }
          }
        }
        // reference
        if (this.headerReference.includes(headerStr.trim().toLowerCase())) {
          createEsDto.reference = esItem[j];
        }
        // payer payee
        if (this.headerPayerPayee.includes(headerStr.trim().toLowerCase())) {
          createEsDto.payer_payee = esItem[j];
        }
        // description 2
        if (this.headerDescription2.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = createEsDto.description.length === 0 ?
              esItem[j] : createEsDto.description + ' && ' + esItem[j];
        }
      }
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        for (let key of EsCsvDesKeyForTransfer) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isTransfer = true;
            break;
          }
        }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        if (createEsDto.withdrawal > 0) {
          createEsDtoList.push(createEsDto);
        }
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankCITI(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String, bankAccount: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      for (let j = 0; j < headerLine.length; j++) {
        const headerStr = headerLine[j];
        // date
        if (this.headerDate.includes(headerStr.trim().toLowerCase())) {
          let dateStrFromCsv: string = esItem[j];
          let dateStrToDatabase: string = null;
          if (dateStrFromCsv.indexOf('/') != -1) {
            const dateStrArray: string[] = dateStrFromCsv.split('/');
            dateStrToDatabase = dateStrArray[2] + '-' + dateStrArray[0] + '-' + dateStrArray[1];
          } else {
            dateStrToDatabase = dateStrFromCsv;
          }
          createEsDto.date = dateStrToDatabase;
        }
        // description
        if (this.headerDescription.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = esItem[j];
        }
        // deposit
        if (this.headerDeposit.includes(headerStr.trim().toLowerCase())) {
          const numStr = esItem[j].trim() ? esItem[j] : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Deposit is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.withdrawal = num;
          if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
        }
        // withdrawal
        if (this.headerWithdrawal.includes(headerStr.trim().toLowerCase())) {
          const numStr = esItem[j].trim() ? esItem[j] : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.deposit = num;
          if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
        }
        // reference
        if (this.headerReference.includes(headerStr.trim().toLowerCase())) {
          createEsDto.reference = esItem[j];
        }
        // payer payee
        if (this.headerPayerPayee.includes(headerStr.trim().toLowerCase())) {
          createEsDto.payer_payee = esItem[j];
        }
        // description 2
        if (this.headerDescription2.includes(headerStr.trim().toLowerCase())) {
          createEsDto.description = createEsDto.description.length === 0 ?
              esItem[j] : createEsDto.description + ' && ' + esItem[j];
        }
      }
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        for (let key of EsCsvDesKeyForTransfer) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isTransfer = true;
            break;
          }
        }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankSANTANDER(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String, bankAccount: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      // date - column 2
      let dateStrFromCsv: string = esItem[1].replace('\'', '');
      createEsDto.date = dateStrFromCsv.substring(4, 8) + '-' + dateStrFromCsv.substring(2, 4) + '-' + dateStrFromCsv.substring(0, 2);
      // description - column 10
      createEsDto.description = esItem[9];
      // deposit - withdrawal - column 7
      const numSignal = esItem[5].trim() ? esItem[5] : '+';
      const numStr = esItem[6].trim() ? esItem[6].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
      const num = parseFloat(numStr);
      if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
      if (numSignal == '+') {
        createEsDto.deposit = num;
        createEsDto.withdrawal = 0.00;
      } else {
        createEsDto.deposit = 0.00;
        createEsDto.withdrawal = num;
      }
      // reference - column 9
      createEsDto.reference = esItem[8];
      // payer payee - column 15 - Nombre Ordenante - Payer Name
      createEsDto.payer_payee = esItem[14];
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        for (let key of EsCsvDesKeyForTransfer) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isTransfer = true;
            break;
          }
        }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankICBC(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String, bankAccount: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      for (let j = 0; j < headerLine.length; j++) {
        const headerStr = headerLine[j];
        // date
        if (['交易时间', 'tiempo de transacción'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          let dateStrFromCsv: string = esItem[j].replace(/\t/g, '').trim();
          const dateStrArray: string[] = dateStrFromCsv.substring(0, 10).split('-');
          createEsDto.date = dateStrArray[2] + '-' + dateStrArray[1] + '-' + dateStrArray[0];
        }
        // description
        if (['摘要', 'resumen'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          createEsDto.description = esItem[j].replace(/\t/g, '').trim();
        }
        // deposit - withdrawal
        if (['收入金额', 'importe de abono'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').trim() : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.deposit = num;
          if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
        }
        if (['支出金额', 'importe de cargo'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').trim() : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.withdrawal = num;
          if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
        }
        // reference
        if (['备注', 'observación'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          createEsDto.reference = esItem[j].replace(/\t/g, '').trim();
        }
        // payer payee
        if (['对方账户', 'cuenta de contrapartida'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          createEsDto.payer_payee = esItem[j].replace(/\t/g, '').trim();
        }
      }
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        // for (let key of EsCsvDesKeyForTransfer) {
        //   if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
        //     isTransfer = true;
        //     break;
        //   }
        // }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankBASEINET(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String, bankAccount: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      // date - column 2
      let dateStrFromCsv: string = esItem[1].replace(/\t/g, '').trim();
      const dateStrArray: string[] = dateStrFromCsv.substring(0, 10).split('-');
      createEsDto.date = dateStrArray[2] + '-' + dateStrArray[1] + '-' + dateStrArray[0];
      // description - column 3
      createEsDto.description = esItem[2].replace(/\t/g, '').trim();
      // withdraw - column 6
      const numStrWithdraw = esItem[5].trim() ? esItem[5].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
      const numWithdraw = parseFloat(numStrWithdraw);
      if (!regex.test(numStrWithdraw) || isNaN(numWithdraw)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
      createEsDto.withdrawal = numWithdraw;
      if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
      // deposit - column 7
      const numStrDeposit = esItem[6].trim() ? esItem[6].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
      const numDeposit = parseFloat(numStrDeposit);
      if (!regex.test(numStrDeposit) || isNaN(numDeposit)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
      createEsDto.deposit = numDeposit;
      if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
      // reference - column 4
      createEsDto.reference = esItem[3].replace(/\t/g, '').trim();
      // payer payee - column 5
      createEsDto.payer_payee = esItem[4].replace(/\t/g, '').trim();
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        // for (let key of EsCsvDesKeyForTransfer) {
        //   if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
        //     isTransfer = true;
        //     break;
        //   }
        // }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankMONEX(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String, bankAccount: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      // date - column 2
      let dateStrFromCsv: string = esItem[1].replace(/\t/g, '').trim();
      const dateStrArray: string[] = dateStrFromCsv.substring(0, 10).split('/');
      createEsDto.date = dateStrArray[2] + '-' + dateStrArray[1] + '-' + dateStrArray[0];
      // description - column 4
      createEsDto.description = esItem[3].replace(/\t/g, '').trim();
      // deposit / withdrawal - column 11
      const numStr = esItem[10].trim() ? esItem[10].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
      // 处理括号表示的负数，如($100,000.00)转换为-100000.00
      const processedNumStr = numStr.startsWith('(') && numStr.endsWith(')')
          ? '-' + numStr.substring(1, numStr.length - 1)
          : numStr;
      const num = parseFloat(processedNumStr);
      if (!regex.test(processedNumStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
      createEsDto.deposit = num;
      createEsDto.withdrawal = 0.00;
      // reference - column 6
      createEsDto.reference = esItem[5].replace(/\t/g, '').trim();
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        // for (let key of EsCsvDesKeyForTransfer) {
        //   if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
        //     isTransfer = true;
        //     break;
        //   }
        // }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  generateCreateEsDtoByBankBBVA(headerLine: string[], dataLine: any[], fileUploadDto: FileUpload, bankType: String, bankAccount: String): Es[] {
    this.logger.log('keys', headerLine);
    const regex = /^-?[0-9]+(\.[0-9]{1,2})?$/;
    let createEsDtoList: Es[] = [];
    for (let i = 0; i < dataLine.length; i++) {
      const esItem = dataLine[i];
      // valid data line
      let dataContent = '';
      for (let t = 0; t < esItem.length; t++) {
        dataContent += esItem[t];
      }
      if (dataContent.trim() == '') continue;
      let createEsDto: Es = new Es();
      createEsDto.company_code = fileUploadDto.company_code;
      createEsDto.currency = fileUploadDto.file_currency;
      createEsDto.bank_account = fileUploadDto.bank_account;
      createEsDto.statement_type = BankTypeEnum[bankType.toUpperCase()];
      createEsDto.creator = fileUploadDto.creator;
      createEsDto.file_id = fileUploadDto.id;
      for (let j = 0; j < headerLine.length; j++) {
        const headerStr = headerLine[j];
        // date
        if (['operation date'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          let dateStrFromCsv: string = esItem[j].replace(/\t/g, '').trim();
          const dateStrArray: string[] = dateStrFromCsv.substring(0, 10).split('-');
          createEsDto.date = dateStrArray[2] + '-' + dateStrArray[1] + '-' + dateStrArray[0];
        }
        // description
        if (['observations'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          createEsDto.description = esItem[j].replace(/\t/g, '').trim();
        }
        // deposit - withdrawal
        if (['amount (cop)'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
          const num = parseFloat(numStr);
          if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
          createEsDto.deposit = num;
          if (!createEsDto.withdrawal && createEsDto.deposit != 0) createEsDto.withdrawal = 0.00;
        }
        // if (['abono'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
        //   const numStr = esItem[j].trim() ? esItem[j].replace(/\t/g, '').replace(/,/g, '').replace(/\$/g, '').trim() : '0';
        //   const num = parseFloat(numStr);
        //   if (!regex.test(numStr) || isNaN(num)) throw new BadRequestException(`The value of Withdrawal is invalid at line [${i + 2}]. It should be valid number, e.g: 1.25.`)
        //   createEsDto.withdrawal = num;
        //   if (!createEsDto.deposit && createEsDto.withdrawal != 0) createEsDto.deposit = 0.00;
        // }
        // reference
        if (['concept'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
          createEsDto.reference = esItem[j].replace(/\t/g, '').trim();
        }
        // payer payee
        // if (['destinatario'].includes(headerStr.toLowerCase().replace(/\t/g, '').trim())) {
        //   createEsDto.payer_payee = esItem[j].replace(/\t/g, '').trim();
        // }
      }
      try {
        // valid account type
        if (!createEsDto.statement_type) throw new BadRequestException(`The account type is required at line [${i + 2}].`);
        // valid account number
        if (!createEsDto.bank_account) throw new BadRequestException(`The account number is required at line [${i + 2}].`);
        // valid transaction date
        if (!createEsDto.date) throw new BadRequestException(`The transaction date is required at line [${i + 2}].`);
        // valid description
        if (createEsDto.description == null) throw new BadRequestException(`The description is required at line [${i + 2}].`);

        // handle negative value
        if (createEsDto.deposit < 0) {
          createEsDto.withdrawal = Math.abs(createEsDto.deposit);
          createEsDto.deposit = 0.00;
        }else if (createEsDto.withdrawal < 0) {
          createEsDto.deposit = Math.abs(createEsDto.withdrawal);
          createEsDto.withdrawal = 0.00;
        }

        // valid withdrawal and deposit
        if (createEsDto.deposit == null)
          throw new BadRequestException(`The deposit is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == null)
          throw new BadRequestException(`The withdrawal is required at line [${i + 2}].`);
        if (createEsDto.withdrawal == 0 && createEsDto.deposit == 0)
          continue;
          // throw new BadRequestException(`The both Withdrawal and Deposit should not be zero at line [${i + 2}].`);
        if (createEsDto.withdrawal != 0 && createEsDto.deposit != 0)
          throw new BadRequestException(`Either Withdrawal or Deposit should be zero at line [${i + 2}].`);
        // set balance and br_type
        let isRefund, isTransfer = false;
        for (let key of EsCsvDesKeyForRefund) {
          if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
            isRefund = true;
            break;
          }
        }
        // for (let key of EsCsvDesKeyForTransfer) {
        //   if (createEsDto.description.toUpperCase().indexOf(key.toUpperCase()) != -1) {
        //     isTransfer = true;
        //     break;
        //   }
        // }
        // set br_type
        if (isTransfer) {
          createEsDto.br_type = parseInt(BkTypeEnum.FT);
        }else if (isRefund) {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.PR) : parseInt(BkTypeEnum.SR)
        }else {
          createEsDto.br_type = createEsDto.deposit != 0.00 ? parseInt(BkTypeEnum.RS) : parseInt(BkTypeEnum.RP);
        }
        createEsDto.report_type = EnumUtil.getEnumKey(createEsDto.br_type.toString(), BkTypeEnum);
        // set balance
        createEsDto.balance = createEsDto.deposit != 0.00 ? createEsDto.deposit : createEsDto.withdrawal;

        createEsDtoList.push(createEsDto);
      } catch (e) {
        this.logger.error('[INSERT][ES]', '-[fileName]-' + fileUploadDto.file_name + '-[withdrawal]-' + createEsDto.withdrawal + '-[deposit]-' + createEsDto.deposit);
        throw new HttpException(e.response, e.status);
      }
    }
    return createEsDtoList;
  }

  async identifyInvoiceFileByFileId(id: string, param: any) {
    if (!param.companyCode || param.companyCode.length == 0) throw new BadRequestException( 'The company code is required.');
    // valid file status
    let fileDto = await this.fileUploadRepository.findOne({where: {id: parseInt(id), company_code: param.companyCode, is_identified: 0, file_type: In([FileTypeEnum.SALES_NOT_PAID, FileTypeEnum.SALES_CASH, FileTypeEnum.PURCHASE_NOT_PAID, FileTypeEnum.PURCHASE_CASH])}});
    if (!fileDto) throw new BadRequestException( 'The file is not exist or it has been identified.');
    if (!fileDto.file_url || fileDto.file_url.length == 0) throw new BadRequestException( 'The file url is not exist.');

    // ocr login
    const ocrToken = await this.ocrUtilsService.ocrLogin();

    if (!fileDto.ocr_transaction_id || fileDto.ocr_transaction_id.length == 0) {
      // upload file to ABBYY if no transaction_id
      fileDto = await this.uploadFileToOcr(fileDto, ocrToken);
    }

    return await this.ocrIdentify(fileDto, ocrToken);
  }

  async ocrUpload(files: Express.Multer.File, ocrToken: string): Promise<any> {
    // ocr login
    if (!ocrToken) {
      ocrToken = await this.ocrUtilsService.ocrLogin();
    }
    // ocr upload file
    const res = await this.ocrUtilsService.ocrPostUpload(files, ocrToken);
    console.log("ocrUploadRes:", JSON.stringify(res));
    // return transaction id
    return res;
  }

  async ocrIdentify(fileDto: FileUpload, ocrToken: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {
        // ocr login
        if (!ocrToken) {
          ocrToken = await this.ocrUtilsService.ocrLogin();
        }
        // ocr download data and save to database
        const ocrFileId = await this.ocrUtilsService.ocrIdentify(fileDto.ocr_transaction_id, ocrToken);
        // ocr file data
        const ocrInvoiceDto: OcrInvoice = await this.ocrUtilsService.ocrGetDataByFileId(fileDto, ocrFileId, ocrToken);
        this.logger.log('ocrInvoiceDto', JSON.stringify(ocrInvoiceDto));
        // save ocr invoice data
        // await this.ocrInvoiceService.createOcrInvoice(ocrInvoiceDto);
        await this.ocrInvoiceService.createOcrInvoiceByUploadFile(ocrInvoiceDto, queryRunner);
        // update identify status from file upload
        // await this.update(fileDto.id, { is_identified: 1 });
        await queryRunner.manager.update<FileUpload>(FileUpload, fileDto.id, { is_identified: 1 });

        // commit transaction
        await queryRunner.commitTransaction();
    }catch (e) {
      await queryRunner.rollbackTransaction();
      throw new HttpException(e, e.status);
    } finally {
      await queryRunner.release();
    }
  }

  async uploadWithInvoice(id: String, jsonStr: String, files: Express.Multer.File[]) {
    // format jsonStr
    const jsonStrFormat = JSON.parse(JSON.stringify(jsonStr)).jsonStr;
    // convert to object array
    const jsonArray = JSON.parse(jsonStrFormat);
    this.logger.log('list', jsonArray);

    // error list for return
    let errorList: string[] = [];
    let fileDto;

    // validate
    if (jsonArray.length != 1 || files.length != 1)
      throw new BadRequestException('Files or jsonStr number should be only one');

    const apInvoice = await this.apRepository.findOne({where: {id: Number(id)}});
    if (apInvoice.br_flag == BrStatusEnum.REVERSED)
      throw new BadRequestException('This invoice has been reversed and can not upload original document again.');

    for (let i = 0; i < files.length; i++) {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const file = files[i];
        const str = jsonArray[i];
        let createDto: FileUpload = new FileUpload();
        createDto = Object.assign(createDto, str);

        // validate jsonStr
        if (!createDto.company_code || !createDto.file_type || !createDto.file_name || !createDto.creator)
          throw new Error('company_code or file_type or file_name or creator is required');
        if (createDto.file_type != FileTypeEnum.PURCHASE_NOT_PAID && createDto.file_type != FileTypeEnum.PURCHASE_CASH)
          throw new Error('file_type is required only for purchase');

        // generate md5 signature
        const readStream = file.buffer.toString();
        // checksum
        const md5sum = createHash('md5')
        md5sum.update(readStream);
        createDto.md5 = md5sum.digest('hex');
        this.logger.log('---md5 string---', createDto.md5);

        // validate md5 signature if it is unique in database
        let fileListFromDatabase: FileUpload[];
        // validation for upload automatically
        fileListFromDatabase = await this.findAll({company_code: createDto.company_code, md5: createDto.md5});
        if (fileListFromDatabase.length > 0)
          throw new Error(fileListFromDatabase[0].id + ' --- it has already been exist, please check first.')

        // upload file to server
        const fileName = `${createDto.company_code}_${Date.now()}_${createDto.md5}${file.originalname.substring(file.originalname.lastIndexOf('.'))}`;
        const filePath = this.apPatch;
        createDto.file_url = `${filePath}/${fileName}`;
        this.logger.log('upload file url: ', join(process.env.FILE_PATH, filePath, fileName));
        let writeStream = createWriteStream(join(process.env.FILE_PATH, filePath, fileName));
        writeStream.write(file.buffer);
        writeStream.close();

        // set invoice_created_status
        createDto.invoice_created_status = FileStatusEnum.GENERATED;
        // save file data to database
        fileDto = await queryRunner.manager.save<FileUpload>(createDto);

        // update invoice file info and ex-previous original document status
        if (apInvoice.file_id) {
          await queryRunner.manager.update<FileUpload>(FileUpload, apInvoice.file_id, {invoice_created_status: FileStatusEnum.NOT_GENERATED});
        }
        await queryRunner.manager.update<Ap>(Ap, apInvoice.id, {file_id: fileDto.id, file_url: fileDto.file_url});

        // commit transaction
        await queryRunner.commitTransaction();
      }
      catch (e) {
        errorList.push(e.message ? '[' + files[i].originalname + ': ' + e.message + ']' : '[' + files[i].originalname + ': ' + ' it is failed to upload]');
        await queryRunner.rollbackTransaction();
        continue;
      } finally {
        await queryRunner.release();
      }
    }
    // handle error
    if (errorList.length > 0) {
      throw new BadRequestException(errorList.toString());
    }
    return fileDto;
  }

  async uploadFileToOcr(uploadFileDetail: FileUpload, ocrToken: string) {
    try {
      if (uploadFileDetail.ocr_transaction_id)
        throw new BadRequestException('File has been uploaded to ABBYY.')
      if (!uploadFileDetail.file_url)
        throw new BadRequestException('file_url is not exist.');
      if (uploadFileDetail.file_type != FileTypeEnum.PURCHASE_NOT_PAID &&
          uploadFileDetail.file_type != FileTypeEnum.PURCHASE_CASH)
        throw new BadRequestException(`file_type[${uploadFileDetail.file_type}] is not correct.`);

      const fileUrl = this.configService.get('UPLOAD_FILE_PATH') + uploadFileDetail.file_url;

      const fileRes = await lastValueFrom(this.httpService.get(fileUrl, {responseType: 'arraybuffer'})
          .pipe(
              tap(response => {

              }),
              map(response  => this.toMulterFile(uploadFileDetail, response.data)),
              catchError(e => {
                e.response.message = '[Get file from server Error] ' + JSON.stringify(e.response.data.errors);
                throw new HttpException(e.response, e.response.status);
              })
          ));

      // upload ocr data to abbyy if it is AR/AP
      const ocrUploadRes = await this.ocrUpload(fileRes, ocrToken);
      if (!ocrUploadRes) throw new BadRequestException( 'The file ocr upload error.');
      return await this.update(uploadFileDetail.id, {ocr_transaction_id: ocrUploadRes.transactionId});
    }catch (e) {
      throw new HttpException(e, e.status);
    }

  }

  private toMulterFile(fileDetail: FileUpload, dataStream: any): Express.Multer.File {
    // 自定义 MulterFile 类型
    const multerFile: Express.Multer.File = {
      buffer: Buffer.from(dataStream),
      destination: '',
      filename: fileDetail.file_name,
      fieldname: '',
      originalname: fileDetail.file_name,
      path: '',
      encoding: '7bit',
      mimetype: 'application/octet-stream',
      size: Buffer.from(dataStream).length, // 填写文件大小
      stream: dataStream,
    };

    // 在这里进行其他操作，如获取文件大小并填写到 multerFile.size 字段中
    // 也可以在这里进行文件写入操作，将数据流写入到具体文件中

    return multerFile;
  }

  async updateComment(id: number, param: any) {
    return await this.dataSource.createQueryBuilder()
        .update(FileUpload)
        .set({comment: param.comment})
        .where("id = :id", {id: id})
        .execute()
  }

  async updateXmlStatus(id: number, param: any) {
    if (param.xml_status !== 'PENDING' && param.xml_status !== 'SUCCESS' && param.xml_status !== 'FAILURE') {
      throw new BadRequestException('XML status error');
    }
    return await this.dataSource.createQueryBuilder()
        .update(FileUpload)
        .set({xml_status: param.xml_status})
        .where("id = :id", {id: id})
        .execute()
  }

  async updateOcrStatus(id: number, param: any) {
    if (param.is_identified !== 1 && param.is_identified !== 0) {
      throw new BadRequestException('XML status error');
    }
    return await this.dataSource.createQueryBuilder()
        .update(FileUpload)
        .set({is_identified: param.is_identified})
        .where("id = :id", {id: id})
        .execute()
  }

  detectCsvHeaderAndDataForBaseinet(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
    let csvHeaderDataRes = new CsvHeaderDataDetector();
    csvHeaderDataRes.header = [];
    csvHeaderDataRes.data = [];
    for (let i = 0; i < csvData.length; i++) {
      let itemList: Array<string> = csvData[i];
      if (csvHeaderDataRes.header.length == 0 && i == 9) {
        csvHeaderDataRes.header = itemList;
      }else if (csvHeaderDataRes.header.length > 0) {
        // 过滤空行，无关键数据行
        const dateStrFromCsv: string = itemList[1].replace(/\t/g, '').trim();
        const descriptionFromCsv: string = itemList[2].replace(/\t/g, '').trim();
        const withdrawFromCsv: string = itemList[5].trim();
        const depositFromCsv: string = itemList[6].trim();
        if (dateStrFromCsv && descriptionFromCsv && (withdrawFromCsv || depositFromCsv)) {
          csvHeaderDataRes.data.push(itemList);
        }
      }
    }
    return csvHeaderDataRes;
  }

  detectCsvHeaderAndDataForMonex(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
    let csvHeaderDataRes = new CsvHeaderDataDetector();
    csvHeaderDataRes.header = [];
    csvHeaderDataRes.data = [];
    for (let i = 0; i < csvData.length; i++) {
      let itemList: Array<string> = csvData[i];
      if (csvHeaderDataRes.header.length == 0 && i == 1) {
        csvHeaderDataRes.header = itemList;
      }else if (csvHeaderDataRes.header.length > 0) {
        // 过滤空行，无关键数据行
        const dateStrFromCsv: string = itemList[1].replace(/\t/g, '').trim();
        const descriptionFromCsv: string = itemList[3].replace(/\t/g, '').trim();
        const withdrawDepositFromCsv: string = itemList[10].trim();
        if (dateStrFromCsv && descriptionFromCsv && withdrawDepositFromCsv) {
          csvHeaderDataRes.data.push(itemList);
        }
      }
    }
    return csvHeaderDataRes;
  }

  detectCsvHeaderAndDataForBbva(csvData: string[][], keywords: string[], minColumns = 3): CsvHeaderDataDetector {
    for (let i = 0; i < csvData.length; i++) {
      const row = csvData[i];
      if (row.length < minColumns) continue;

      const score = row.filter(cell =>
          keywords.some(keyword => cell.toLowerCase().includes(keyword)),
      ).length;

      if (score >= 6) {
        let csvHeaderDataRes = new CsvHeaderDataDetector();
        // 获取标题行
        csvHeaderDataRes.header = row;
        // 获取后续数据行
        csvHeaderDataRes.data = csvData.slice(i + 1);
        return csvHeaderDataRes;
      }
    }
    throw new Error('No CSV header or data found.');
  }

  private isCheckingAccountFormat(csvData: string[][]): boolean {
    // 支票账户特征判断
    if (csvData.length === 0) return false;
    // const requiredHeaders = ['account', 'date'];
    // const firstRow = csvData[0].map(item => item.trim().toLowerCase());
    // // 检查所有必需的表头是否都存在
    // return requiredHeaders.every(header => firstRow.includes(header));
    return csvData[0].length > 10;
  }

  private isCreditCardFormat(csvData: string[][]): boolean {
    // 信用卡账户特征判断
    if (csvData.length === 0) return false;
    // const requiredHeaders = ['credit card', 'transaction date'];
    // const firstRow = csvData[0].map(item => item.trim().toLowerCase());
    // // 检查所有必需的表头是否都存在
    // return requiredHeaders.every(header => firstRow.includes(header));
    return csvData[0].length == 10;
  }

  private processCheckingAccount(csvData: string[][]): CsvHeaderDataDetector {
    let csvHeaderDataRes = new CsvHeaderDataDetector();
    csvHeaderDataRes.header = [];
    csvHeaderDataRes.data = [];
    for (let i = 0; i < csvData.length; i++) {
      let itemList: Array<string> = csvData[i];
      if (csvHeaderDataRes.header.length == 0 && i == 0) {
        csvHeaderDataRes.header = itemList;
      }else if (csvHeaderDataRes.header.length > 0) {
        // 过滤空行，无关键数据行
        const dateStrFromCsv: string = itemList[1].replace(/\t/g, '').trim();
        const descriptionFromCsv: string = itemList[9].replace(/\t/g, '').trim();
        const withdrawDepositFromCsv: string = itemList[6].trim();
        const signal: string = itemList[5].trim();
        if (dateStrFromCsv && descriptionFromCsv && withdrawDepositFromCsv && signal) {
          csvHeaderDataRes.data.push(itemList);
        }
      }
    }
    return csvHeaderDataRes;
  }

  private processCreditCardAccount(csvData: string[][]): CsvHeaderDataDetector {
    let csvHeaderDataRes = new CsvHeaderDataDetector();
    csvHeaderDataRes.header = this.standardizeCreditCardHeaders(csvData[0]);
    csvHeaderDataRes.data = this.standardizeCreditCardData(csvData.slice(0 + 1));
    return csvHeaderDataRes;
  }

  private standardizeCreditCardHeaders(headers: string[]): string[] {
    // 将信用卡账单的表头标准化为统一格式
    return headers.map(header => {
      const h = header.trim().toLowerCase();
      switch (h) {
        case 'credit card':
          return 'account';
        case 'transaction date':
          return 'date';
        case 'sign':
          return 'deposits/position'
        default:
          return header;
      }
    });
  }

  private standardizeCreditCardData(data: string[][]): string[][] {
    // 将信用卡账单的数据标准化为统一格式
    // 处理每一行数据
    return data.map(row => {
      // 对每行的列进行处理
      return row.map((cell, columnIndex) => {
        // 日期列 (columnIndex === 1)
        if (columnIndex === 1 && cell) {
          let dateStr = cell.trim();
          if (dateStr.length === 5) {
            dateStr = '0' + dateStr;
          }
          if (dateStr.length === 6) {
            // 从 'MMDDYY' 格式转换为 'YYYY-MM-DD' 格式
            const month = dateStr.substring(0, 2);
            const day = dateStr.substring(2, 4);
            const year = '20' + dateStr.substring(4, 6);
            return `${day}${month}${year}`;
          }
        }
        // sign符号列处理 (columnIndex === 5)
        if (columnIndex === 5 && cell && cell.trim()) {
          const sign = cell.trim();
          if (sign === '+') {
            return '-';
          } else if (sign === '-') {
            return '+';
          }
        }
        return cell;
      });
    });
  }

}
