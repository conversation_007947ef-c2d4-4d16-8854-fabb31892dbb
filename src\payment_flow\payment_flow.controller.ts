import {Controller, Get, Post, Body, Patch, Param, Delete, Query} from '@nestjs/common';
import { PaymentFlowService } from './payment_flow.service';
import { CreatePaymentFlowDto } from './dto/create-payment_flow.dto';
import { UpdatePaymentFlowDto } from './dto/update-payment_flow.dto';
import {BaseController} from "../common/controller/base.controller";
import {ApprovalFlow} from "../invoice_flow/entities/invoice_flow.entity";
import {PaymentApprovalFlow} from "./entities/payment_flow.entity";
import {ApiTags} from "@nestjs/swagger";
import {CreateInvoiceFlowDto} from "../invoice_flow/dto/create-invoice_flow.dto";

@ApiTags('Payment-flow')
@Controller('payment-flow')
export class PaymentFlowController extends BaseController<PaymentApprovalFlow> {
  constructor(private readonly paymentFlowService: PaymentFlowService) {
    super(paymentFlowService);
  }

  @Post()
  createPaymentFlow(@Body() createPaymentFlowDto: CreatePaymentFlowDto) {
    return this.paymentFlowService.createPaymentFlow(createPaymentFlowDto);
  }

  @Post('batch')
  createPaymentFlowBatch(@Body() createPaymentFlowDtoList: CreatePaymentFlowDto[]) {
    return this.paymentFlowService.createPaymentFlowBatch(createPaymentFlowDtoList);
  }

  @Get()
  findAllPaymentFlow(@Query() queryData: any) {
    return this.paymentFlowService.findAllPaymentFlow(queryData);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.paymentFlowService.findOne(+id);
  }

  @Patch(':id')
  updatePaymentFlow(@Param('id') id: string, @Body() updatePaymentFlowDto: UpdatePaymentFlowDto) {
    return this.paymentFlowService.update(+id, updatePaymentFlowDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.paymentFlowService.remove(+id);
  }
}
