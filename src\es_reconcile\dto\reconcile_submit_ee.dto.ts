import {ApiProperty} from "@nestjs/swagger";
import {IsNotEmpty} from "class-validator";
import {ReconcileSubmitDto} from "./reconcile_submit_sp.dto";

export class ReconcileSubmitEeDto {

    @ApiProperty({type: 'string', description: 'company code', example: '3000'})
    @IsNotEmpty()
    company_code: string;

    @ApiProperty({type: 'number', description: 'E_Statement Id', example: 101})
    @IsNotEmpty()
    statement_id: number;

    @ApiProperty({type: 'number', description: 'reconcile type: 0 AR/RS; 1 AP/RP; 2 PR; 3 SR; 4 FT; 5 PY; 6 FX；8 YED; 9 CP; 10 BC; 11 SP', example: 1})
    @IsNotEmpty()
    br_type: number;

    @ApiProperty({type: 'number', description: 'br entity type: 0 AR ; 1 AP; 7 ES', example: 1})
    @IsNotEmpty()
    br_entity_type: number;

    @ApiProperty({type: 'string', description: 'posting date', example: '2022-11-11'})
    @IsNotEmpty()
    posting_date: string;

    @ApiProperty({type: 'number', description: '1 - CNY; 2 - CAD; 3 - USD', example: 2})
    currency: number;

    @ApiProperty({type: 'string', description: 'bank account', example: '451-90355-********'})
    @IsNotEmpty()
    bank_account: string;

    @ApiProperty({type: 'number', description: 'balance', example: 10553})
    @IsNotEmpty()
    balance: number;

    @ApiProperty({type: 'string', description: 'charge coa', example: "1008-3002-01"})
    charge_coa: string;

    @ApiProperty({type: 'number', description: 'charge fee', example: 0.00})
    charge_fee: number;

    @ApiProperty({type: 'string', description: 'coa account', example: "1008-3002-01"})
    @IsNotEmpty()
    gl_account: string;

    @ApiProperty({type: 'string', description: 'creator', example: '250'})
    @IsNotEmpty()
    creator: string;

    @ApiProperty({type: 'document_no', description: 'document_no of gl', example: 'bbad1dac942a4d7b8f90589408c76bff'})
    @IsNotEmpty()
    document_no: string;

    @ApiProperty({type: 'report_type', description: 'report type for updating es', example: 'CRO, CPO'})
    @IsNotEmpty()
    report_type: string;

    invoice_list: ReconcileSubmitDto.Invoice[];
}