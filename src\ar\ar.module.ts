import {forwardRef, Module} from '@nestjs/common';
import { ArService } from './ar.service';
import { Ar<PERSON>ontroller } from './ar.controller';
import {TypeOrmModule} from "@nestjs/typeorm";
import {Ar} from "./entities/ar.entity";
import {ArItem} from "./entities/ar.item.entity";
import {HttpModule} from "@nestjs/axios";
import {ConfigModule} from "@nestjs/config";
import {EsReconcileModule} from "../es_reconcile/es_reconcile.module";
import {ReconciliationHistory} from "../es_reconcile/entities/reconciliation_history.entity";
import {LogModule} from "../log/log.module";
import {CoaTopInit} from "../coa_top_init/entities/coa_top_init.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Ar, ArItem, CoaTopInit, ReconciliationHistory]), LogModule, HttpModule, ConfigModule, forwardRef(() => EsReconcileModule)],
  controllers: [ArController],
  providers: [ArService],
  exports:[ArService]
})
export class ArModule {}
