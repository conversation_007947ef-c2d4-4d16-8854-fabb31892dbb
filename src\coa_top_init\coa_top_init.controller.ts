import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { CoaTopInitService } from './coa_top_init.service';
import { CreateCoaTopInitDto } from './dto/create-coa_top_init.dto';
import {ApiTags} from "@nestjs/swagger";
import {CoaTopInit} from "./entities/coa_top_init.entity";
import {BaseController} from "../common/controller/base.controller";

@ApiTags('CoaTopInit')
@Controller('coa-top-init')
export class CoaTopInitController extends BaseController<CoaTopInit> {
  constructor(private readonly coaTopInitService: CoaTopInitService) {
    super(coaTopInitService);
  }

  @Post()
  createCoATopInit(@Body() createCoaTopInitDto: CreateCoaTopInitDto) {
    return this.coaTopInitService.createCoaTopInit(createCoaTopInitDto);
  }

  // @Get()
  // findAll() {
  //   return this.coaTopInitService.findAll();
  // }
  //
  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.coaTopInitService.findOne(+id);
  // }
  //
  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateCoaTopInitDto: UpdateCoaTopInitDto) {
  //   return this.coaTopInitService.update(+id, updateCoaTopInitDto);
  // }
  //
  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.coaTopInitService.remove(+id);
  // }
}
