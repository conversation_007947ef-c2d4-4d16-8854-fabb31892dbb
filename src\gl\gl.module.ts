import { Module } from '@nestjs/common';
import { GlService } from './gl.service';
import { GlController } from './gl.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Gl } from './entities/gl.entity';
import {GlItem} from "./entities/gl.item.entity";

@Module({
  imports: [TypeOrmModule.forFeature([Gl, GlItem])],
  controllers: [GlController],
  providers: [GlService]
})
export class GlModule {}
